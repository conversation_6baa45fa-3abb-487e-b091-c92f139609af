import { convertNumber, stringForNumber } from './convertNumber';

export function validateValue(arr: any, value: any): boolean {
  if (Array.isArray(arr) && arr.length > 0) {
    const validation = arr[0];

    if (!value) {
      return true;
    }

    switch (validation.type) {
      case 'list':
        if (validation.value.includes(stringForNumber(value))) {
          return true;
        }
        return false;

      case 'range':
        if (
          stringForNumber(value) >= convertNumber(validation.min) &&
          stringForNumber(value) <= convertNumber(validation.max)
        ) {
          return true;
        }
        return false;

      case 'unique':
        if (stringForNumber(value) === convertNumber(validation.value)) {
          return true;
        }
        return false;

      default:
        return false;
    }
  }

  return true;
}

export const validationArrayMessage = (validationArray: any, setState: any, language: string): void => {
  if (validationArray?.length > 0) {
    if (validationArray[0].type === 'list') {
      setState(
        language === 'USA'
          ? `The entered value must equal ${validationArray[0].value.join(', ')}.`
          : `O valor digitado deve ser igual a ${validationArray[0].value.join(', ')}.`
      );
    }
    if (validationArray[0].type === 'range') {
      setState(
        language === 'USA'
          ? `The value entered must be between ${validationArray[0].min} and ${validationArray[0].max}.`
          : `O valor digitado deve estar entre ${validationArray[0].min} e ${validationArray[0].max}.`
      );
    }
    if (validationArray[0].type === 'unique') {
      setState(
        language === 'USA'
          ? `The entered value must equal ${validationArray[0].value}.`
          : `O valor digitado deve ser igual a ${validationArray[0].value}.`
      );
    }
  }
};
