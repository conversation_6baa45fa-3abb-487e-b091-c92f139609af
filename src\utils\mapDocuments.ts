import { format, utcToZonedTime } from 'date-fns-tz';

export function mapDocuments(documents: any[], t: any, filterCertificate?: any): any {
  return documents.map((doc: any) => ({
    expire_at: doc?.expire_at ? format(utcToZonedTime(doc?.expire_at, 'America/Sao_Paulo'), t('format.date')) : '',
    type: doc.type,
    docName: doc.document_name,
    download: doc.document_path,
    products: doc.products,
    certificate:
      t('language') === 'USA'
        ? filterCertificate?.certificate?.description
        : filterCertificate?.certificate?.description_ptbr,
    code_certificate: filterCertificate?.certificate_code,
    name: filterCertificate?.name,
    address: filterCertificate?.address,
    uuidDocument: doc?.uuid,
    uuid: filterCertificate?.uuid,
    secure_download_link: doc?.secure_download_link,
  }));
}
