import React, { <PERSON> } from "react";
import './styles.scss';

interface ImagesProps {
  data: Slide[],
  onClick: (index: number) => void;
}

interface Slide {
  src: string;
  title?: string;
}

const Images:FC<ImagesProps> = (props) => {
  const { data, onClick } = props;

  const handlerClickImage = (index: number): void => {
    onClick(index);
  }

  return (
    <div className="images-container">
      {data.map((slide, index) => (
        <div role="button" tabIndex={0} onClick={() => handlerClickImage(index)} key={index} className="image"
          onKeyDown={(event) => {
            if (event.key === 'Enter' || event.key === ' ') {
              handlerClickImage(index);
            }
          }}
        >
          <img src={slide.src} alt={slide.title}/>
        </div>
      ))}
    </div>
  );
}

export default Images;
