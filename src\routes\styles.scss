.containerModalPermission {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2.625rem;
  h3 {
    padding-left: 20px;
    padding-right: 20px;
    color: var(--basics-secondary, #201e40);
    text-align: center;
    font-size: 1.5rem;
    font-family: Poppins;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }
  p {
    padding-left: 50px;
    padding-right: 50px;
    color: var(--basics-secondary, #201e40);
    text-align: center;
    font-size: 1rem;
    font-family: Poppins;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
  .colButtons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.2rem;
    padding-left: 2rem;
    span {
      padding-right: 14px;
    }
  }
}
.modal.show .modal-dialog {
  transform: none;
  display: flex;
  height: 90%;
  align-items: center;
}

.dialog {
  position: fixed;
  top: 6rem;
  left: 0;
  left: 47%;
  transform: translateX(-47%);
  width: 47%;
  max-width: 900px;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 999;
}

.content {
  margin-top: 200px;
}

.alignItemsDialog {
  h5 {
    padding-top: 0.6rem;
    color: #132632;
    font-size: 1.2rem;
    font-family: Lato;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
  span {
    text-decoration-line: underline;
    font-size: 1.2rem;
    font-family: Lato;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #18b680;
    cursor: pointer;
  }
  img {
    margin-right: 5px;
    min-height: 1.4rem;
  }
}
