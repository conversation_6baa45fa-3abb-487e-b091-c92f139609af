import React from 'react';
import Select from 'react-select';
import { Col } from 'react-bootstrap';
import Text from '../../Text';
import './styles.scss';

interface ISelect {
  onChange?: (event: any) => void;
  isMulti?: boolean;
  onKeyUp?: any;
  isInvalid?: boolean;
  cy?: string;
  msg?: string;
  getOptionLabel?: any;
  children?: React.ReactElement;
  getOptionValue?: any;
  value?: any;
  title?: string;
  placeholder?: string;
  options?: any;
  isDisabled?: boolean;
  renderKey?: boolean;
  id?: any;
}

const SearchSelect = ({
  value,
  getOptionLabel,
  getOptionValue,
  options,
  onChange,
  isMulti = false,
  onKeyUp,
  isInvalid,
  isDisabled,
  cy,
  msg,
  children,
  title,
  renderKey = false,
  placeholder,
  id,
}: ISelect): React.ReactElement => (
  <Col onKeyUp={onKeyUp} className={`select ${isInvalid && 'select-error'} ${isMulti && 'select-multi'}`} data-cy={cy}>
    {children ? (
      <div className="d-flex gap-2 align-items-center justify-content-between">
        <Text as="span" className={`form-label d-block ${isInvalid && 'select-error__title'}`}>
          {title}
        </Text>
        {children}
      </div>
    ) : (
      <Text as="span" className={`form-label d-block ${isInvalid && 'select-error__title'}`}>
        {title}
      </Text>
    )}
    {isDisabled ? (
      <div className="selectDisabled">{value && value.label}</div>
    ) : (
      <Select
        isDisabled={isDisabled}
        key={renderKey ? id : null}
        value={value}
        formatOptionLabel={getOptionLabel}
        defaultInputValue={getOptionValue}
        options={options}
        isClearable={!isMulti}
        onChange={onChange}
        placeholder={placeholder}
      />
    )}
    {isInvalid && (
      <Text as="span" className="select-error__invalid-feedback">
        {msg}
      </Text>
    )}
  </Col>
);

export default SearchSelect;
