.login {
  height: 70vh;
  padding: 2rem;

  @media (max-width: 1440px) {
    max-width: 76rem;
  }
  @media (max-width: 1140px) {
    max-width: 52rem;
  }
  @media (max-width: 940px) {
    max-width: 36rem;
  }

  @media (max-width: 768px) {
    height: 80vh;
  }
  .box-login {
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 640px;
    padding-top: 7rem;
    @media (max-width: 768px) {
      padding-left: 0.5rem;
    }

    .card {
      width: 100%;
      border-radius: 8px;

      .card-body {
        padding: 2.5rem 1.75rem 2rem 2rem;
        width: 100%;

        .logo {
          padding: 1.75rem 0;
        }
        .btnLogin {
          min-height: 3rem;
          border-radius: 8px;
          min-width: 130px;
        }

        .labelLogin {
          padding-left: 0.225rem;
          font-family: 'Poppins';
          font-weight: 700;
          width: 100%;
        }
        .titleLogin {
          font-family: 'Poppins';
          font-style: normal;
          font-weight: 700;
          font-size: 48px;
          line-height: 72px;
          color: #201e40;
        }
        .notAccountText {
          font-family: 'Poppins';
          font-style: normal;
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          color: #201e40;
        }
        .linkCreateAccount {
          color: #201e40;
          font-weight: 700;
        }

        form {
          button {
            background-color: var(--green-400);
            border: 0;
            transition: all 0.2s;

            &:hover {
              background-color: var(--green-800);
            }
          }
        }
      }
    }
  }

  a {
    color: #64a4ec;
    cursor: pointer;
  }
}
