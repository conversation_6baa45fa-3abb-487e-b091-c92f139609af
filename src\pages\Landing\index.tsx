import React from 'react';
import { Container, <PERSON>, <PERSON>, Button } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import Section from '../../components/Section';
import './styles.scss';

// Importar imagens existentes
import ForestImage from '../../statics/home/<USER>';

const Landing: React.FC = () => {
  const { t } = useTranslation();
  const history = useHistory();

  const handleQueroVender = () => {
    history.push('/newUserForm');
  };

  return (
    <Section title="WoodFlow - Landing" description="Landing page WoodFlow EUDR" className="landing-page">
      {/* Hero Section */}
      <div className="hero-section">
        <Container>
          <Row className="align-items-center h-100">
            <Col lg={6} className="hero-content">
              <div className="hero-text">
                <h1 className="hero-title">
                  {t('landing.heroTitle').split('MUNDO!')[0]}
                  <span className="highlight"> EXPORTE PARA O MUNDO!</span>
                </h1>
                <p className="hero-subtitle" style={{ color: '#201e40' }}>
                  {t('landing.heroSubtitle')}
                </p>
                <div className="hero-cta-section">
                  <Button className="cta-button" size="lg" onClick={handleQueroVender}>
                    {t('landing.ctaButton')}
                  </Button>
                </div>
              </div>
            </Col>
            <Col lg={6} className="hero-image">
              <div className="wood-stack-image">
                {/* Placeholder para imagem de madeira empilhada */}
                <div className="wood-visual"></div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      {/* Facilidade e Segurança Section */}
      <div className="features-section">
        <Container>
          <Row className="text-center mb-5">
            <Col>
              <h2 className="section-title">
                {t('landing.featuresTitle').split('madeira brasileira')[0]} <br />
                <span className="highlight-green">madeira Brasileira</span>
              </h2>
              <p className="section-subtitle">{t('landing.featuresSubtitle')}</p>
            </Col>
          </Row>
        </Container>
      </div>

      {/* EUDR Information Section */}
      <div className="eudr-section">
        <Container>
          <Row className="align-items-center">
            <Col lg={6}>
              <div className="eudr-content">
                <h2 className="eudr-title">
                  {t('landing.eudrTitle').split('EUDR')[0]}
                  <span className="highlight-green">EUDR</span>
                </h2>
                <p className="eudr-description">{t('landing.eudrDescription')}</p>
                <div className="eudr-benefits">
                  <div className="benefit-item">
                    <span className="benefit-icon">✓</span>
                    <span>{t('landing.eudrBenefit1')}</span>
                  </div>
                  <div className="benefit-item">
                    <span className="benefit-icon">✓</span>
                    <span>{t('landing.eudrBenefit2')}</span>
                  </div>
                  <div className="benefit-item">
                    <span className="benefit-icon">✓</span>
                    <span>{t('landing.eudrBenefit3')}</span>
                  </div>
                  <div className="benefit-item">
                    <span className="benefit-icon">✓</span>
                    <span>{t('landing.eudrBenefit4')}</span>
                  </div>
                  <div className="benefit-item">
                    <span className="benefit-icon">✓</span>
                    <span>{t('landing.eudrBenefit5')}</span>
                  </div>
                </div>
              </div>
            </Col>
            <Col lg={6}>
              <img src={ForestImage} alt={t('landing.madeiraLegal')} className="img-fluid" />
            </Col>
          </Row>
        </Container>
      </div>

      {/* Documentos Obrigatórios Section */}
      <div className="documents-section">
        <Container>
          <Row className="text-center mb-5">
            <Col>
              <h2 className="section-title">{t('landing.documentosObrigatorios')}</h2>
              <p className="section-subtitle">{t('landing.todosDocumentos')}</p>
            </Col>
          </Row>
          <Row>
            <Col lg={4} md={6} className="mb-4">
              <div className="document-card">
                <div className="document-icon">📋</div>
                <h4>{t('landing.documentacaoTrabalhista')}</h4>
                <p>{t('landing.documentacaoTrabalhistaDescription')}</p>
              </div>
            </Col>
            <Col lg={4} md={6} className="mb-4">
              <div className="document-card">
                <div className="document-icon">🌳</div>
                <h4>{t('landing.licencaIbama')}</h4>
                <p>{t('landing.licencaIbamaDescription')}</p>
              </div>
            </Col>
            <Col lg={4} md={6} className="mb-4">
              <div className="document-card">
                <div className="document-icon">✅</div>
                <h4>{t('landing.certificacaoFcs')}</h4>
                <p>{t('landing.certificacaoFcsDescription')}</p>
              </div>
            </Col>
            <Col lg={4} md={6} className="mb-4">
              <div className="document-card">
                <div className="document-icon">📍</div>
                <h4>{t('landing.coordenadasGeograficas')}</h4>
                <p>{t('landing.coordenadasGeograficasDescription')}</p>
              </div>
            </Col>
            <Col lg={4} md={6} className="mb-4">
              <div className="document-card">
                <div className="document-icon">🔗</div>
                <h4>{t('landing.cadeiaCustodia')}</h4>
                <p>{t('landing.cadeiaCustodiaDescription')}</p>
              </div>
            </Col>
            <Col lg={4} md={6} className="mb-4">
              <div className="document-card">
                <div className="document-icon">⚖️</div>
                <h4>{t('landing.conformidadeLegal')}</h4>
                <p>{t('landing.conformidadeLegalDescription')}</p>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      {/* Importância dos Documentos Section */}
      <div className="importance-section">
        <Container>
          <Row className="text-center mb-5">
            <Col>
              <h2 className="section-title">{t('landing.documentacaoCorreta')}</h2>
              <p className="section-subtitle">{t('landing.documentacaoCorretaDescription')}</p>
            </Col>
          </Row>
          <Row className="align-items-center">
            <Col lg={6}>
              <div className="importance-content">
                <div className="importance-item">
                  <div className="importance-number">01</div>
                  <div className="importance-text">
                    <h4>{t('landing.eviteBloqueio')}</h4>
                    <p>{t('landing.eviteBloqueioDescription')}</p>
                  </div>
                </div>
                <div className="importance-item">
                  <div className="importance-number">02</div>
                  <div className="importance-text">
                    <h4>{t('landing.garanteAcesso')}</h4>
                    <p>{t('landing.garanteAcessoDescription')}</p>
                  </div>
                </div>
                <div className="importance-item">
                  <div className="importance-number">03</div>
                  <div className="importance-text">
                    <h4>{t('landing.protegeSancoes')}</h4>
                    <p>{t('landing.protegeSancoesDescription')}</p>
                  </div>
                </div>
                <div className="importance-item">
                  <div className="importance-number">04</div>
                  <div className="importance-text">
                    <h4>{t('landing.comprovaOrigemLegal')}</h4>
                    <p>{t('landing.comprovaOrigemLegalDescription')}</p>
                  </div>
                </div>
                <div className="importance-item">
                  <div className="importance-number">05</div>
                  <div className="importance-text">
                    <h4>{t('landing.auditoriasSurpresa')}</h4>
                    <p>{t('landing.auditoriasSurpresaDescription')}</p>
                  </div>
                </div>
              </div>
            </Col>
            <Col lg={6}>
              <div className="importance-visual">
                <div className="stats-container">
                  <div className="stat-item">
                    <div className="stat-number">73%</div>
                    <div className="stat-label">{t('landing.evitamMarcas')}</div>
                  </div>
                  <div className="stat-item">
                    <div className="stat-number">40%</div>
                    <div className="stat-label">{t('landing.auditorias')}</div>
                  </div>
                  <div className="stat-item">
                    <div className="stat-number">38%</div>
                    <div className="stat-label">{t('landing.naoConformes')}</div>
                  </div>
                  <div className="stat-item">
                    <div className="stat-number">100%</div>
                    <div className="stat-label">{t('landing.conformidade')}</div>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </Section>
  );
};

export default Landing;
