import { ProductComplete } from '../interfaces';
import { Links, Meta } from '../interfaces/IMeta';
import HttpClient from './httpClient';

export interface IProductSelectsDetail {
  id: number;
  description: string;
  description_ptbr: string;
}

export interface IProductSelects {
  data: IProductSelectsDetail[];
  links: Links;
  meta: Meta;
}

export interface Type {
  id: number;
  description: string;
  description_ptbr: string;
}

export interface Specie {
  id: number;
  description: string;
  description_ptbr: string;
}

export interface Quality {
  id: number;
  description: string;
  description_ptbr: string;
}

export interface Purpose {
  id: number;
  description: string;
  description_ptbr: string;
}

export interface Glue {
  id: number;
  description: string;
  description_ptbr: string;
}

export interface ResponseProduct {
  data: ProductComplete[];
  links: Links;
  meta: Meta;
}

export interface IPropsSaveProduct {
  company_id: number | Array<number>;
  type_id: number;
  specie_id: number;
  quality_id: number;
  purpose_id: number;
  glue_id: number;
  description: string;
  description_ptbr: string;
  certificates: any;
  thickness: number;
  length: number;
  width: number;
  layers: number;
}

export interface ProjectById {
  data: ProductComplete;
}

class ProductService {
  static async listTypes(description: string): Promise<IProductSelects> {
    const { data } = await HttpClient.api.get<IProductSelects>(
      `/product/types?page=${1}&perPage=${20}&description=${description}`
    );

    return data;
  }

  static async newProduct(product: IPropsSaveProduct): Promise<void> {
    const { data } = await HttpClient.api.post(`/products`, product);

    return data;
  }

  static async putProduct(product: IPropsSaveProduct, id: string): Promise<void> {
    const { data } = await HttpClient.api.put(`/products/${id}`, product);

    return data;
  }

  static async listPurposes(
    description: string,
    type?: string | number | boolean | object | undefined
  ): Promise<IProductSelects> {
    const filterType = type ? `&type=${type}` : '';
    const { data } = await HttpClient.api.get<IProductSelects>(
      `/product/purposes?page=${1}&perPage=${20}&description=${description}${filterType}`
    );

    return data;
  }

  static async listFinishies(
    description: string,
    type?: string | number | boolean | object | undefined
  ): Promise<IProductSelects> {
    const filterType = type ? `&type=${type}` : '';

    const { data } = await HttpClient.api.get<IProductSelects>(
      `/product/finishes?page=${1}&perPage=${200}&description=${description}${filterType}`
    );

    return data;
  }

  static async listGlues(
    description: string,
    type?: string | number | boolean | object | undefined
  ): Promise<IProductSelects> {
    const filterType = type ? `&type=${type}` : '';
    const { data } = await HttpClient.api.get<IProductSelects>(
      `/product/glues?page=${1}&perPage=${20}&description=${description}${filterType}`
    );

    return data;
  }

  static async listQualities(
    description: string,
    type?: string | number | boolean | object | undefined
  ): Promise<IProductSelects> {
    const filterType = type ? `&type=${type}` : '';
    const { data } = await HttpClient.api.get<IProductSelects>(
      `/product/qualities?page=${1}&perPage=${200}&description=${description}${filterType}`
    );

    return data;
  }

  static async listSpecies(
    description: string,
    type?: string | number | boolean | object | undefined
  ): Promise<IProductSelects> {
    const filterType = type ? `&type=${type}` : '';
    const { data } = await HttpClient.api.get<IProductSelects>(
      `/product/species?page=${1}&perPage=${20}&description=${description}${filterType}`
    );

    return data;
  }

  static async getProducts({
    page,
    companyId,
    query,
    perPage,
    visibility,
    thickness,
    width,
    length,
    type_id,
    specie_id,
    quality_id,
    certificates,
    finish_id,
    by_preferences = false,
    order_by = 'created_at',
    sort = 'desc',
  }: any): Promise<ResponseProduct> {
    const { data } = await HttpClient.api.get(
      `/products?company_id=${companyId === undefined ? '' : companyId}&query=${query === undefined ? '' : query
      }&thickness=${thickness === undefined ? '' : thickness}&length=${length === undefined ? '' : length}&width=${width === undefined ? '' : width
      }&type_id=${type_id === undefined ? '' : type_id}&specie_id=${specie_id === undefined ? '' : specie_id
      }&quality_id=${quality_id === undefined ? '' : quality_id}&certificates=${certificates === undefined ? '' : certificates
      }&finish_id=${finish_id === undefined ? '' : finish_id}&page=${page}&perPage=${perPage}&visibility=${visibility === undefined || null ? '' : visibility
      }&order_by=${order_by}&sort=${sort}&by_preferences=${by_preferences}`
    );

    return data;
  }

  static async listCertificates(
    description: string,
    type?: string | number | boolean | object | undefined
  ): Promise<IProductSelects> {
    const filterType = type ? `&type=${type}` : '';
    const { data } = await HttpClient.api.get<IProductSelects>(
      `/product/certificates?page=${1}&perPage=${20}&description=${description}${filterType}`
    );

    return data;
  }

  static async getProductById(id: string): Promise<ProjectById> {
    const { data } = await HttpClient.api.get(`/products/${id}`);

    return data;
  }

  static async deleteProductById(id: string): Promise<ProjectById> {
    const { data } = await HttpClient.api.delete(`/products/${id}`);

    return data;
  }
}

export default ProductService;
