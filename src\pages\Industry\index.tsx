import React from 'react';
import { useTranslation } from 'react-i18next';
import 'react-image-crop/dist/ReactCrop.css';
import './styles.scss';
import CardUploaDocuments from './UploadDocuments';

const Actions: React.FunctionComponent = () => {
  const { t } = useTranslation();

  return (
    <div className="dashboard-container">
      <h1 className="dashboard-title">{t('modal.editIndustry')}</h1>
      <CardUploaDocuments />
    </div>
  );
};

export default Actions;
