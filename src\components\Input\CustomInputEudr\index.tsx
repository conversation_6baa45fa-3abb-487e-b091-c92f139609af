import React, { useState, forwardRef } from 'react';
import { Form, InputGroup } from 'react-bootstrap';
import { HiEye, HiEyeOff } from 'react-icons/hi';
import classNames from 'classnames';
import { IInput } from './interfaces';
import './styles.scss';

const CustomInput = forwardRef<HTMLInputElement, IInput>(
  (
    {
      value,
      placeholder,
      maxLength,
      minLength,
      id,
      label,
      readOnly,
      type,
      required,
      disabled,
      onChange,
      onBlur,
      onKeyDown,
      onKeyPress,
      onKeyUp,
      isInvalid,
      desc,
      msg,
      className,
      tabIndex,
      cy,
      name,
    },
    ref
  ) => {
    const [show, setShow] = useState<boolean>(false);

    if (type === 'password') {
      return (
        <InputGroup>
          <Form.Control
            type={!show && type === 'password' ? type : 'text'}
            value={value}
            onChange={onChange}
            onBlur={onBlur}
            onKeyDown={onKeyDown}
            onKeyPress={onKeyPress}
            onKeyUp={onKeyUp}
            placeholder={placeholder}
            maxLength={maxLength}
            minLength={minLength}
            readOnly={readOnly}
            required={required}
            disabled={disabled}
            isInvalid={isInvalid}
            aria-describedby={desc}
            ref={ref}
            autoComplete="off"
            className={classNames(className)}
            tabIndex={tabIndex}
            data-cy={cy}
            name={name}
          />
          <InputGroup.Text
            role="presentation"
            onClick={() => setShow(!show)}
            id="viewPassword"
            className={`inputs__password bg-transparent border-left-0 ${classNames(
              isInvalid && 'inputs__view-pass-error'
            )}`}
          >
            {show ? <HiEyeOff color="var(--gray-300)" /> : <HiEye color="var(--gray-300)" />}
          </InputGroup.Text>
          <Form.Control.Feedback type="invalid">{msg}</Form.Control.Feedback>
        </InputGroup>
      );
    }

    return (
      <Form.Group className="m-inputs inputs" controlId={id}>
        <label htmlFor={id} className="w-100">
          {label}
        </label>
        <Form.Control
          type={type}
          value={value}
          onChange={onChange}
          onBlur={onBlur}
          onKeyDown={onKeyDown}
          onKeyPress={onKeyPress}
          onKeyUp={onKeyUp}
          placeholder={placeholder}
          maxLength={maxLength}
          minLength={minLength}
          readOnly={readOnly}
          required={required}
          disabled={disabled}
          isInvalid={isInvalid}
          aria-describedby={desc}
          ref={ref}
          autoComplete="off"
          className={classNames(className)}
          tabIndex={tabIndex}
          data-cy={cy}
          name={name}
        />
        <Form.Control.Feedback type="invalid">{msg}</Form.Control.Feedback>
      </Form.Group>
    );
  }
);

CustomInput.displayName = 'CustomInput';

export default CustomInput;
