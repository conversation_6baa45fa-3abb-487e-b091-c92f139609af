import React from 'react';
import { Row, Col } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';

import Card from '../../../../components/Card';
import Select from '../../../../components/Select';
import Textarea from '../../../../components/Textarea';
import Text from '../../../../components/Text';
import './styles.scss';
import RadioButton from '../../../../components/Radio';
import { useAuth } from '../../../../contexts/AuthContext';

const CardIdentifyProduct = ({
  companyId,
  typeProduct,
  values,
  setFieldValue,
  selectTypes,
  handleChange,
  setVisibleKeys,
  visibleKeys,
  visible,
  checkCompany,
}: any): React.ReactElement => {
  const { t } = useTranslation();
  const { user } = useAuth();

  return (
    <Card
      cy="card-test-product"
      className="style"
      renderBody={
        <Row>
          <Text as="span" size="1.2rem" weight={600} color="#201E40">
            {t('labels.productIdentification')}
          </Text>
          <Col md={12} className="mt-4">
            <Select
              id={`typeProduct${t('language') || companyId}`}
              renderKey
              loadOptions={selectTypes}
              cacheOptions
              isDisabled={checkCompany()}
              defaultOptions
              title={`${t('labels.labelTypeProduct')}*`}
              placeholder={t('labels.selectProductCategory')}
              noOptionsMessage={() => t('labels.noOptions')}
              loadingMessage={() => 'Carregando...'}
              onChange={handleChange}
              value={
                Object.values(typeProduct || {}).length
                  ? { value: typeProduct?.value, label: typeProduct?.label }
                  : null
              }
              cy="test-selectTypeProduct"
            />
          </Col>
          <Col md={6} className="styleArea mt-4">
            <Textarea
              cy="test-Detalhes"
              maxLength={160}
              id="description_ptbr"
              name="description_ptbr"
              label={t('labels.descriptionPtBr')}
              onChange={(e) => setFieldValue('description_ptbr', e.target.value)}
              value={values?.description_ptbr || ''}
              placeholder={t('labels.enterDescriptionPtBr')}
            />
            <small>{t('labels.otherLanguageIsRequired')}</small>
          </Col>
          <Col md={6} className="styleArea mt-4">
            <Textarea
              cy="test-Detalhes"
              maxLength={160}
              id="description"
              name="description"
              label={t('labels.description')}
              onChange={(e) => setFieldValue('description', e.target.value)}
              value={values?.description || ''}
              placeholder={t('labels.enterDescription')}
            />
          </Col>
          <Col md={6} className="styleArea mt-4">
            <Textarea
              cy="test-Detalhes"
              maxLength={160}
              id="observation"
              name="observation"
              label={t('labels.internalNote')}
              onChange={(e) => setFieldValue('observation', e.target.value)}
              value={values?.observation || ''}
              placeholder={t('labels.addInternalNote')}
            />
          </Col>
          <Col md={6} className="styleArea mt-4">
            <Textarea
              cy="test-Detalhes"
              maxLength={160}
              id="layers_observation"
              name="layers_observation"
              label={t('labels.layerNote')}
              placeholder={t('labels.addLayerNote')}
              onChange={(e) => setFieldValue('layers_observation', e.target.value)}
              value={values?.layers_observation || ''}
            />
          </Col>

          {user?.default_company?.features?.includes('market') ? (
            <>
              <span className="mt-4">
                {t('language') === 'BR'
                  ? 'Visualização no catálogo de produtos:'
                  : 'Visualization in the product catalog:'}
              </span>
              <Col md={12}>
                <RadioButton options={visible} selectedValue={visibleKeys} setSelected={setVisibleKeys} />
              </Col>
            </>
          ) : (
            <></>
          )}
        </Row>
      }
    />
  );
};

export default CardIdentifyProduct;
