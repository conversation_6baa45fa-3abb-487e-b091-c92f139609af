import React, { useState } from 'react';
import { Table } from 'rsuite';
import { Col, Container, Image, Row } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import EmptyState from '../../../../components/EmptyState';
import EmptyStateImage from '../../../../statics/emptyState.png';
import { FiEdit2 } from 'react-icons/fi';
import InactiveUser from '../../../../statics/delete.svg';
import Modal from '../../../../components/Modal';
import Button from '../../../../components/Button';

import '../styles.scss';
import { useLoader } from '../../../../contexts/LoaderContext';
import toastMsg, { ToastType } from '../../../../utils/toastMsg';
import { CheckCircle2, XCircle } from 'lucide-react';
import { ModalUpload } from '../../../../components/ModalUpload';
import DocumentosService from '../../../../services/documentos.service';

export const UploadArquivo = ({
  sortColumn,
  sortType,
  handleSortColumn,
  isMobile,
  data,
  tableColumns,
  showLoader,
  loadList,
}: any): React.ReactElement => {
  const { Column, HeaderCell, Cell } = Table;
  const { t, i18n } = useTranslation();
  const [show, setShow] = useState<any>({ show: false, client: null });
  const [showModalDocument, setShowModalDocument] = useState<any>({
    show: false,
    idDoc: null,
    idTipo: null,
    fileSize: null,
    name: null,
    idExportador: null,
  });
  const [showMsg, setShowMsg] = useState<any>({
    show: false,
    msg: null,
    idDoc: null,
    titulo: null,
    site: null,
    orgao: null,
    local: null,
  });
  const [uploadError, setUploadError] = useState<any>(null);
  const [showHelpModal, setShowHelpModal] = useState<boolean>(false);

  const { setShowLoader } = useLoader();

  const handleUploadError = (error: any) => {
    // Salvar no localStorage temporariamente
    localStorage.setItem('uploadError', JSON.stringify(error));
    // Forçar atualização do estado
    setUploadError({ ...error });
  };

  const clearUploadError = () => {
    localStorage.removeItem('uploadError');
    setUploadError(null);
  };

  // Verificar se há erro salvo no localStorage ao montar o componente
  React.useEffect(() => {
    const savedError = localStorage.getItem('uploadError');
    if (savedError) {
      try {
        const errorObj = JSON.parse(savedError);
        setUploadError(errorObj);
      } catch (e) {
        localStorage.removeItem('uploadError');
      }
    }
  }, []);

  // Debug: monitorar mudanças no uploadError
  React.useEffect(() => {
    //eslint-disable-next-line
    console.log('uploadError state changed:', uploadError);
  }, [uploadError]);

  const handleDeleteDocument = async (id: any): Promise<void> => {
    if (!id) {
      return;
    }

    try {
      setShowLoader(true);
      await DocumentosService.delete(id);
      setShowLoader(false);
      toastMsg(ToastType.Success, t('response.deleteSuccess'));
      loadList();
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  };

  return (
    <Container className="containerBackgroundCustomers">
      <Modal
        show={show.show}
        handleClose={() => setShow({ ...show, show: false })}
        title={t('modal.removeClientTitle')}
        size="lg"
        className="styleModalConfirm"
      >
        <Container>
          <Row className="mt-4 p-2">
            <Col className="d-flex align-items-center ">
              <h6>
                <br />
                {t('modal.removeDocSubTitle') + show.titulo + '?'}
                <br />
                {t('modal.removeProductDescription')}
              </h6>
            </Col>
          </Row>
        </Container>

        <Row className="mt-4">
          <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
            <Button
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={() => {
                setShow(false);
              }}
            >
              {t('buttons.cancel')}
            </Button>
            <Button cy="btn-save" type="button" variant="danger" onClick={() => handleDeleteDocument(show.idDoc)}>
              {t('buttons.delete')}
            </Button>
          </Col>
        </Row>
      </Modal>

      <Modal
        show={showModalDocument.show}
        handleClose={() => setShowModalDocument({ ...showModalDocument, show: false })}
        title={i18n.language === 'pt-BR' ? 'Upload de Arquivos' : 'File Upload'}
        size="xl"
        className="styleModalConfirm"
        colorIcon
      >
        <ModalUpload
          idTipoDocumento={showModalDocument.idTipo}
          nameDocumento={showModalDocument.name}
          idExportador={showModalDocument.idExportador}
          fileSize={showModalDocument.fileSize}
          idDoc={showModalDocument.idDoc}
          loadList={loadList}
          onClose={() => setShowModalDocument({ ...showModalDocument, show: false })}
          onError={handleUploadError}
        />
      </Modal>

      <Modal
        show={showMsg.show}
        handleClose={() => setShowMsg({ ...showMsg, show: false })}
        title={t('modal.ondeEmitir') + ' ' + showMsg.titulo}
        size="lg"
        className="styleModalConfirm"
      >
        <Container>
          <Row className="mt-4 p-2">
            <Col className="d-flex align-items-center ">
              <h6>
                {showMsg.msg}
                <br />
                <br />
                <b>{t('labels.orgaoResponsavel')}:</b> {showMsg.orgao}
                <br />
                <b>{t('labels.localAcesso')}:</b> {showMsg.local}
                <br />
                {showMsg?.site ? (
                  <Button
                    cy="link"
                    variant="link"
                    onClick={() => window.open(showMsg.site, '_blank')}
                    style={{ color: 'blue' }}
                  >
                    {showMsg.site}
                  </Button>
                ) : (
                  <></>
                )}
              </h6>
            </Col>
          </Row>
        </Container>

        <Row className="mt-4">
          <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
            <Button
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={() => {
                setShowMsg(false);
              }}
            >
              {t('buttons.fechar')}
            </Button>
          </Col>
        </Row>
      </Modal>

      {/* Mensagem de erro do upload */}
      {uploadError && (
        <div className="alert alert-danger d-flex justify-content-between align-items-center mb-3" role="alert">
          <div className="d-flex align-items-center">
            <i className="fas fa-exclamation-triangle me-2"></i>
            <span>{uploadError.message}</span>
          </div>
          <div className="d-flex gap-2">
            {uploadError.showHelpButton && (
              <Button cy="btn-help" variant="outline-primary" size="sm" onClick={() => setShowHelpModal(true)}>
                Como resolver
              </Button>
            )}
            <Button cy="btn-close-error" variant="outline-secondary" size="sm" onClick={clearUploadError}>
              ✕
            </Button>
          </div>
        </div>
      )}

      {data.length > 0 ? (
        <>
          <Table
            bordered
            cellBordered
            sortColumn={sortColumn}
            sortType={sortType}
            onSortColumn={handleSortColumn}
            autoHeight={isMobile}
            height={400}
            affixHorizontalScrollbar={!isMobile}
            data={data}
            className="table"
            rowClassName={(rowData: any) => rowData?.className || ''}
          >
            {tableColumns.map((column: any) => {
              const { field, headerName, color, cursor, textDecoration, fixed, ...rest } = column;

              if (field === 'actions') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            {rowData.idDocumento ? (
                              <>
                                <div
                                  onClick={() => {
                                    clearUploadError(); // Limpar erro anterior
                                    setShowModalDocument({
                                      show: true,
                                      idDoc: rowData.idDocumento,
                                      fileSize: rowData.tamanho_arquivo,
                                      idTipo: rowData.idTipo,
                                      name: rowData.documento,
                                      idExportador: rowData.idExportador,
                                    });
                                  }}
                                  role="presentation"
                                  title={t('titles.alterDocument')}
                                  style={{ cursor: 'pointer', color: '#494747' }}
                                >
                                  <FiEdit2 size={20} color="green" />
                                </div>
                                <div
                                  onClick={() =>
                                    setShow({ show: true, idDoc: rowData.idDocumento, titulo: rowData.documento })
                                  }
                                  role="presentation"
                                  title={t('titles.removeDocument')}
                                  style={{ cursor: 'pointer' }}
                                >
                                  <Image src={InactiveUser} height={20} />
                                </div>
                              </>
                            ) : (
                              <></>
                            )}
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              if (field === 'recebido') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            <div>
                              {rowData[field] === 'true' ? (
                                <CheckCircle2 className="h-5 w-5 text-green-500" />
                              ) : (
                                <XCircle className="h-5 w-5 text-red-500" />
                              )}
                            </div>
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              if (field === 'enviarDocumento') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            <div>
                              {rowData[field] === 'true' ? (
                                <Button
                                  cy="btn-doc"
                                  type="button"
                                  variant="outline-secondary"
                                  style={{ height: 30, fontSize: 10, marginTop: -4 }}
                                  onClick={() => {
                                    clearUploadError(); // Limpar erro anterior
                                    setShowModalDocument({
                                      show: true,
                                      idTipo: rowData.idTipo,
                                      name: rowData.documento,
                                      idExportador: rowData.idExportador,
                                      fileSize: rowData.tamanho_arquivo,
                                    });
                                  }}
                                >
                                  {t('table.enviarDocumento')}
                                </Button>
                              ) : (
                                <label style={{ color: '#046b04' }}>{t('labels.documentoEnviado')}</label>
                              )}
                            </div>
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              if (field === 'ondeEmitir') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            <div>
                              <Button
                                cy="btn_oe"
                                variant="link"
                                style={{
                                  height: 30,
                                  fontSize: 12,
                                  marginTop: -4,
                                  color: '#201e40',
                                }}
                                onClick={() =>
                                  setShowMsg({
                                    show: true,
                                    msg: rowData.descricao,
                                    idDoc: rowData.idDocumento,
                                    titulo: rowData.documento,
                                    site: rowData.site,
                                    orgao: rowData.orgao,
                                    local: rowData.local,
                                  })
                                }
                              >
                                {t('table.ondeEmitir')}
                              </Button>
                            </div>
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              return (
                <>
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap />
                  </Column>
                </>
              );
            })}
          </Table>
        </>
      ) : (
        <>
          {!showLoader && (
            <EmptyState
              text={t('labels.labelDocumnetNotFound')}
              secondaryText={t('labels.labelDescDocumentNotFound')}
              img={EmptyStateImage}
            />
          )}
        </>
      )}

      {/* Modal de ajuda para conversão de PDF */}
      <Modal
        show={showHelpModal}
        handleClose={() => setShowHelpModal(false)}
        title="Como resolver problemas com PDF"
        size="lg"
        className="styleModalConfirm"
      >
        <div className="p-4">
          <div className="alert alert-info mb-4">
            <strong>Problemas mais comuns:</strong>
            <ul className="mb-0 mt-2">
              <li>Arquivo muito grande (limite: 10MB)</li>
              <li>PDF corrompido ou com erro</li>
              <li>Versão do PDF incompatível</li>
              <li>Arquivo protegido por senha</li>
            </ul>
          </div>

          <h5>Soluções recomendadas:</h5>
          <div className="mt-3">
            <h6>1. Corrigir erros no PDF (Recomendado):</h6>
            <div className="ms-3 mb-3">
              <strong>Ferramentas online gratuitas:</strong>
              <ul>
                <li>
                  <strong>1°. Reduzir compressão do PDF: </strong>
                  <a href="https://www.pdfyeah.com/decompress-pdf/" target="_blank" rel="noreferrer">
                    https://www.pdfyeah.com/decompress-pdf/
                  </a>
                </li>
                <li>
                  <strong>2°. SmallPDF: </strong>
                  <a href="https://smallpdf.com/compress-pdf" target="_blank" rel="noreferrer">
                    smallpdf.com/compress-pdf
                  </a>
                </li>
                <li>
                  <strong>3°. ILovePDF: </strong>
                  <a href="https://ilovepdf.com/compress_pdf" target="_blank" rel="noreferrer">
                    ilovepdf.com/compress_pdf
                  </a>
                </li>
                <li>
                  <strong>4°. PDF24: </strong>
                  <a href="https://tools.pdf24.org/pt/comprimir-pdf" target="_blank" rel="noreferrer">
                    tools.pdf24.org/pt/comprimir-pdf
                  </a>
                </li>
              </ul>
              <div className="alert alert-success mt-2">
                <small>
                  <strong>Dica:</strong> Essas ferramentas otimizam o PDF, desconprimindo ou reduzindo o tamanho e
                  mantendo a qualidad e.
                </small>
              </div>
            </div>

            <h6>2. Usando Adobe Acrobat (Se disponível):</h6>
            <div className="ms-3 mb-3">
              <ol>
                <li>Abra o PDF no Adobe Acrobat</li>
                <li>Vá em &quot;Arquivo&quot; → &quot;Salvar como outro&quot; → &quot;PDF otimizado&quot;</li>
                <li>Nas configurações, reduza a qualidade das imagens para 150 DPI</li>
                <li>Desmarque opções desnecessárias</li>
                <li>Salve o arquivo</li>
              </ol>
            </div>

            <h6>3. Verificações antes do upload:</h6>
            <div className="ms-3">
              <ul>
                <li>✅ Arquivo em formato PDF</li>
                <li>✅ Tamanho menor que 10MB</li>
                <li>✅ PDF não protegido por senha</li>
                <li>✅ Arquivo não corrompido (abre normalmente)</li>
                <li>✅ Conteúdo legível e completo</li>
              </ul>
            </div>
          </div>

          <div className="d-flex justify-content-end mt-4">
            <Button cy="btn-close-help" variant="outline-secondary" onClick={() => setShowHelpModal(false)}>
              Fechar
            </Button>
          </div>
        </div>
      </Modal>
    </Container>
  );
};
