import React from 'react';
import { useTranslation } from 'react-i18next';
import { Row, Col } from 'react-bootstrap';
import Text from '../../components/Text';
import { useLoader } from '../../contexts/LoaderContext';
import { ISupplyChainServiceResponse } from '../../interfaces';
import { useAnalytics } from '../../contexts/AnalyticsContext';
import './styles.scss';
import { IColumnsProps } from './TableColumnProperties';
import SupplyChainService from '../../services/supplyChain.service';
import { useAuth } from '../../contexts/AuthContext';
import { TableComponent } from './TableSupplyChain';
import forEach from 'lodash/forEach';

const DashboardImporter: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [query] = React.useState<string>('');
  const [isMobile, setIsMobile] = React.useState<boolean>(window.innerWidth <= 768);
  const [page, setPage] = React.useState(1);
  const { trackEvent } = useAnalytics();
  const [tableLength, setTableLength] = React.useState(0);
  const [lastPage, setLastPage] = React.useState(1);
  const [limit, setLimit] = React.useState(10);
  const [suplyChains, setSuplyChains] = React.useState<any>([]);
  const [companies, setCompanies] = React.useState<any>([]);
  const { setShowLoader, showLoader } = useLoader();

  const handleWindowSizeChange = (): void => {
    if (window.innerWidth <= 768) {
      setIsMobile(true);
    } else {
      setIsMobile(false);
    }
  };

  React.useEffect(() => {
    window.addEventListener('resize', handleWindowSizeChange);
  }, []);

  const collums: IColumnsProps[] = [
    {
      field: 'exportador',
      headerName: t('steps.exporter'),
      color: '#201E40',
      flexGrow: 6,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'importador',
      headerName: t('table.importador'),
      color: '#201E40',
      flexGrow: 6,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'order',
      headerName: t('table.pedido'),
      color: '#201E40',
      flexGrow: 3,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'invoice',
      headerName: 'Invoice',
      color: '#201E40',
      flexGrow: 3,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'bl',
      headerName: 'BL',
      color: '#201E40',
      flexGrow: 3,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'actions',
      headerName: t('modal.modalTitleActions'),
      color: '#201E40',
      flexGrow: 2,
      fixed: false,
      resizable: !!isMobile,
    },
  ];

  const getCompanyByImporter = React.useCallback(async (): Promise<any> => {
    try {
      const res = await SupplyChainService.getCompanyByImporter(String(user.id));

      if (res) {
        setCompanies(res);
      }
    } catch (error) {
      return {};
    }
  }, [user.id, setCompanies]);

  React.useEffect(() => {
    let isCleaningUp = false;

    if (!isCleaningUp) {
      getCompanyByImporter();
    }

    return () => {
      isCleaningUp = true;
    };
  }, [getCompanyByImporter, t]);

  const getSupplyChain = React.useCallback(async (): Promise<ISupplyChainServiceResponse> => {
    try {
      setShowLoader(true);

      //eslint-disable-next-line
      let idsClient: string = '';
      forEach(companies, (item: any) => {
        if (item && item.cliente && item.cliente.id) {
          if (idsClient === '') {
            idsClient = item.cliente.id;
          } else {
            idsClient += `,${item.cliente.id}`;
          }
        }
      });

      const res = await SupplyChainService.findByImporters(idsClient);

      if (res) {
        setShowLoader(false);
        return res;
      }
      return {} as ISupplyChainServiceResponse;
    } catch (error) {
      setShowLoader(false);
      return {} as ISupplyChainServiceResponse;
    }
  }, [limit, page, query, setShowLoader, companies]);

  async function loadList(): Promise<void> {
    const res = await getSupplyChain();

    if (res.data !== undefined) {
      const rows: any = res.data.map((item: any, index: number) => ({
        exportador: item.cliente?.name || t('labels.notInformed'),
        importador: item.importador?.name || t('labels.notInformed'),
        order: item.pedido || t('labels.notInformed'),
        invoice: item.invoice || t('labels.notInformed'),
        bl: item.bl || t('labels.notInformed'),
        className: index % 2 === 0 ? 'custom-row' : '',
        uuid: item.uuid,
        id: item.id,
      }));
      setSuplyChains(rows);
      setLastPage(res.meta.last_page);
      setTableLength(res.meta.total);
    }
  }

  React.useEffect(() => {
    let isCleaningUp = false;

    if (!isCleaningUp && companies.length > 0) {
      loadList();
    }

    return () => {
      isCleaningUp = true;
    };
  }, [getSupplyChain, t]);

  const handleChangeLimit = (dataKey: number): void => {
    setPage(1);
    setLimit(dataKey);
    trackEvent('Quotes per page', {
      action: `See ${dataKey} per page`,
      param: 'page',
    });
  };

  const handleDownPage = (): void => {
    const newPage = page - 1;
    if (newPage >= 1) {
      setPage(newPage);
    }

    trackEvent('Quotes Previus Page', {
      action: `Clicou previus page`,
      param: 'Page',
    });
  };

  const handleUpPage = (): void => {
    const newPage = page + 1;
    if (page <= tableLength / limit) {
      setPage(newPage);
    }

    trackEvent('Quotes Next Page', {
      action: `Clicou next page`,
      param: 'Page',
    });
  };

  return (
    
     <div className="dashboard-container">
      
      <Row>
        <Col md={12} className="d-flex justify-content-between mb-4">
          <Text className="pages-title" as="b" size="2rem" weight={600} color="black">
            {t('titles.supply')}
          </Text>
        </Col>

        <TableComponent
          isMobile={isMobile}
          data={suplyChains}
          tableColumns={collums}
          page={page}
          lastPage={lastPage}
          limit={limit}
          handleChangeLimit={handleChangeLimit}
          handleDownPage={handleDownPage}
          handleUpPage={handleUpPage}
          showLoader={showLoader}
          atualizarSupplyChain={loadList}
        />
      </Row>
    
    </div>

  );
};

export default DashboardImporter;
