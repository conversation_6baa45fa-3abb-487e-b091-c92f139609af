import React, { useCallback, useRef, useState } from 'react';
import { Row, Col, Form, Button, Card } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom';

import Text from '../../../components/Text';
import Section from '../../../components/Section';
import toastMsg, { ToastType } from '../../../utils/toastMsg';
import AuthsService from '../../../services/auth.service';
import Recaptcha from '../../../components/Recaptcha';
import './styles.scss';

export default function ForgotPassword(): React.ReactElement {
  const { t } = useTranslation();
  const history = useHistory();
  const emailRef = useRef<HTMLInputElement>(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = useCallback(
    async (e: React.SyntheticEvent) => {
      e.preventDefault();

      try {
        if (!emailRef || !emailRef.current) return;
        setLoading(true);

        await AuthsService.sendPasswordResetEmail(emailRef.current.value);

        toastMsg(ToastType.Success, t('response.emailSendSucess'));

        history.push('/login');
      } catch (error) {
        toastMsg(ToastType.Error, t('response.emailError'));

        setLoading(false);
      }
    },
    [history, t]
  );

  return (
    <Section title="Esqueci minha senha" description="Esqueci minha senha">
      <Row className="forgot-password d-flex align-items-center justify-content-center mt-5">
        <Col className="box-forgot">
          <Card>
            <Card.Body>
              <Row className="justify-content-start">
                <Text as="h1" className="w-auto labelForgot">
                  {t('labels.labelForgotPassword')}
                </Text>
              </Row>
              <Form onSubmit={handleSubmit} autoComplete="off">
                <Form.Group className="mb-3 mt-3" controlId="email">
                  <Form.Label>{`${t('placeholders.holderEmail')}`}</Form.Label>
                  <Form.Control type="email" ref={emailRef} required />
                </Form.Group>
                <Row className="align-items-center justify-content-end">
                  <Form.Group className="w-auto" controlId="button">
                    <Button disabled={loading} className="btnLogin w-10 d-flex align-items-center" type="submit">
                      <Text as="span">{`${t('buttons.forgot')}`}</Text>
                    </Button>
                  </Form.Group>
                </Row>
              </Form>
              <Row className="text-center mt-4">
                <Text as="span">{`2025 -  ${t('labels.labelCopyright')}`}</Text>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>
      <Recaptcha />
    </Section>
  );
}
