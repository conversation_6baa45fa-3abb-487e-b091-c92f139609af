import { User } from '.';
import { <PERSON>s, Meta } from './IMeta';

export interface Documento {
  documento_id: number;
  nom_documento_pt: string;
  nom_documento_en: string;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface Status {
  etapa_id: number;
  nom_etapa_pt: string;
  nom_etapa_en: string;
  num_ordem: number;
  des_cor_hexa_fundo: string;
  des_cor_hexa_fonte: string;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface Datum {
  uuid: string;
  business_id: number;
  notes: string;
  shipment_estimate: string;
  arrival_estimate: string;
  created_at: string;
  status: Status;
  user: User;
}

export interface IDocumentTable {
  data: Datum[];
  links: Links;
  meta: Meta;
}

export interface DocumentoPermissaoUsuario {
  perfil_id: number;
  ind_visualiza: boolean;
  ind_insere: boolean;
  ind_exclui: boolean;
}

export interface DocumentoEtapaDestino {
  documento_id: number;
  etapa_id: number;
  cod_pais: string;
  cliente_id?: any;
  ind_obrigatorio: string;
  ind_unico_multiplo: string;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface NegocioUploadDoc {
  negocio_doc_id: number;
  negocio_id: number;
  documento_id: number;
  etapa_id: number;
  documento: DocumentoEtapaDestino;
  des_documento: string;
  des_arq_documento: string;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface RootDocument {
  documento_id: number;
  nom_documento_pt: string;
  nom_documento_en: string;
  documento_permissao_usuario: DocumentoPermissaoUsuario;
  documento_etapa_destino: DocumentoEtapaDestino[];
  negocio_doc: NegocioUploadDoc[];
}

export interface ResponseDocument {
  data: RootDocument[];
}
