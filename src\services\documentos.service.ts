import HttpClient from './httpClient';

export interface IGrupoDocumentoService {
    titulo: string;
    descricao: string;
}

export interface IGrupoDocumentoExportadorService {
    uuid: string;
    documento_id: number;
    titulo: string;
    descricao: string;
    status: string;
}

export interface IGrupoDocumentoExportadorSave {
    id?: number
    nome: string;
    descricao: string;
    status: string;
    documento: IDocumentSave;
    exportador_id: string;
}

export interface IGrupoDocumentoFlorestaSave {
    id?: number
    nome: string;
    descricao: string;
    status: string;
    documento: IDocumentSave;
    floresta_id?: string;
}

export interface IDocumentSave {
    nome: string;
    descricao: string;
    descricao_en: string;
    extensao: string;
    tamanho_arquivo: string;
    vencimento: string;
    tipo_documento_id: string;
    status: string;
}

export interface ITipoDocumentoService {
    id: number;
    titulo: string;
    descricao: string;
    descricao_en: string;
    grupo_documento_id: number;
}

class DocumentosService {

    static async getGruposDocumento(): Promise<IGrupoDocumentoService> {
        const { data } = await HttpClient.api.get('documento/grupos');

        return data;
    }

    static async getDocumentoExportador(idExportador: number): Promise<IGrupoDocumentoExportadorService> {
        const { data } = await HttpClient.api.get(`documento/?exportador_id=${idExportador}`);

        return data;
    }

    static async getTiposDocumento(idGrupo: string): Promise<ITipoDocumentoService> {
        const { data } = await HttpClient.api.get(`documento/tipoDocumento/${idGrupo}`);

        return data;
    }

    static async getDocumentoById(id: string): Promise<ITipoDocumentoService> {
        const { data } = await HttpClient.api.get(`documento/id/${id}`);

        return data;
    }

    static async getDocumentoCar(id: string): Promise<IDocumentSave> {
        const { data } = await HttpClient.api.get(`documento/car/${id}`);

        return data;
    }

    static async salvarDocumento(formData: FormData): Promise<any> {
        const idDocumento = formData.get('id');

        try {
            if (idDocumento !== "") {
                const { data } = await HttpClient.api.post(`/documento/update`, formData, {
                    headers: { 'Content-Type': 'multipart/form-data' },
                });
                return data;
            } else {
                const { data } = await HttpClient.api.post(`/documento/`, formData, {
                    headers: { 'Content-Type': 'multipart/form-data' },
                });
                return data;
            }
        } catch (error: any) {
            // Estruturar o erro para melhor tratamento no frontend
            const errorResponse = {
                message: error?.response?.data?.message || error?.response?.data?.errors || 'Erro desconhecido no upload',
                status: error?.response?.status || 500,
                isPdfError: false,
                showHelpButton: false
            };

            // Verificar se é erro relacionado a PDF
            const errorMessage = errorResponse.message.toLowerCase();
            if (errorMessage.includes('pdf') ||
                errorMessage.includes('formato') ||
                errorMessage.includes('tamanho') ||
                errorMessage.includes('comprimido') ||
                errorMessage.includes('versão') ||
                errorMessage.includes('corrupted') ||
                errorMessage.includes('invalid')) {
                errorResponse.isPdfError = true;
                errorResponse.showHelpButton = true;
            }

            // Re-lançar o erro com informações estruturadas
            const enhancedError = new Error(errorResponse.message);
            (enhancedError as any).response = {
                data: errorResponse,
                status: errorResponse.status
            };

            throw enhancedError;
        }
    }

    static async delete(id: string): Promise<string> {
        const { statusText } = await HttpClient.api.delete(`/documento/${id}`);
        return statusText;
    }

}

export default DocumentosService;