/**
 * @description
 * Retorna o arquivo csv para baixar.
 * Exemplo de uso:
 * convertBlobToCSVDownload(Blob);
 * @param {Blob} res
 * @return {Void}
 */

const convertBlobToCSVDownload = async (res: Blob): Promise<void> => {
  const file = new Blob([res], { type: 'text/csv' });
  const csvURL = URL.createObjectURL(file);
  const tempLink = document.createElement('a');
  tempLink.href = csvURL;
  tempLink.setAttribute('download', 'importPosts.csv');
  tempLink.click();
};

export default convertBlobToCSVDownload;
