import FileSaver from "file-saver";
import { ISupplyChainServiceResponse } from "../interfaces";
import HttpClient from "./httpClient";
import toastMsg, { ToastType } from '../utils/toastMsg';

export interface IPropsSaveSupplyChain {
    id?: string,
    name: string;
    pedido: string;
    bl: string;
    invoice: string;
    status: string;
    importador_id: string;
    cliente_id: string;
}

export interface IPropsSaveProductSupplyChain {
    product_description: string,
    product_quantity: string,
    product_quantity_tons?: string, // NOVO: Quantidade em toneladas
    status: 'A',
    supply_chain_id: string,
}

export interface IPropsSaveSupplyChainSpecie {
    id?: string,
    uuid?: string,
    mp_qtde: string,
    mp_qtde_tons?: string, // NOVO: Quantidade MP em toneladas
    mp_nome: string,
    floresta_nome: string,
    fornecedor_nome: string,
    supply_chain_product_id: string,
    floresta_id: string,
    materia_prima_id: string,
    fornecedor_id: string,
}

class SupplyChainService {
    static async getSupplyChainByIndustry(
        client_id: number,
        name: string,
        page: number,
        perPage: number,
        order_by = 'name',
        sort = 'asc'
    ): Promise<ISupplyChainServiceResponse> {
        const { data } = await HttpClient.api.get(`/supplychain/?cliente_id=${client_id}&name=${name === undefined ? '' : name}&page=${page}&perPage=${perPage}&order_by=${order_by}&sort=${sort}`
        );
        return data;
    }

    static async getSupplyChainByImporter(
        client_id: number,
        name: string,
        page: number,
        perPage: number,
        order_by = 'name',
        sort = 'asc'
    ): Promise<ISupplyChainServiceResponse> {
        const { data } = await HttpClient.api.get(`/supplychain/?importador_id=${client_id}&name=${name === undefined ? '' : name}&page=${page}&perPage=${perPage}&order_by=${order_by}&sort=${sort}`
        );
        return data;
    }

    static async findByImporters(ids: string): Promise<any> {
        const { data } = await HttpClient.api.get(`/supplychain/${ids}/importer`);
        return data;
    }

    static async getSupplyById(id: string): Promise<any> {
        const { data } = await HttpClient.api.get(`/supplychain/${id}/all`);
        return data;
    }

    static async updateSupplyChain(supply: IPropsSaveSupplyChain, uuid: string): Promise<void> {
        const { data } = await HttpClient.api.put(`/supplychain/${uuid}`, { ...supply });

        return data;
    }

    static async saveSupplyChain(supply: IPropsSaveSupplyChain): Promise<void> {
        const { data } = await HttpClient.api.post(`/supplychain`, { ...supply });

        return data;
    }

    static async liberarDownloadSupplyChain(status: string, id: string): Promise<void> {
        const { data } = await HttpClient.api.put(`/supplychain/${id}/${status}`);

        return data;
    }

    static async saveProductSupplyChain(product: IPropsSaveProductSupplyChain): Promise<void> {
        const { data } = await HttpClient.api.post(`/supplychain/product`, product);

        return data;
    }

    static async updateProductSupplyChain(uuid: string, product: IPropsSaveProductSupplyChain): Promise<void> {
        const { data } = await HttpClient.api.put(`/supplychain/${uuid}/product`, product);

        return data;
    }

    static async getProductBySupplyChain(uuid: string): Promise<any> {
        const { data } = await HttpClient.api.get(`/supplychain/${uuid}/product`);

        return data;
    }

    static async getDocumentoBySupplyChainId(id: string): Promise<any> {
        const { data } = await HttpClient.api.get(`/supplychain/${id}/documento`);

        return data;
    }

    static async salvarSpecie(formData: FormData): Promise<IPropsSaveSupplyChainSpecie> {
        try {
            const { data } = await HttpClient.api.post(`supplychain/species/`, formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            return data.data;
        } catch (error: any) {
            // Estruturar o erro para melhor tratamento no frontend
            const errorResponse = {
                message: error?.response?.data?.message || error?.response?.data?.errors || 'Erro desconhecido no upload',
                status: error?.response?.status || 500,
                isPdfError: false,
                showHelpButton: false
            };

            // Verificar se é erro relacionado a PDF
            const errorMessage = errorResponse.message.toLowerCase();
            if (errorMessage.includes('pdf') ||
                errorMessage.includes('formato') ||
                errorMessage.includes('tamanho') ||
                errorMessage.includes('comprimido') ||
                errorMessage.includes('versão') ||
                errorMessage.includes('corrupted') ||
                errorMessage.includes('invalid')) {
                errorResponse.isPdfError = true;
                errorResponse.showHelpButton = true;
            }

            // Re-lançar o erro com informações estruturadas
            const enhancedError = new Error(errorResponse.message);
            (enhancedError as any).response = {
                data: errorResponse,
                status: errorResponse.status
            };

            throw enhancedError;
        }
    }

    static async salvarDocSupply(formData: FormData): Promise<IPropsSaveSupplyChain> {
        try {
            const { data } = await HttpClient.api.post(`supplychain/docsupply/`, formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            return data.data;
        } catch (error: any) {
            // Estruturar o erro para melhor tratamento no frontend
            const errorResponse = {
                message: error?.response?.data?.message || error?.response?.data?.errors || 'Erro desconhecido no upload',
                status: error?.response?.status || 500,
                isPdfError: false,
                showHelpButton: false
            };

            // Verificar se é erro relacionado a PDF
            const errorMessage = errorResponse.message.toLowerCase();
            if (errorMessage.includes('pdf') ||
                errorMessage.includes('formato') ||
                errorMessage.includes('tamanho') ||
                errorMessage.includes('comprimido') ||
                errorMessage.includes('versão') ||
                errorMessage.includes('corrupted') ||
                errorMessage.includes('invalid')) {
                errorResponse.isPdfError = true;
                errorResponse.showHelpButton = true;
            }

            // Re-lançar o erro com informações estruturadas
            const enhancedError = new Error(errorResponse.message);
            (enhancedError as any).response = {
                data: errorResponse,
                status: errorResponse.status
            };

            throw enhancedError;
        }
    }

    static async getSpeciesBySupplyChainProduct(
        scp_id: number,
        name: string,
        page: number,
        perPage: number,
        order_by = 'name',
        sort = 'asc'
    ): Promise<ISupplyChainServiceResponse> {
        const { data } = await HttpClient.api.get(`/supplychain/speciesAll/?supply_chain_product_id=${scp_id}&name=${name === undefined ? '' : name}&page=${page}&perPage=${perPage}&order_by=${order_by}&sort=${sort}`
        );

        return data;
    }

    static async delete(uuid: string): Promise<void> {
        await HttpClient.api.delete(`/supplychain/${uuid}`);
    }

    static async deleteSpecie(id: string): Promise<void> {
        await HttpClient.api.delete(`/supplychain/specie/${id}`);
    }

    /*static async downloadDocumento(shupplyId: string): Promise<void> {
        const { data } = await HttpClient.api.get(`/supplychain/${shupplyId}/gerarDocumento`, {
            responseType: 'blob',
        });

        FileSaver.saveAs(data);
    }*/

    /*static async downloadDocumento(shupplyId: string): Promise<void> {
        try {
            const response = await HttpClient.api.get(`/supplychain/${shupplyId}/gerarDocumento`, {
                responseType: 'blob',
            });

            // Verifica se o response é um blob PDF
            if (response.data.type === 'application/pdf') {
                const fileName = response.headers['content-disposition']
                    ? response.headers['content-disposition'].split('filename=')[1].replace(/"/g, '')
                    : `EUDR_${shupplyId}.pdf`;

                FileSaver.saveAs(response.data, fileName);
            } else {
                // Se não for PDF, provavelmente é uma mensagem de erro em JSON
                const reader = new FileReader();
                reader.onload = async () => {
                    const errorData = JSON.parse(reader.result as string);
                    const errorMessage = errorData.message || 'Erro ao gerar documento';
                    toastMsg(ToastType.Error, errorMessage);
                };
                reader.readAsText(response.data);
                return;
            }
        } catch (error: any) {
            if (error.response?.data instanceof Blob) {
                // Lê o blob como texto para extrair a mensagem de erro
                const reader = new FileReader();
                //eslint-disable-next-line @typescript-eslint/no-unused-vars
                return new Promise((resolve, reject) => {
                    reader.onload = async () => {
                        const errorData = JSON.parse(reader.result as string);
                        const errorMessage = errorData.message + "--------" || 'Erro ao gerar documento';
                        toastMsg(ToastType.Error, errorMessage);
                    };
                    reader.readAsText(error.response.data);
                });
            }
            return;
            // Erro genérico ou de rede
            //const errorMessage = error.message || 'Erro ao gerar documento';
            //throw new Error(errorMessage);
        }
    }*/

    static async downloadDocumento(shupplyId: string): Promise<void> {
        try {
            const response = await HttpClient.api.get(`/supplychain/${shupplyId}/gerarDocumento`, {
                responseType: 'blob',
            });

            // Verifica se o response é um blob PDF
            if (response.data.type === 'application/pdf') {
                const fileName = response.headers['content-disposition']
                    ? response.headers['content-disposition'].split('filename=')[1].replace(/"/g, '')
                    : `EUDR_${shupplyId}.pdf`;

                FileSaver.saveAs(response.data, fileName);
            } else {
                // Se não for PDF, provavelmente é uma mensagem de erro em JSON
                const reader = new FileReader();
                reader.onload = () => {
                    try {
                        const errorData = JSON.parse(reader.result as string);
                        const errorMessage = errorData.message || 'Erro ao gerar documento';
                        toastMsg(ToastType.Error, errorMessage);
                    } catch (e) {
                        toastMsg(ToastType.Error, 'Erro ao gerar documento');
                    }
                };
                reader.readAsText(response.data);
            }
        } catch (error: any) {
            if (error.response?.data instanceof Blob) {
                const reader = new FileReader();
                reader.onload = () => {
                    try {
                        const errorData = JSON.parse(reader.result as string);
                        const errorMessage = errorData.message || 'Erro ao gerar documento';
                        toastMsg(ToastType.Error, errorMessage);
                    } catch (e) {
                        toastMsg(ToastType.Error, 'Erro ao processar resposta do servidor');
                    }
                };
                reader.readAsText(error.response.data);
            } else {
                toastMsg(ToastType.Error, 'Erro ao gerar documento');
            }
            throw error; // Propaga o erro para que o componente possa tratar o loader
        }
    }

    static async downloadGeoJson(documentId: string, tipo: string, name: string): Promise<void> {
        const { data } = await HttpClient.api.get(`/supplychain/${documentId}/gerarGeoJson/${tipo}`, {
            responseType: 'blob',
        });
        if (tipo === 'area') {
            const fileName = `${name}.png`;
            FileSaver.saveAs(data, fileName);
        } else {
            const fileName = `${name}.geoJson`;
            FileSaver.saveAs(data, fileName);
        }
    }

    static async getSupplyChainSpecieMP(supplyChainEspecieId: string): Promise<any> {
        const { data } = await HttpClient.api.get(`/supplychain/${supplyChainEspecieId}/scsmp`);

        return data;
    }

    static async getCompanyByImporter(userId: string): Promise<any> {
        const { data } = await HttpClient.api.get(`/supplychain/${userId}/company`);

        return data;
    }

    static async getCompanyByImporterSF(id: string): Promise<any> {
        const { data } = await HttpClient.api.get(`/supplychain/${id}/companySF`);

        return data;
    }

    // Método específico para buscar documentos de espécies (notas fiscais)
    static async getDocumentosEspecie(especieId: string): Promise<any> {
        try {
            const { data } = await HttpClient.api.get(`/supplychain/${especieId}/documento`);
            return data;
        } catch (error) {
            console.error('Erro ao buscar documentos da espécie:', error);
            throw error;
        }
    }

    // Método para obter informações de um arquivo específico
    static async getFileInfo(filePath: string): Promise<any> {
        try {
            const { data } = await HttpClient.api.head(filePath);
            return data;
        } catch (error) {
            console.error('Erro ao obter informações do arquivo:', error);
            throw error;
        }
    }
}
export default SupplyChainService;