import React, { useEffect, useState } from 'react';
import * as yup from 'yup';
import { Formik, Form } from 'formik';
import { useLocation } from 'react-router-dom';
import { Row, Col } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import toastMsg, { ToastType } from '../../../../../utils/toastMsg';
import { ISelectOption } from '../../../../../interfaces';
import Button from '../../../../../components/Button';
import Input from '../../../../../components/Input';
import AuthsService, { UserById } from '../../../../../services/auth.service';
import { useLoader } from '../../../../../contexts/LoaderContext';
import './styles.scss';
import { StatusProfile } from '../../../../../enums/statusProfile';

interface IPropsDefaultValues {
  name: string;
  email: string;
}

const defaultValues = {
  name: '',
  email: '',
} as IPropsDefaultValues;

interface IPropsCard {
  idClient: string;
}

const Actions = ({ idClient }: IPropsCard): React.ReactElement => {
  const { t } = useTranslation();
  const location: any = useLocation();
  const [statusAccess] = useState<ISelectOption>({
    value: StatusProfile.STATUS_APPROVED,
    label: t('labels.approve'),
  });
  const [formValues, setFormValues] = useState<IPropsDefaultValues>(defaultValues);
  const [user, setUser] = useState<UserById>();
  const { setShowLoader } = useLoader();

  const createSchema = yup.object().shape({
    name: yup.string().max(255, t('exceptions.invalidCompanyName')).required(t('exceptions.requiredField')),
    email: yup
      .string()
      .max(255, t('exceptions.invalidEmail'))
      .email(t('exceptions.invalidEmail'))
      .required(t('exceptions.requiredField')),
  });

  const userDetail = React.useCallback(async (): Promise<UserById> => {
    try {
      setShowLoader(true);
      const res = await AuthsService.getById(location?.state?.id);
      if (res) {
        return res;
      }
      return {} as UserById;
    } catch (error) {
      return {} as UserById;
    } finally {
      setShowLoader(false);
    }
  }, [location?.state?.id, setShowLoader]);

  useEffect(() => {
    let isCleaningUp = false;

    async function loadCrudDetail(): Promise<void> {
      const res = await userDetail();
      if (res && !isCleaningUp) {
        setUser(res);
      }
    }
    if (location?.state?.id) {
      loadCrudDetail();
    }

    return () => {
      isCleaningUp = true;
    };
  }, [userDetail, location?.state?.id]);

  const handleSubmit = async (values: IPropsDefaultValues): Promise<void> => {
    try {
      setShowLoader(true);

      const newValues: any = {
        ...values,
        profile_id: 6,
        company_id: idClient,
        status_profile: statusAccess.value || StatusProfile.STATUS_APPROVED,
      };

      /*if (location?.state?.id) {
        const currentUserData = await AuthsService.getById(location?.state?.id);

        if (currentUserData?.data?.email === newValues.email) {
          delete newValues.email;
        }

        await AuthsService.updateUser(newValues, location?.state?.id);
        toastMsg(ToastType.Success, t('response.editSuccess'));
      } else {*/
      await AuthsService.newUser(newValues);
      toastMsg(ToastType.Success, t('labels.labelsDadosEnviado'));
      //}
      //history.go(0);
    } catch (error: any) {
      toastMsg(ToastType.Error, t('exceptions.emailAlready'));
    } finally {
      setShowLoader(false);
    }
  };

  const setValuesUpdate = React.useCallback(async () => {
    setFormValues({
      name: user?.data?.name || '',
      email: user?.data?.email || '',
    });
  }, [user, t]);

  useEffect(() => {
    if (user) {
      setValuesUpdate();
    }
  }, [setValuesUpdate, user]);

  return (
    <>
      <Formik
        initialValues={formValues}
        validationSchema={createSchema}
        enableReinitialize
        validateOnBlur={false}
        onSubmit={(values) => {
          handleSubmit(values);
        }}
      >
        {({ values, errors, touched, setFieldValue }) => (
          <>
            <Form autoComplete="off" style={{ width: '95%' }}>
              <Row className="d-flex w-100">
                <Col md={12} className="mt-3">
                  <Input
                    maxLength={255}
                    label={t('placeholders.holderNewUser')}
                    cy="test-name"
                    isInvalid={(errors.name && touched.name) || false}
                    onChange={(e) => {
                      setFieldValue('name', e.target.value);
                    }}
                    msg={errors.name}
                    id="name"
                    name="name"
                    as="input"
                    value={values?.name || ''}
                  />
                </Col>
                <Col md={12} className="mt-3">
                  <Input
                    maxLength={255}
                    label={t('placeholders.holderEmail')}
                    cy="test-email"
                    isInvalid={(errors.email && touched.email) || false}
                    onChange={(e) => {
                      setFieldValue('email', e.target.value);
                    }}
                    msg={errors.email}
                    id="email"
                    name="email"
                    as="input"
                    value={values?.email || ''}
                  />
                </Col>

                <Col md={12} className="d-flex justify-content-end gap-2 mb-2 mt-4">
                  <Button cy="btn-save" type="submit" variant="success" disabled={!values.name || !values.email}>
                    {t('buttons.save')}
                  </Button>
                </Col>
              </Row>
            </Form>
          </>
        )}
      </Formik>
    </>
  );
};

export default Actions;
