export interface RangeProps {
  max: string;
  min: string;
  type: 'range';
}

interface UniqueProps {
  value: string;
  type: 'unique';
}

interface ListProps {
  value: string[];
  type: 'list';
}

interface Item {
  id: number;
  description: string;
  description_ptbr: string;
  types: number[];
}

type Props = RangeProps | UniqueProps | ListProps;

export function renderPropertiesValues(arr: Props[] | any): string {
  if (arr) {
    let obj;
    if (arr && arr.length > 0) {

      obj = arr[0];
    }
    if (!obj) {
      return '';
    }
    if (obj.type === 'range') {
      const { max, min } = obj;
      return `${min} - ${max}`;
    }
    if (obj.type === 'unique') {
      const { value } = obj;
      return value;
    }
    if (obj.type === 'list') {
      const { value } = obj;
      return value.join(' | ');
    }
  }
  return '';
}

export function renderDefaultObjectsTable(arr: Item[] | any, language: string): string {
  if (arr.length > 0) {
    return arr.map((element: any) => (language === 'USA' ? element.description : element.description_ptbr)).join(', ');
  }
  return '';
}
