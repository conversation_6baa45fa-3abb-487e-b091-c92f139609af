import React from 'react';
import { Card, CardProps } from 'react-bootstrap';
import './styles.scss';

interface IProp extends CardProps {
  renderBody?: React.ReactNode;
  header?: React.ReactElement;
  cy: string;
  classNameHeader?: string;
}

const CardUser = ({ renderBody, header, cy, classNameHeader, className, ...props }: IProp): React.ReactElement => (
  <Card {...props} className={`card-component ${className}`} data-cy={cy}>
    {header ? (
      <Card.Header className={!classNameHeader ? 'card-component__header' : classNameHeader}>{header}</Card.Header>
    ) : (
      ''
    )}

    <Card.Body className="card-component__body">{renderBody}</Card.Body>
  </Card>
);

CardUser.defaultProps = { renderBody: '' };

export default CardUser;
