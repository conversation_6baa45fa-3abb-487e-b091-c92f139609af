header {
  .nav {
    display: flex;
    align-items: center;
    height: 5rem;
    max-height: 5rem;
  }

  &__button {
    color: var(--white-100);
    font-weight: var(--is-600);
    font-size: 0.8rem;
  }

  .selectLanguage {
    .btn {
      background-color: var(--gree-400) !important;
      border-radius: 4px;
      border-color: var(--gray-600) !important;

      padding-top: 1.25rem;
      padding-bottom: 1.25rem;

      display: flex;
      justify-content: space-between;
      align-items: center;

      color: var(--gray-600) !important;

      &:hover {
        background-color: var(--gree-400) !important;
        color: var(--gray-800) !important;
        border-color: var(--green-400) !important;
        box-shadow: none !important;
      }

      img {
        height: 2rem;
        max-width: 2rem;
      }
    }
  }

  .login-button {
    background: #3c755f !important;
    border: none !important;
    color: white !important;
    border-radius: 50px;
    padding: 0.75rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(24, 32, 26, 0.2);

    &:hover {
      background: #1f3b33 !important;
      color: white !important;
      transform: translateY(-2px);
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    }

    &:focus {
      background: #1f3b33 !important;
      color: white !important;
      box-shadow: 0 0 0 0.2rem rgba(60, 117, 95, 0.25);
    }

    &:active {
      background: #1f3b33 !important;
      color: white !important;
    }

    @media (max-width: 768px) {
      margin-bottom: 1rem;
      width: 100%;
      padding: 0.6rem 1.5rem;
      font-size: 0.9rem;
    }
  }
}

.alignTextTop {
  @media (max-width: 1024px) {
    white-space: nowrap;
  }
}

.navbar-top {
  background-image: url('../../statics/others/bgHeader.png');
  border-radius: 0px 0px 30px 30px !important;
  background-repeat: no-repeat;
  background-size: cover;
  @media (max-width: 1070px) {
    background-image: url('../../statics/others/bgMobileHeader.png');
    border-radius: 0px 0px 30px 30px !important;
    background-repeat: no-repeat;
    background-size: cover;
  }
  &--link {
    color: var(--white-100);
    font-weight: var(--is-300);
    text-decoration: none;
    margin-right: 2rem;
    padding-bottom: 0.5rem;
    @media (max-width: 768px) {
      margin-right: 0;
      margin-left: 1.4rem;
    }
    &:hover,
    &:focus {
      color: var(--white-100);
    }
    &__active {
      font-weight: var(--is-700);
      border-bottom: 2px solid #008000 !important;
    }

    @media (max-width: 768px) {
      margin-top: 0.84rem;
      margin-bottom: 1rem;
      //text-align: right;
      position: relative;
      right: 0.5rem;
    }
  }
  &--button {
    display: flex;
    @media (max-width: 768px) {
      justify-content: flex-end;
    }
  }
  &--showMobile {
    display: none;
    @media (max-width: 768px) {
      display: flex;
    }
  }
  &--showAll {
    display: flex;
    margin-right: 2rem;
    @media (max-width: 768px) {
      display: none;
    }
  }
}

.navbar-toggler {
  cursor: pointer;
  background-color: var(--green-400);
  .navbar-toggler-icon {
    background-image: url('../../statics/icons/menu.svg');
  }
}

.alignResponsiveHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  @media (max-width: 768px) {
    width: 100%;
  }
}

.containerAlign {
  padding-left: 2rem;
  white-space: nowrap;
  @media (max-width: 768px) {
    padding-left: 0;
  }
}

.colMobileLogo {
  @media (max-width: 768px) {
    display: none;
  }
}

.colMobile {
  @media (max-width: 1080px) {
    display: none;
  }
}

.initialName {
  border-radius: 50%;
  display: inline-block;
  height: 3rem;
  width: 3rem;
  border: 1px solid #edf0f0;
  background-color: #edf0f0;
  @media (max-width: 990px) {
    display: none;
  }
}

.logout {
  background-color: #fafafa;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.alignItemsDropDown {
  display: flex;
  align-items: center;
  gap: 30px 20px;
}

.alignTextCompanySelected {
  margin-top: 1.2rem;
  padding-left: auto;
}

.dropDownMobile {
  @media (max-width: 768px) {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: flex-end;
  }
}

.nameUser {
  margin-bottom: 1rem;
}

.abreviationUser {
  padding-top: 0.5rem;
  color: #87949a !important;
}

.sectionLimit {
  max-width: 96rem;
  margin-left: auto;
  margin-right: auto;
}
.line {
  width: 0px;
  height: 54px;
  top: 19px;
  margin-right: 2rem;
  border: 0.8px solid #ffffff;
  @media (max-width: 990px) {
    display: none;
  }
}
.dropDownAlign {
  @media (max-width: 768px) {
    padding-left: 1rem !important;
    align-self: flex-start;
  }

  .dropup,
  .dropend,
  .dropdown,
  .dropstart {
    padding-right: 9.5rem !important;
    @media (max-width: 768px) {
      padding-right: 13rem !important;
    }
  }
}

.dropdown-menu {
  max-height: 36rem;
  max-width: 18rem;
  overflow-y: auto;
  overflow-x: hidden;
  @media (max-width: 1600px) {
    max-height: 20rem;
  }
  @media (max-width: 1400px) {
    max-height: 16rem;
  }
  @media (max-width: 768px) {
    max-height: 7rem;
  }
}

.dropdown-item {
  white-space: inherit;
}

.nav-link:hover,
.nav-link:focus {
  color: white;
}
.nav-link {
  color: white;
  @media (max-width: 990px) {
    padding-left: 0;
  }
}

a:hover {
  text-decoration: none;
}

p {
  color: #54666f;
  font-size: 0.7rem;
}

.navbar-top--showAll {
  @media (max-width: 990px) {
    & button {
      padding: 0;
    }
  }
}
