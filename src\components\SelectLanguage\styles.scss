.selectLanguage {
  .dropdown-menu {
    min-width: auto;
  }

  .btn {
    background-color: transparent;

    padding-top: 1.25rem;
    padding-bottom: 1.25rem;

    display: flex;
    justify-content: space-between;
    align-items: center;

    color: var(--gray-600);

    &:hover {
      background-color: transparent;
      box-shadow: none;
    }

    img {
      height: 2rem;
      max-width: 2rem;
    }
  }
  .btn-check:checked + .btn-primary,
  .btn-check:active + .btn-primary,
  .btn-primary:active,
  .btn-primary.active,
  .show > .btn-primary.dropdown-toggle {
    color: #ffffff;
    background-color: transparent;
    border-color: transparent;
    box-shadow: none;
  }
}

.alingBtnLanguages {
  display: flex;
  justify-content: flex-end;
}
