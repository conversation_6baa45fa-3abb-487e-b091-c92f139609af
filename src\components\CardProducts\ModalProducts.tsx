import React from 'react';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom';
import { Col, Image, Container, Row } from 'react-bootstrap';
import Modal from '../Modal';
import Text from '../Text';
import EmptyStateImage from '../../statics/emptyStateImage.png';
import ICShare from '../../statics/share.svg';
import { useAnalytics } from '../../contexts/AnalyticsContext';
import { useAuth } from '../../contexts/AuthContext';
import { renderDefaultObjectsTable, renderPropertiesValues } from '../../utils/renderDefaultObjects';
import Button from '../Button';
import './styles.scss';

const ProductsModal = ({ isOpen, setIsOpen, product, isProfile, statusId }: any): React.ReactElement => {
  const { t, i18n } = useTranslation();
  const { user } = useAuth();
  const history = useHistory();
  const { trackEvent } = useAnalytics();
  const [isMobile, setIsMobile] = React.useState<boolean>(window.innerWidth <= 768);
  const handleWindowSizeChange = (): void => {
    if (window.innerWidth <= 768) {
      setIsMobile(true);
    } else {
      setIsMobile(false);
    }
  };
  React.useEffect(() => {
    window.addEventListener('resize', handleWindowSizeChange);
  }, []);

  const renderDimensions = React.useCallback((value: any): string => {
    const metricsReturn = renderPropertiesValues(value);

    return metricsReturn;
  }, []);

  const renderNameProduct = React.useCallback(
    (value: any): string => {
      if (value) {
        let productLabel;
        if (i18n.language === 'pt-BR') {
          productLabel = value?.name_ptbr;
        } else {
          productLabel = value?.name;
        }
        return productLabel;
      }
      return '';
    },
    [i18n]
  );

  const renderBodyModal = (): React.ReactElement => (
    <Col md={12} className="d-flex gap-2">
      <Col md={4} className="d-flex">
        <Image
          src={product?.image || EmptyStateImage}
          height="218"
          width="260"
          style={{ paddingTop: '16px', paddingLeft: '16px', paddingRight: '6px' }}
        />
      </Col>
      <Col className="modalProduct">
        <h4 className="title">{renderNameProduct(product)}</h4>
        <span className="subTitle">{renderDefaultObjectsTable(product?.specie, t('language'))}</span>
        <Col md={12} className="d-flex mt-2">
          <small className="desc">{t('language') === 'USA' ? product?.description : product?.description_ptbr}</small>
        </Col>
        <Col className="mt-4">
          {renderDimensions(product?.thickness) && (
            <>
              <span>
                {t('labels.labelThicknessProduct')} (mm):{' '}
                <small className="numbers">{renderDimensions(product?.thickness)}</small>
              </span>
              <br />
            </>
          )}
          {renderDimensions(product?.width) && (
            <>
              <span>
                {t('labels.labelWidthProduct')} (mm):{' '}
                <small className="numbers">{renderDimensions(product?.width)}</small>
              </span>
              <br />
            </>
          )}
          {renderDimensions(product?.length) && (
            <span>
              {t('labels.labelLengthProduct')} (mm):{' '}
              <small className="numbers">{renderDimensions(product?.length)}</small>
            </span>
          )}
        </Col>
        {renderDefaultObjectsTable(product?.quality, t('language')) ? (
          <Col md={12} className="d-flex gap-1 mt-3 w-100">
            <span>
              {t('labels.labelQualityProduct')}:{' '}
              <small className="numbers">{renderDefaultObjectsTable(product?.quality, t('language'))}</small>
            </span>
          </Col>
        ) : (
          <></>
        )}
        {renderDefaultObjectsTable(product?.certificates, t('language')) ? (
          <Col md={12} className="d-flex gap-1 ">
            <span>
              {t('labels.labelCertificateProduct')}:{' '}
              <small className="numbers">{renderDefaultObjectsTable(product?.certificates, t('language'))}</small>
            </span>
          </Col>
        ) : (
          <></>
        )}

        {renderDefaultObjectsTable(product?.finishes, t('language')) ? (
          <Col md={12} className="d-flex gap-1">
            <span>
              {t('labels.labelFinishies')}:{' '}
              <small className="numbers">{renderDefaultObjectsTable(product?.finishes, t('language'))}</small>
            </span>
          </Col>
        ) : (
          <></>
        )}
        <div
          onClick={() => {
            history.push(`/suppliers-profile/${product?.company?.uuid}`, {
              id: product?.company?.uuid,
              name: product?.company?.name,
              idQuery: product?.company?.id,
            });
          }}
          role="presentation"
          className="redirect mt-4 mb-2"
          title={`redirect to ${product?.company?.profile?.name}`}
        >
          {!isProfile &&
            (product?.company?.profile?.status_id === 3 ? (
              <div className="d-flex gap-2 shipper">
                {product?.company?.profile?.name}
                <Image src={ICShare} />
              </div>
            ) : (
              ''
            ))}
        </div>
        {user.profileId >= 3 && !statusId && (
          <>
            <div className="mt-2">
              <Button
                className="btnReturn w-10 d-flex align-items-start"
                type="button"
                cy="test-create"
                variant="btn-transparent"
                onClick={() => {
                  history.push('/new-quote', { product });
                  trackEvent('Products - Request quote', {
                    action: `id do produto ${product.id}, nome ${renderNameProduct(product)} e fornecedor ${
                      product?.company?.name
                    }`,
                  });
                }}
              >
                <Text as="b">{t('labels.labelRequestQuote')}</Text>
              </Button>
            </div>
          </>
        )}
      </Col>
    </Col>
  );

  return (
    <Modal
      show={isOpen}
      handleClose={() => setIsOpen(false)}
      title={t('language') === 'USA' ? 'Product Details' : 'Detalhes do produto'}
      size="lg"
      colorIcon
    >
      {isMobile ? (
        <Container className="d-flex mb-4">
          <Row>{renderBodyModal()}</Row>{' '}
        </Container>
      ) : (
        <Container className="d-flex mb-4">{renderBodyModal()}</Container>
      )}
    </Modal>
  );
};

export default ProductsModal;
