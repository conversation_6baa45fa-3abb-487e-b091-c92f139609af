import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useHistory } from 'react-router-dom';
import { Row, Col, Container } from 'react-bootstrap';
import { Formik, Form } from 'formik';
import * as yup from 'yup';
import toastMsg, { ToastType } from '../../../utils/toastMsg';
import { useLoader } from '../../../contexts/LoaderContext';
import Section from '../../../components/Section';
import Button from '../../../components/Button';
import Text from '../../../components/Text';
import ProductService from '../../../services/products.service';
import CompaniesService from '../../../services/companies.service';
import { ISelectOption, ProjectById } from '../../../interfaces';
import RenderCropImage from '../../../utils/renderCropImage';
import renderSelectValues from '../../../utils/renderSelectValues';
import CardIdentifyProduct from './CardIdentify';
import CardOptions from './CardOptions';
import CardMetrics from './CardMetrics';
import { transformObject } from '../../../utils/returnValues';
import { useAuth } from '../../../contexts/AuthContext';
import { stateMetrics } from '../../../utils/setStatesMetrics';
import { updateDimensions } from '../../../utils/updateDimensions';
import 'react-image-crop/dist/ReactCrop.css';
import './styles.scss';

interface IPropsDefaultValues {
  description_ptbr: string;
  description: string;
  name_ptbr: string;
  name: string;
  layers_observation: string;
  observation: string;
}

const defaultValues = {
  description_ptbr: '',
  description: '',
  name_ptbr: '',
  name: '',
  layers_observation: '',
  observation: '',
} as IPropsDefaultValues;

interface IInputFile {
  fileUrl: string;
  fileName: string;
  size?: any;
}

interface IManipulateFile {
  isEditing: boolean;
  isDeleting: boolean;
  fileIndex: number | null;
}

const Actions: React.FunctionComponent = () => {
  const { t } = useTranslation();
  const { setShowLoader } = useLoader();
  const location: any = useLocation();
  const { user } = useAuth();
  const history = useHistory();
  const [formValues, setFormValues] = useState<IPropsDefaultValues>(defaultValues);
  const [typeProduct, setTypeProduct] = useState<ISelectOption>({} as ISelectOption);
  const [companyId, setCompanyId] = useState<string>('');
  const [id, setId] = useState<string>('');
  const [product, setProduct] = useState<ProjectById>();
  const [showCropImageModal, setShowCropImageModal] = useState<boolean>(false);
  //eslint-disable-next-line
  const [newFile, setNewFile] = useState<IInputFile>({} as IInputFile);
  const [files, setFiles] = useState<File[]>([] as File[]);
  const [fileToEdit, setFileToEdit] = useState<string | undefined>('');
  const [speciesProduct, setSpeciesProduct] = useState<ISelectOption[]>([]);
  const [qualitiesProduct, setQualitiesProduct] = useState<ISelectOption[]>([]);
  const [finishesProduct, setFinishesProduct] = useState<ISelectOption[]>([]);
  const [gluesProduct, setGluesProduct] = useState<ISelectOption[]>([]);
  const [certificates, setCertificates] = useState<ISelectOption[]>([]);
  const [layers, setLayers] = useState<string[]>([]);
  const [typeThickness, setTypeThickness] = useState<ISelectOption>({} as ISelectOption);
  const [typeWidth, setTypeWidth] = useState<ISelectOption>({} as ISelectOption);
  const [typeLength, setTypeLength] = useState<ISelectOption>({} as ISelectOption);
  const [visible, setVisible] = React.useState<any>([]);
  const [visibleKeys, setVisibleKeys] = React.useState<any>(true);
  const [thickness, setThickness] = useState({
    list: [],
    range: { min: '', max: '' },
    unique: '',
  });
  const [width, setWidth] = useState({
    list: [],
    range: { min: '', max: '' },
    unique: '',
  });
  const [length, setLength] = useState({
    list: [],
    range: { min: '', max: '' },
    unique: '',
  });

  //eslint-disable-next-line
  const [manipulateFile, setManipulateFile] = useState<IManipulateFile>({
    isEditing: false,
    isDeleting: false,
    fileIndex: 0,
  });

  const handleAddFile = (file: File): void => {
    const newFiles = [...files];
    newFiles.push(file);
    setFiles(newFiles);
  };

  const handleEditFile = (file: File): void => {
    const newFiles = [...files];
    if (manipulateFile.fileIndex) {
      newFiles.splice(manipulateFile.fileIndex, 1, file);

      setFiles(newFiles);
    }
  };

  const selectVisibled = React.useCallback(async (): Promise<ISelectOption> => {
    const optionsTypeAccess: any = [
      {
        value: true,
        label: t('language') === 'USA' ? 'Visible' : 'Visível',
      },
      { value: false, label: t('language') === 'USA' ? 'Occult' : 'Oculto' },
    ];
    return optionsTypeAccess;
  }, [t]);

  React.useEffect(() => {
    let isCleaningUp = false;

    async function loadType(): Promise<void> {
      const res = await selectVisibled();

      if (res && !isCleaningUp) {
        setVisible(res);
      }
    }
    loadType();

    return () => {
      isCleaningUp = true;
    };
  }, [selectVisibled]);

  const createSchema = yup.object().shape({
    length: yup.string().max(255, 'Tamanho máximo 255'),
    width: yup.string().max(255, 'Tamanho máximo 255'),
    thickness: yup.string().max(255, 'Tamanho máximo 255'),
    layers: yup.string().max(255, 'Tamanho máximo 255'),
    description_ptbr: yup.string().min(2).max(255, 'Tamanho máximo 255'),
    description: yup.string().min(2, t('exceptions.invalidDescription')).max(255, 'Tamanho máximo 255'),
  });

  useEffect(() => {
    if (location.pathname.includes('create-company')) {
      setCompanyId(String(user.default_company?.id));
    } else if (location.pathname.includes('update-company')) {
      setCompanyId(String(user.default_company?.id));
      setId(location?.state?.product?.uuid);
    } else if (!location.pathname.includes('create-company') && !location.pathname.includes('update-company')) {
      setId(location?.state?.product?.uuid);
    }
  }, [id, location, user.default_company?.id]);

  const cancel = (): void => {
    setFormValues(defaultValues);
    setTypeProduct({} as ISelectOption);
    setSpeciesProduct([]);
    setQualitiesProduct([]);
    setFinishesProduct([]);
    setGluesProduct([]);
    setLayers([]);
  };

  const productDetail = useCallback(async (): Promise<ProjectById> => {
    try {
      setShowLoader(true);
      const res = await ProductService.getProductById(id);
      if (res) {
        setShowLoader(false);
        return res;
      }
      return {} as ProjectById;
    } catch (error) {
      setShowLoader(false);
      return {} as ProjectById;
    }
  }, [id, setShowLoader]);

  useEffect(() => {
    let isCleaningUp = false;

    async function loadCrudDetail(): Promise<void> {
      const res = await productDetail();

      if (res && !isCleaningUp) {
        setProduct(res);
      }
    }
    if (id) {
      loadCrudDetail();
    }

    return () => {
      isCleaningUp = true;
    };
  }, [productDetail, id]);

  const checkCompany = useCallback((): boolean => {
    const companies = ['128', '101', '12', '316'];
    if (companies.includes(String(user?.default_company?.id) ?? '')) {
      return true;
    }

    return false;
  }, [user]);

  useEffect(() => {
    async function loadTypeProduct(): Promise<void> {
      if (checkCompany()) {
        setTypeProduct({ value: 1, label: t('language') === 'BR' ? 'Compensado' : 'Plywood' });
      }
    }

    loadTypeProduct();
  }, [checkCompany, t]);

  const selectTypes = useCallback(
    async (value: string): Promise<ISelectOption[]> => {
      const res = await ProductService.listTypes(value);
      const options = res.data.map((item: any) => ({
        value: item.id,
        label: t('language') === 'USA' ? item.description : item.description_ptbr,
      }));

      return renderSelectValues(value, options);
    },
    [t]
  );

  const selectSpecies = useCallback(
    async (value: string): Promise<ISelectOption[]> => {
      const res = await ProductService.listSpecies(value, typeProduct?.value ? typeProduct?.value : '');
      const optionsSpecies = res.data.map((item: any) => ({
        value: item.id,
        label: t('language') === 'USA' ? item.description : item.description_ptbr,
      }));

      return renderSelectValues(value, optionsSpecies);
    },
    [t, typeProduct?.value]
  );

  const selectQualities = useCallback(
    async (value: string): Promise<ISelectOption[]> => {
      const res = await ProductService.listQualities(value, typeProduct?.value ? typeProduct?.value : '');
      const optionsQualities = res.data.map((item: any) => ({
        value: item.id,
        label: t('language') === 'USA' ? item.description : item.description_ptbr,
      }));

      return renderSelectValues(value, optionsQualities);
    },
    [t, typeProduct?.value]
  );

  const selectFinishes = useCallback(
    async (value: string): Promise<ISelectOption[]> => {
      const res = await ProductService.listFinishies(value, typeProduct?.value ? typeProduct?.value : '');
      const optionsFinishes = res.data.map((item: any) => ({
        value: item.id,
        label: t('language') === 'USA' ? item.description : item.description_ptbr,
      }));

      return renderSelectValues(value, optionsFinishes);
    },
    [t, typeProduct?.value]
  );

  const selectGlues = useCallback(
    async (value: string): Promise<ISelectOption[]> => {
      const res = await ProductService.listGlues(value, typeProduct?.value ? typeProduct?.value : '');
      const optionsGlues = res.data.map((item: any) => ({
        value: item.id,
        label: t('language') === 'USA' ? item.description : item.description_ptbr,
      }));

      return renderSelectValues(value, optionsGlues);
    },
    [t, typeProduct?.value]
  );

  const selectCertificates = useCallback(
    async (value: string): Promise<ISelectOption[]> => {
      const res = await ProductService.listCertificates(value, typeProduct?.value ? typeProduct?.value : '');
      const optionsCertificates = res.data.map((item: any) => ({
        value: item.id,
        label: t('language') === 'USA' ? item.description : item.description_ptbr,
      }));

      return renderSelectValues(value, optionsCertificates);
    },
    [t, typeProduct?.value]
  );

  const resetStates = (): void => {
    setSpeciesProduct([]);
    setQualitiesProduct([]);
    setFinishesProduct([]);
    setGluesProduct([]);
    setCertificates([]);
  };

  const handleChange = (selectedOption: any): void => {
    setTypeProduct(selectedOption);
    resetStates();
  };

  const selectCompanies = async (value: string): Promise<ISelectOption[]> => {
    const res = await CompaniesService.getCompanies(value.length > 2 ? value : '', 'seller', 1, 20);
    const options = res.data.map((item) => ({
      label: item.name,
      value: item?.id,
    }));

    return renderSelectValues(value, options);
  };

  const selectMetrics = async (value: string): Promise<ISelectOption[]> => {
    const res = [
      {
        name: t('language') === 'USA' ? 'List' : 'Lista',
        id: 'list',
      },
      {
        name: t('language') === 'USA' ? 'Range' : 'Intervalo',
        id: 'range',
      },
    ];
    const options = res.map((item) => ({
      label: item.name,
      value: item?.id,
    }));

    return renderSelectValues(value, options);
  };

  const selectMetricsFiltered = async (value: string): Promise<ISelectOption[]> => {
    const res = [
      {
        name: t('language') === 'USA' ? 'List' : 'Lista',
        id: 'list',
      },
    ];
    const options = res.map((item) => ({
      label: item.name,
      value: item?.id,
    }));

    return renderSelectValues(value, options);
  };

  const isMountedRef = useRef(false);

  const setValuesUpdate = useCallback(async () => {
    const layersUpdate = JSON.stringify(product?.data?.layers);

    setFormValues({
      description_ptbr: product?.data.description_ptbr || '',
      description: product?.data.description || '',
      name: product?.data.name || '',
      name_ptbr: product?.data.name_ptbr || '',
      observation: product?.data.observation || '',
      layers_observation: product?.data?.layers_observation || '',
    });

    setVisibleKeys(product?.data?.visibility !== 0);
    setFileToEdit(product?.data?.image);
    let isCleaningUp = false;

    if (!isMountedRef.current && product?.data && !isCleaningUp) {
      setTypeProduct({
        label: t('language') === 'USA' ? product?.data?.type?.description : product?.data?.type?.description,
        value: product?.data?.type?.id,
      });

      isMountedRef.current = true;
    }

    setCertificates(
      product?.data?.certificates?.map((element: any) => ({
        label: t('language') === 'USA' ? element?.description : element?.description_ptbr,
        value: element?.id,
      })) || []
    );

    setSpeciesProduct(
      product?.data?.specie?.map((element: any) => ({
        label: t('language') === 'USA' ? element?.description : element?.description_ptbr,
        value: element?.id,
      })) || []
    );

    setFinishesProduct(
      product?.data?.finishes?.map((element: any) => ({
        label: t('language') === 'USA' ? element?.description : element?.description_ptbr,
        value: element?.id,
      })) || []
    );

    setQualitiesProduct(
      product?.data?.quality?.map((element: any) => ({
        label: t('language') === 'USA' ? element?.description : element?.description_ptbr,
        value: element?.id,
      })) || []
    );

    setGluesProduct(
      product?.data?.glue?.map((element: any) => ({
        label: t('language') === 'USA' ? element?.description : element?.description_ptbr,
        value: element?.id,
      })) || []
    );

    setLayers(JSON.parse(layersUpdate) || []);

    const thicknessUpdate = product?.data?.thickness;
    const widthUpdate = product?.data?.width;
    const lengthUpdate = product?.data?.length;

    updateDimensions(thicknessUpdate, setThickness, setTypeThickness);
    updateDimensions(widthUpdate, setWidth, setTypeWidth);
    updateDimensions(lengthUpdate, setLength, setTypeLength);

    return () => {
      isCleaningUp = true;
    };
  }, [product, t]);

  useEffect(() => {
    let isCleaningUp = false;

    if (product && !isCleaningUp) {
      setValuesUpdate();
    }
    return () => {
      isCleaningUp = true;
    };
  }, [product, setValuesUpdate]);

  const handleKeyTypeProduct = useCallback(
    (e: Event & { target: HTMLInputElement }) => {
      selectTypes(e.target.value);
    },
    [selectTypes]
  );

  const file2Base64 = (file: File): Promise<string> =>
    new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result ? reader.result.toString() : '');
      reader.onerror = (error) => reject(error);
    });

  function filterArray(arr: any): any {
    return arr.filter((obj: any) => {
      if (obj === null || obj === undefined) {
        return false;
      }
      if (obj.type === 'range' && obj.min === '' && obj.max === '') {
        return false;
      }
      if (obj.type === 'unique' && (obj.value === '' || obj.value === null)) {
        return false;
      }
      if (obj.type === 'list' && (!obj.value || obj.value.length === 0)) {
        return false;
      }
      return true;
    });
  }

  const handleSubmit = async (values: IPropsDefaultValues): Promise<void> => {
    try {
      let base64;
      setShowLoader(true);
      if (files.length) {
        base64 = await file2Base64(files[0]);
      }

      const returnQualities = qualitiesProduct.map((valueId) => Number(valueId.value));
      const returnSpecies = speciesProduct.map((valueId) => Number(valueId.value));
      const returnFinishies = finishesProduct.map((valueId) => Number(valueId.value));
      const returnGlues = gluesProduct.map((valueId) => Number(valueId.value));
      const returnCertificates = certificates.map((valueId) => Number(valueId.value));
      const returnLayers = layers.map((value) => Number(value));

      const newValues: any = {
        ...values,
        description_ptbr: values.description_ptbr || undefined,
        description: values.description || undefined,
        thickness: filterArray(transformObject(thickness)),
        length: filterArray(transformObject(length)),
        width: filterArray(transformObject(width)),
        layers: layers ? returnLayers : [],
        layers_observation: values.layers_observation,
        certificates: returnCertificates || null,
        company_id: user.default_company?.id,
        type_id: typeProduct?.value || null,
        species: returnSpecies || null,
        finishes: returnFinishies || null,
        qualities: returnQualities || null,
        glues: returnGlues || null,
        image: fileToEdit || base64 || null,
        visibility: user?.default_company?.features?.includes('market') ? visibleKeys : false,
      };

      if (id) {
        await ProductService.putProduct(newValues, id);
        toastMsg(ToastType.Success, t('response.editSuccess'));
      } else {
        await ProductService.newProduct(newValues);
        toastMsg(ToastType.Success, t('response.postSuccess'));
      }

      history.push('/products');

      cancel();
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  };

  function stringForNumber(string: any): any {
    if (typeof string !== 'string') {
      return null;
    }
    // eslint-disable-next-line no-param-reassign
    string = string.replace(/\./g, '').replace(',', '.');
    return parseFloat(string);
  }

  const validateTchikness = (): boolean => {
    if (
      typeThickness?.value === 'range' &&
      (!thickness.range.max ||
        !thickness.range.min ||
        stringForNumber(thickness.range.min) >= stringForNumber(thickness.range.max))
    ) {
      return true;
    }

    return false;
  };

  const validateWidth = (): boolean => {
    if (
      typeWidth?.value === 'range' &&
      (!width.range.max || !width.range.min || stringForNumber(width.range.min) >= stringForNumber(width.range.max))
    ) {
      return true;
    }
    if (typeWidth?.value === 'unique' && !width.unique) {
      return true;
    }
    if (typeWidth?.value === 'list' && !width.list.length) {
      return true;
    }
    return false;
  };

  const validateLength = (): boolean => {
    if (
      typeLength?.value === 'range' &&
      (!length.range.max || !length.range.min || stringForNumber(length.range.min) >= stringForNumber(length.range.max))
    ) {
      return true;
    }
    if (typeLength?.value === 'unique' && !length.unique) {
      return true;
    }
    if (typeLength?.value === 'list' && !length.list.length) {
      return true;
    }
    return false;
  };

  useEffect(() => {
    if (!typeProduct?.value && !product) {
      setFinishesProduct([]);
      setSpeciesProduct([]);
      setQualitiesProduct([]);
      setGluesProduct([]);
      setCertificates([]);
    }
    if (typeLength?.value) {
      stateMetrics(setLength, typeLength);
    }
    if (typeWidth?.value) {
      stateMetrics(setWidth, typeWidth);
    }
    if (typeThickness?.value) {
      stateMetrics(setThickness, typeThickness);
    }
  }, [product, typeLength, typeProduct?.value, typeThickness, typeWidth]);

  return (
    <Section className="productsLimit">
      <Text className="pages-title" as="h1" size="2rem" weight={500} color="#203245">
        {id ? t('modal.editProduct') : t('modal.newProduct')}
      </Text>
      <RenderCropImage
        key={newFile.fileUrl}
        image={newFile}
        onSave={manipulateFile.isEditing ? handleEditFile : handleAddFile}
        showModal={showCropImageModal}
        setShowModal={setShowCropImageModal}
      />
      <Container>
        <Formik
          initialValues={formValues}
          validationSchema={createSchema}
          enableReinitialize
          validateOnBlur={false}
          onSubmit={(values) => {
            handleSubmit(values);
          }}
        >
          {({ values, errors, touched, setFieldValue }) => (
            <Form autoComplete="off">
              <Row className="d-flex flex-row justify-content-between align-items-center">
                <CardIdentifyProduct
                  setFieldValue={setFieldValue}
                  companyId={companyId}
                  selectCompanies={selectCompanies}
                  handleKeyTypeProduct={handleKeyTypeProduct}
                  typeProduct={typeProduct}
                  errors={errors}
                  values={values}
                  touched={touched}
                  selectTypes={selectTypes}
                  setTypeProduct={setTypeProduct}
                  setQualitiesProduct={setQualitiesProduct}
                  setSpeciesProduct={setSpeciesProduct}
                  setCertificates={setCertificates}
                  setFinishesProduct={setFinishesProduct}
                  setGluesProduct={setGluesProduct}
                  handleChange={handleChange}
                  resetStates={resetStates}
                  setVisibleKeys={setVisibleKeys}
                  visibleKeys={visibleKeys}
                  visible={visible}
                  checkCompany={checkCompany}
                />
                <CardOptions
                  setFieldValue={setFieldValue}
                  selectSpecies={selectSpecies}
                  setSpeciesProduct={setSpeciesProduct}
                  speciesProduct={speciesProduct}
                  selectQualities={selectQualities}
                  setQualitiesProduct={setQualitiesProduct}
                  qualitiesProduct={qualitiesProduct}
                  selectFinishes={selectFinishes}
                  finishesProduct={finishesProduct}
                  setFinishesProduct={setFinishesProduct}
                  selectGlues={selectGlues}
                  setGluesProduct={setGluesProduct}
                  gluesProduct={gluesProduct}
                  layers={layers}
                  setLayers={setLayers}
                  typeId={typeProduct?.value}
                  selectCertificates={selectCertificates}
                  certificates={certificates}
                  setCertificates={setCertificates}
                />
                <CardMetrics
                  selectMetrics={selectMetrics}
                  setTypeThickness={setTypeThickness}
                  typeThickness={typeThickness}
                  setTypeLength={setTypeLength}
                  typeLength={typeLength}
                  typeWidth={typeWidth}
                  setTypeWidth={setTypeWidth}
                  setThickness={setThickness}
                  thickness={thickness}
                  width={width}
                  setWidth={setWidth}
                  setLength={setLength}
                  length={length}
                  selectMetricsFiltered={selectMetricsFiltered}
                />
                <Col md={12} className="d-flex justify-content-between mb-2 mt-4" style={{ paddingLeft: '0.75rem' }}>
                  <Col className="d-flex justify-content-end gap-2">
                    <Button
                      cy="btn-cancel"
                      type="button"
                      variant="no-color"
                      className="btnReturn"
                      onClick={() => {
                        if (
                          location?.pathname?.includes('update-company') ||
                          location.pathname.includes('create-company')
                        ) {
                          history.push(`/products`);
                        } else {
                          history.push('/products');
                        }
                      }}
                    >
                      {t('buttons.cancel')}
                    </Button>
                    <Button
                      cy="btn-save"
                      type="submit"
                      variant="success"
                      className="btnCreate"
                      disabled={
                        !typeProduct?.value || (values.description && !values.description_ptbr)
                          ? true
                          : !!(false || (values.description_ptbr && !values.description)) ||
                            validateTchikness() ||
                            validateWidth() ||
                            validateLength() ||
                            !typeWidth?.value ||
                            !typeLength?.value ||
                            !speciesProduct.length
                      }
                    >
                      {t('buttons.onlySave')}
                    </Button>
                  </Col>
                </Col>
              </Row>
            </Form>
          )}
        </Formik>
      </Container>
    </Section>
  );
};

export default Actions;
