export const formattedValue = (value: number | string): any => {
  if (typeof value === 'number') {
    return value.toLocaleString('pt-BR', { minimumFractionDigits: 2 });
  }
  const number = parseFloat(value);
  return number.toLocaleString('pt-BR', { minimumFractionDigits: 2 });
};

export function convertNumberValidateInput(str: string): any {
  if (!str) return '';
  if (typeof str === 'number') {
    return formattedValue(str);
  }
  if (str.includes(',')) {
    const num = parseFloat(str?.replace(',', '.'));
    if (Number.isInteger(num)) {
      return Math.floor(num);
    }
  }

  return str;
}

export function convertNumber(str: any, val = false): any {
  if (!str) return '';

  if (typeof str === 'number' && !val) {
    return str;
  }

  const sanitizedStr = str.replace(/\./g, '').replace(/,/g, '.');
  const num = parseFloat(sanitizedStr);

  if (Number.isInteger(num)) {
    return Math.floor(num);
  }

  return num;
}

export function stringForNumber(string: any): any {
  if (typeof string !== 'string') {
    return null;
  }
  string = string.replace(/\./g, '').replace(',', '.');
  return parseFloat(string);
}

export function convertNumberForBusiness(str: any, val = false): any {
  if (!str) return '';

  if (typeof str === 'number' && !val) {
    return str;
  }

  const sanitizedStr = str.replace(/[^\d,.]/g, '');

  const hasComma = sanitizedStr.indexOf(',') !== -1;

  if (hasComma) {
    const lastDotIndex = sanitizedStr.lastIndexOf('.');
    const sanitizedStrWithDot =
      lastDotIndex !== -1 ? sanitizedStr.slice(0, lastDotIndex) + sanitizedStr.slice(lastDotIndex + 1) : sanitizedStr;

    const num = parseFloat(sanitizedStrWithDot.replace(',', '.'));
    return num;
  }
  const num = Number(sanitizedStr);
  return num;
}
