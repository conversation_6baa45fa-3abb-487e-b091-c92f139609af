.dashboard-container {
  padding: 2rem;
  max-width: 1320px;
  margin: 0 auto;
  margin-top: 100px;
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #132632;
}

.supply-chain-steps {
  margin-bottom: 2rem;
  background-color: #ffffff;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.steps-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #132632;
  margin-bottom: 0.5rem;
}

.divider {
  border-top: 1px solid #e0e0e0;
  margin: 1rem 0 2rem 0;
}

.steps-row {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
}

.step-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  max-width: 200px;
}

.step-icon {
  background-color: #5a5a5a;
  color: white;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;

  svg {
    font-size: 1.8rem;
  }
}

.step-arrow {
  position: absolute;
  right: -15px;
  top: 25px;
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  z-index: 1;

  @media (max-width: 768px) {
    display: none;
  }
}

.step-number {
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #132632;
}

.step-description {
  font-size: 0.9rem;
  color: #555;
  margin-bottom: 0.5rem;
}

.step-note {
  font-size: 0.85rem;
  font-style: italic;
  color: #666;
}

.simple-process {
  margin-top: 3rem;
  background-color: #ffffff;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.simple-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #132632;
  margin-bottom: 0.5rem;
}

.simple-description {
  font-size: 0.95rem;
  color: #555;
  line-height: 1.5;
}

// Responsividade
@media (max-width: 992px) {
  .steps-row {
    justify-content: center;
  }

  .step-col {
    flex: 0 0 30%;
    margin-bottom: 2rem;
  }
}

@media (max-width: 768px) {
  .step-col {
    flex: 0 0 45%;
  }
}

@media (max-width: 576px) {
  .step-col {
    flex: 0 0 100%;
  }
}

.cursor-pointer {
  cursor: pointer;
}

.page-title {
  font-size: 2rem;
  font-weight: 500;
  color: rgb(32, 50, 69);
}
