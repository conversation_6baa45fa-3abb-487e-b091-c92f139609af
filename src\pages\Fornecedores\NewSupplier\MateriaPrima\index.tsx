import React, { useState } from 'react';
import { Container, Row, Col, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import CustomInput from '../../../../components/Input/CustomInput';
import CustomButton from '../../../../components/Button';
import { useLoader } from '../../../../contexts/LoaderContext';
import toastMsg, { ToastType } from '../../../../utils/toastMsg';
import FlorestaService, { materiaPrimaProps } from '../../../../services/floresta.service';
import Modal from '../../../../components/Modal';

interface IPropsFMateriaPrima {
  idFornecedor: string;
  closeModal: any;
  atualziarMateriaPrima?: any;
  materiaPrima?: materiaPrimaProps;
}

const CardInfoMateriaPrima = ({
  idFornecedor,
  closeModal,
  atualziarMateriaPrima,
  materiaPrima,
}: IPropsFMateriaPrima): React.ReactElement => {
  const { t } = useTranslation();
  const [show, setShow] = useState<any>({ show: false, idMT: '', titulo: '' });
  const { setShowLoader } = useLoader();
  const [nome, setNome] = useState('');
  const [cientifico, setCientifico] = useState('');

  React.useEffect(() => {
    async function loadDocuments(): Promise<void> {
      setNome(materiaPrima?.nome || '');
      setCientifico(materiaPrima?.nome_cientifico || '');
    }
    if (materiaPrima) {
      loadDocuments();
    }
  }, [setNome, setCientifico]);

  const handleRemoveMateriaPrima = async (uuid: string): Promise<void> => {
    if (!uuid) {
      return;
    }

    try {
      setShowLoader(true);
      await FlorestaService.deleteMateriaPrima(uuid);
      atualziarMateriaPrima();
      setShowLoader(false);
      toastMsg(ToastType.Success, t('response.deleteSuccess'));
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  };

  const handleSubmit = React.useCallback(async () => {
    try {
      setShowLoader(true);

      if (!nome || !cientifico) {
        toastMsg(ToastType.Error, t('messages.errorRequiredFieldsMT'));
        setShowLoader(false);
        return;
      }

      if (materiaPrima) {
        const updateVaue: any = {
          id: materiaPrima.id,
          uuid: materiaPrima.uuid,
          nome: nome,
          nome_cientifico: cientifico,
          status: 'A',
          fornecedor_id: idFornecedor,
        };

        await FlorestaService.updateMateriaPrima(updateVaue);
      } else {
        const newValues: any = {
          nome: nome,
          nome_cientifico: cientifico,
          status: 'A',
          fornecedor_id: idFornecedor,
        };

        await FlorestaService.saveMateriaPrima(newValues);
      }
      toastMsg(ToastType.Success, t('response.saveSuccess'));
      atualziarMateriaPrima();
      closeModal(false);
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  }, [nome, cientifico, setShowLoader, t]);

  return (
    <>
      <Modal
        show={show.show}
        handleClose={() => setShow({ ...show, show: false })}
        title={t('modal.removeFlorestaTitle')}
        size="lg"
        className="styleModalConfirm"
      >
        <Container>
          <Row className="mt-2 p-2">
            <Col className="d-flex align-items-center ">
              <h6>
                <br />
                {t('modal.removerMaterialPrima') + show.titulo + '?'}
                <br />
                {t('modal.removeProductDescription')}
              </h6>
            </Col>
          </Row>
        </Container>

        <Row className="mt-2">
          <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
            <CustomButton
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={() => {
                setShow(false);
              }}
            >
              {t('buttons.cancel')}
            </CustomButton>
            <CustomButton
              cy="btn-save"
              type="button"
              variant="danger"
              onClick={() => handleRemoveMateriaPrima(show.idMT)}
            >
              {t('buttons.delete')}
            </CustomButton>
          </Col>
        </Row>
      </Modal>
      <div id={materiaPrima?.uuid}>
        <Row style={{ marginLeft: 'auto' }}>
          <Col md={12}>
            <CustomInput
              cy={'test-nome' + materiaPrima?.uuid}
              id={'nome' + materiaPrima?.uuid}
              name={'nome' + materiaPrima?.uuid}
              label={'Nome *'}
              placeholder={'Nome da Matéria prima'}
              value={nome}
              onChange={(e) => {
                setNome?.(e.target.value);
              }}
              type="text"
              maxLength={255}
            />
          </Col>
          <Col md={12}>
            <CustomInput
              cy={'test-cientifico' + materiaPrima?.uuid}
              id={'cientifico' + materiaPrima?.uuid}
              name={'cientifico' + materiaPrima?.uuid}
              label={'Nome científico *'}
              placeholder={'Nome científico'}
              value={cientifico}
              onChange={(e) => {
                setCientifico?.(e.target.value);
              }}
              type="text"
              maxLength={255}
            />
          </Col>
        </Row>
      </div>
      <div className="d-flex justify-content-end" style={{ marginTop: '20px' }}>
        <Button variant="outline-secondary" onClick={handleSubmit}>
          {t('buttons.materiaPimaSave')}
        </Button>
      </div>
    </>
  );
};

export default CardInfoMateriaPrima;
