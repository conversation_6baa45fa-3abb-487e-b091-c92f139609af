import React from 'react';
import classNames from 'classnames';
import { Field } from 'formik';
import Text from '../Text';

interface IProp {
  cy: string;
  isInvalid?: boolean;
  msg?: string;
  className?: string;
  label?: string;
  id: string;
  name: string;
  as: string;
  placeholder?: string;
  defaultValue?: string;
  value?: string | number;
  maxLength?: number;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onChange?: React.ChangeEventHandler<HTMLInputElement> | undefined;
  disabled?: boolean;
}

const Input = ({
  cy,
  isInvalid,
  msg,
  className,
  label,
  id,
  name,
  as,
  placeholder,
  value,
  defaultValue,
  onBlur,
  maxLength,
  onChange,
  disabled,
}: IProp): React.ReactElement => (
  <label htmlFor={id} className="w-100">
    {label}
    <Field
      disabled={disabled}
      onChange={onChange}
      key={defaultValue ? id : null}
      maxLength={maxLength}
      cy={cy}
      id={id}
      as={as}
      value={value}
      name={name}
      defaultValue={defaultValue || ''}
      placeholder={placeholder}
      className={classNames(`form-control ${isInvalid ? 'is-invalid' : ''} ${className}`)}
      onBlur={onBlur}
    />
    {isInvalid ? (
      <Text as="span" color="var(--red-500)" weight={500}>
        {msg}
      </Text>
    ) : null}
  </label>
);

Input.defaultProps = { isInvalid: false, msg: '', className: '', label: '', placeholder: '' };

export default Input;
