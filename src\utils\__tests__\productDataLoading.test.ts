// Teste para validar o carregamento de dados do produto

describe('Product Data Loading', () => {
  describe('loadSupplyChainProduct function logic', () => {
    // Simular a lógica da função loadSupplyChainProduct
    const processProductData = (backendData: any) => {
      if (backendData?.data != undefined) {
        const quantity = backendData?.data?.product_quantity?.replace(/\.(?=[^.]*$)/, ',');
        const quantityTons = backendData?.data?.product_quantity_tons?.replace(/\.(?=[^.]*$)/, ',') || '';
        const productDescription = backendData?.data?.product_description;
        
        const productData = {
          id: backendData?.data?.id,
          uuid: backendData?.data?.uuid,
          status: backendData?.data?.status,
          supply_chain_id: backendData?.data?.supply_chain_id,
          product_description: backendData?.data?.product_description,
          product_quantity: backendData?.data?.product_quantity,
          product_quantity_tons: backendData?.data?.product_quantity_tons,
        };

        return {
          quantity,
          quantityTons,
          productDescription,
          productData
        };
      }
      return null;
    };

    it('should load product data with both m³ and tons quantities', () => {
      const mockBackendResponse = {
        data: {
          id: '1',
          uuid: 'test-uuid',
          status: 'A',
          supply_chain_id: 'sc-1',
          product_description: 'Teste produto',
          product_quantity: '252.252',
          product_quantity_tons: '150.500'
        }
      };

      const result = processProductData(mockBackendResponse);

      expect(result).not.toBeNull();
      expect(result?.quantity).toBe('252,252');
      expect(result?.quantityTons).toBe('150,500');
      expect(result?.productDescription).toBe('Teste produto');
      expect(result?.productData.product_quantity_tons).toBe('150.500');
    });

    it('should handle product data with only m³ quantity', () => {
      const mockBackendResponse = {
        data: {
          id: '1',
          uuid: 'test-uuid',
          status: 'A',
          supply_chain_id: 'sc-1',
          product_description: 'Teste produto',
          product_quantity: '252.252',
          product_quantity_tons: null
        }
      };

      const result = processProductData(mockBackendResponse);

      expect(result).not.toBeNull();
      expect(result?.quantity).toBe('252,252');
      expect(result?.quantityTons).toBe('');
      expect(result?.productData.product_quantity_tons).toBeNull();
    });

    it('should handle product data with only tons quantity', () => {
      const mockBackendResponse = {
        data: {
          id: '1',
          uuid: 'test-uuid',
          status: 'A',
          supply_chain_id: 'sc-1',
          product_description: 'Teste produto',
          product_quantity: null,
          product_quantity_tons: '150.500'
        }
      };

      const result = processProductData(mockBackendResponse);

      expect(result).not.toBeNull();
      expect(result?.quantityTons).toBe('150,500');
      expect(result?.productData.product_quantity_tons).toBe('150.500');
    });

    it('should handle missing product_quantity_tons field', () => {
      const mockBackendResponse = {
        data: {
          id: '1',
          uuid: 'test-uuid',
          status: 'A',
          supply_chain_id: 'sc-1',
          product_description: 'Teste produto',
          product_quantity: '252.252'
          // product_quantity_tons field missing
        }
      };

      const result = processProductData(mockBackendResponse);

      expect(result).not.toBeNull();
      expect(result?.quantity).toBe('252,252');
      expect(result?.quantityTons).toBe('');
      expect(result?.productData.product_quantity_tons).toBeUndefined();
    });

    it('should return null for undefined data', () => {
      const mockBackendResponse = {
        data: undefined
      };

      const result = processProductData(mockBackendResponse);

      expect(result).toBeNull();
    });
  });
});
