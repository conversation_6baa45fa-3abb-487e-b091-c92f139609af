# Guia de Implementação - Campo "Total em Toneladas"

## Visão Geral

Este documento fornece um guia passo a passo para implementar os novos campos de quantidade em toneladas nas telas de Cadeia de Fornecimento e Espécies de Madeira Utilizada, mantendo os campos existentes em metros cúbicos.

## Pré-requisitos

- Node.js e npm/yarn instalados
- Acesso ao banco de dados
- Ambiente de desenvolvimento configurado
- Conhecimento em TypeScript, React e APIs REST

## Fase 1: Backend - Estrutura de Dados

### 1.1 Atualização das Interfaces TypeScript

**Arquivo: `src/services/supplyChain.service.ts`**

```typescript
// Adicionar novos campos às interfaces existentes
export interface IPropsSaveProductSupplyChain {
  product_description: string;
  product_quantity: string; // Mantém m³
  product_quantity_tons?: string; // NOVO: Quantidade em toneladas
  status: 'A';
  supply_chain_id: string;
}

export interface IPropsSaveSupplyChainSpecie {
  id?: string;
  uuid?: string;
  mp_qtde: string; // Mantém m³
  mp_qtde_tons?: string; // NOVO: Quantidade MP em toneladas
  mp_nome: string;
  floresta_nome: string;
  fornecedor_nome: string;
  supply_chain_product_id: string;
  floresta_id: string;
  materia_prima_id: string;
  fornecedor_id: string;
}
```

**Arquivo: `src/services/floresta.service.ts`**

```typescript
export interface IpropsMateriaPrima {
  id?: number;
  nome: string;
  nome_cientifico: string;
  quantidade?: string; // Mantém m³
  quantidade_tons?: string; // NOVO: Quantidade em toneladas
  status?: string;
}
```

### 1.2 Atualização dos Métodos de Serviço

**Arquivo: `src/services/supplyChain.service.ts`**

```typescript
// Atualizar métodos existentes para suportar novos campos
static async saveProductSupplyChain(product: IPropsSaveProductSupplyChain): Promise<any> {
    const { data } = await HttpClient.api.post('/supplychain/product', {
        ...product,
        // Garantir que novos campos sejam enviados
        product_quantity_tons: product.product_quantity_tons || null
    });
    return data.data;
}

static async updateProductSupplyChain(uuid: string, product: any): Promise<any> {
    const { data } = await HttpClient.api.put(`/supplychain/product/${uuid}`, {
        ...product,
        product_quantity_tons: product.product_quantity_tons || null
    });
    return data.data;
}
```

## Fase 2: Frontend - Componentes de Interface

### 2.1 Atualização das Traduções

**Arquivo: `src/translate/languages/pt.ts`**

```typescript
// Adicionar novas chaves de tradução na seção labels
labels: {
    // ... labels existentes
    productQuantityM3: 'Quantidade do Produto (m³)',
    productQuantityTons: 'Quantidade do Produto (toneladas)', // NOVO
    quantityMatPrimaTons: 'Quantidade da Matéria-Prima (toneladas)', // NOVO
    conversionHelper: 'Conversão automática baseada na densidade da madeira', // NOVO
    quantityRequired: 'Pelo menos uma quantidade deve ser preenchida', // NOVO
}
```

**Arquivo: `src/translate/languages/en.ts`**

```typescript
// Adicionar traduções em inglês
labels: {
    // ... labels existentes
    productQuantityM3: 'Product Quantity (m³)',
    productQuantityTons: 'Product Quantity (tons)', // NOVO
    quantityMatPrimaTons: 'Raw Material Quantity (tons)', // NOVO
    conversionHelper: 'Automatic conversion based on wood density', // NOVO
    quantityRequired: 'At least one quantity must be filled', // NOVO
}
```

### 2.2 Componente de Input para Toneladas

**Arquivo: `src/components/Input/QuantityInput/index.tsx`** (NOVO)

```typescript
import React from 'react';
import MaskedInput from 'react-text-mask';
import { Col } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';

interface IQuantityInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  colSize?: number;
  cy?: string;
}

const QuantityInput: React.FC<IQuantityInputProps> = ({
  label,
  value,
  onChange,
  placeholder,
  disabled = false,
  colSize = 4,
  cy = 'quantity-input',
}) => {
  const { t } = useTranslation();

  const quantityMask = (rawValue: string) => {
    const digits = rawValue.replace(/[^\d]/g, '');
    const decimalPart = [/\d/, /\d/, /\d/]; // 3 casas decimais

    const integerPart = [];
    for (let i = 0; i < digits.length - 3; i++) {
      integerPart.push(/\d/);
      if ((digits.length - 3 - i) % 3 === 1 && i < digits.length - 4) {
        integerPart.push('.');
      }
    }

    return [...integerPart, ',', ...decimalPart];
  };

  return (
    <Col md={colSize} className="mt-2">
      <label htmlFor={cy}>{label}</label>
      <MaskedInput
        value={value}
        onChange={(e) => onChange(e.target.value)}
        mask={quantityMask}
        guide={false}
        placeholderChar={'\u2000'}
        placeholder={placeholder}
        className="form-control"
        disabled={disabled}
        data-cy={cy}
      />
    </Col>
  );
};

export default QuantityInput;
```

### 2.3 Atualização da Tela de Produtos

**Arquivo: `src/pages/SupplyChain/New/Produto/index.tsx`**

```typescript
// Adicionar novos estados
const [quantityTons, setQuantityTons] = useState<string>('');

// Atualizar função de submit
const handleSubmitProduct = React.useCallback(async () => {
  try {
    setShowLoader(true);

    // Validação: pelo menos um campo deve ser preenchido
    if (!quantity && !quantityTons) {
      toastMsg(ToastType.Error, t('labels.quantityRequired'));
      setShowLoader(false);
      return;
    }

    if (supplyChainProductSave?.id) {
      const productUpdate: any = {
        id: supplyChainProductSave?.id,
        product_description: productDescription,
        product_quantity: quantity.replace(',', '.'),
        product_quantity_tons: quantityTons.replace(',', '.'), // NOVO
      };

      const productSave = await SupplyChainService.updateProductSupplyChain(supplyChainProductSave.uuid, productUpdate);
      setSupplyChainProductSave(productSave);
    } else {
      const newProduct: any = {
        product_description: productDescription,
        product_quantity: quantity.replace(',', '.'),
        product_quantity_tons: quantityTons.replace(',', '.'), // NOVO
        status: 'A',
        supply_chain_id: supplyChain.supply_chain_id,
      };

      const productSave: any = await SupplyChainService.saveProductSupplyChain(newProduct);
      setSupplyChainProductSave(productSave);
    }

    // ... resto da função
  } catch (error) {
    // ... tratamento de erro
  }
}, [productDescription, quantity, quantityTons, supplyChain, supplyChainProductSave]);

// Adicionar novos campos no JSX (após o campo existente de m³)
<Row>
  <Col md={4} className="mt-2">
    <label htmlFor="quantity-m3">{t('labels.productQuantityM3')}</label>
    <MaskedInput
      value={quantity}
      onChange={(e) => setQuantity(e.target.value)}
      // ... resto das props existentes
    />
  </Col>

  {/* NOVO CAMPO */}
  <Col md={4} className="mt-2">
    <label htmlFor="quantity-tons">{t('labels.productQuantityTons')}</label>
    <MaskedInput
      value={quantityTons}
      onChange={(e) => setQuantityTons(e.target.value)}
      mask={(rawValue) => {
        const digits = rawValue.replace(/[^\d]/g, '');
        const decimalPart = [/\d/, /\d/, /\d/];
        const integerPart = [];
        for (let i = 0; i < digits.length - 3; i++) {
          integerPart.push(/\d/);
          if ((digits.length - 3 - i) % 3 === 1 && i < digits.length - 4) {
            integerPart.push('.');
          }
        }
        return [...integerPart, ',', ...decimalPart];
      }}
      guide={false}
      placeholderChar={'\u2000'}
      placeholder={t('labels.productQuantityTons')}
      className="form-control"
      data-cy="quantity-tons"
    />
  </Col>

  <Col md={4} className="mt-4" style={{ display: 'flex', justifyContent: 'flex-end' }}>
    {/* Botão existente */}
  </Col>
</Row>;
```

### 2.4 Atualização da Tela de Espécies de Madeira

**Arquivo: `src/pages/SupplyChain/New/Specie/index.tsx`**

```typescript
// Adicionar estado para quantidades em toneladas
const [materiasPrimaTons, setMateriasPrimaTons] = useState<IpropsMateriaPrima[]>([]);

// Função para lidar com mudanças nas quantidades em toneladas
const handleChangeMateriaPrimaTons = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
  const newMateriaPrima = [...materiasPrimaTons];
  if (newMateriaPrima[index]) {
    newMateriaPrima[index].quantidade_tons = event.target.value;
    setMateriasPrimaTons(newMateriaPrima);
  }
};

// Atualizar função de carregamento para incluir toneladas
async function loadQuantidadeMP(): Promise<void> {
  const resMT = await recuperarMateriasPrima(supplyChainSpecie?.floresta?.id);
  const res = await getSuppliChainSpecieMP(supplyChainSpecie?.id);

  if (res) {
    const newMateriaPrimas: IpropsMateriaPrima[] = resMT.reduce((acc: IpropsMateriaPrima[], mp: any) => {
      const foundMateria = res.find((item: any) => item.materia_prima_id === mp.id);
      if (foundMateria) {
        acc.push({
          id: mp.id,
          nome: mp.nome,
          nome_cientifico: mp.nome_cientifico,
          quantidade: foundMateria.mp_qtde,
          quantidade_tons: foundMateria.mp_qtde_tons || '', // NOVO
          status: 'A',
        });
      } else {
        acc.push({
          id: mp.id,
          nome: mp.nome,
          nome_cientifico: mp.nome_cientifico,
          quantidade: '',
          quantidade_tons: '', // NOVO
          status: 'A',
        });
      }
      return acc;
    }, []);

    setMateriasPrima(newMateriaPrimas);
    setMateriasPrimaTons(newMateriaPrimas); // NOVO
  } else {
    setMateriasPrima(resMT);
    setMateriasPrimaTons(resMT.map((mp) => ({ ...mp, quantidade_tons: '' }))); // NOVO
  }
}

// Adicionar validação na função de submit
const handleSubmit = React.useCallback(async () => {
  try {
    setShowLoader(true);

    // Validar se pelo menos uma quantidade foi preenchida para cada matéria-prima
    const hasValidQuantities = materiasPrima.every((mp, index) => {
      const qtdM3 = mp.quantidade?.trim();
      const qtdTons = materiasPrimaTons[index]?.quantidade_tons?.trim();
      return qtdM3 || qtdTons;
    });

    if (!hasValidQuantities) {
      toastMsg(ToastType.Error, t('labels.quantityRequired'));
      setShowLoader(false);
      return;
    }

    // ... resto da lógica de submit incluindo novos campos
  } catch (error) {
    // ... tratamento de erro
  }
}, [materiasPrima, materiasPrimaTons /* outras dependências */]);
```

**Atualização do JSX para incluir campos de toneladas:**

```typescript
{
  materiasPrima?.map((mp, index) => (
    <Row key={mp.id}>
      <Col md={3} className="mt-2">
        <CustomInput
          cy={'test-materiaPrima' + mp.id}
          id={'materiaPrima' + mp.id}
          name={'materiaPrima' + mp.id}
          value={mp.nome}
          type="text"
          disabled
          maxLength={255}
        />
      </Col>
      <Col md={3} className="mt-2">
        <CustomInput
          cy={'test-cientifico' + mp.id}
          id={'cientifico' + mp.id}
          name={'cientifico' + mp.id}
          value={mp.nome_cientifico}
          type="text"
          disabled
          maxLength={255}
        />
      </Col>
      <Col md={2} className="mt-2">
        <label>{t('labels.productQuantityM3')}</label>
        <MaskedInput
          value={mp.quantidade}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChangeMateriaPrima(index, e)}
          mask={quantityMask}
          guide={false}
          placeholderChar={'\u2000'}
          placeholder={t('labels.productQuantityM3')}
          className="form-control"
        />
      </Col>
      {/* NOVO CAMPO TONELADAS */}
      <Col md={2} className="mt-2">
        <label>{t('labels.quantityMatPrimaTons')}</label>
        <MaskedInput
          value={materiasPrimaTons[index]?.quantidade_tons || ''}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChangeMateriaPrimaTons(index, e)}
          mask={quantityMask}
          guide={false}
          placeholderChar={'\u2000'}
          placeholder={t('labels.quantityMatPrimaTons')}
          className="form-control"
        />
      </Col>
      <Col md={2} className="mt-2">
        {/* Botões de ação existentes */}
      </Col>
    </Row>
  ));
}
```

## Fase 3: Validações e Utilitários

### 3.1 Função de Conversão (Opcional)

**Arquivo: `src/utils/woodConversion.ts`** (NOVO)

```typescript
// Densidades médias por espécie de madeira (kg/m³)
const WOOD_DENSITIES: { [key: string]: number } = {
  eucalipto: 600,
  pinus: 450,
  cedro: 400,
  mogno: 650,
  default: 500, // Densidade padrão
};

export const convertM3ToTons = (volumeM3: string, species?: string): string => {
  const volume = parseFloat(volumeM3.replace(',', '.'));
  if (isNaN(volume)) return '';

  const density = WOOD_DENSITIES[species?.toLowerCase() || 'default'];
  const tons = (volume * density) / 1000; // Converter kg para toneladas

  return tons.toFixed(3).replace('.', ',');
};

export const convertTonsToM3 = (weightTons: string, species?: string): string => {
  const weight = parseFloat(weightTons.replace(',', '.'));
  if (isNaN(weight)) return '';

  const density = WOOD_DENSITIES[species?.toLowerCase() || 'default'];
  const volume = (weight * 1000) / density; // Converter toneladas para kg

  return volume.toFixed(3).replace('.', ',');
};

export const validateQuantityConsistency = (
  volumeM3: string,
  weightTons: string,
  species?: string,
  tolerance: number = 0.1
): boolean => {
  if (!volumeM3 || !weightTons) return true; // Se um está vazio, não validar

  const expectedTons = convertM3ToTons(volumeM3, species);
  const actualTons = parseFloat(weightTons.replace(',', '.'));
  const expectedTonsNum = parseFloat(expectedTons.replace(',', '.'));

  const difference = Math.abs(actualTons - expectedTonsNum);
  const percentageDiff = difference / expectedTonsNum;

  return percentageDiff <= tolerance;
};
```

### 3.2 Hook Customizado para Quantidades

**Arquivo: `src/hooks/useQuantityFields.ts`** (NOVO)

```typescript
import { useState, useCallback } from 'react';
import { convertM3ToTons, convertTonsToM3, validateQuantityConsistency } from '../utils/woodConversion';

interface UseQuantityFieldsProps {
  initialM3?: string;
  initialTons?: string;
  species?: string;
  autoConvert?: boolean;
}

export const useQuantityFields = ({
  initialM3 = '',
  initialTons = '',
  species,
  autoConvert = false,
}: UseQuantityFieldsProps) => {
  const [quantityM3, setQuantityM3] = useState(initialM3);
  const [quantityTons, setQuantityTons] = useState(initialTons);
  const [isValid, setIsValid] = useState(true);

  const handleM3Change = useCallback(
    (value: string) => {
      setQuantityM3(value);

      if (autoConvert && value && !quantityTons) {
        const convertedTons = convertM3ToTons(value, species);
        setQuantityTons(convertedTons);
      }

      if (value && quantityTons) {
        const valid = validateQuantityConsistency(value, quantityTons, species);
        setIsValid(valid);
      } else {
        setIsValid(true);
      }
    },
    [quantityTons, species, autoConvert]
  );

  const handleTonsChange = useCallback(
    (value: string) => {
      setQuantityTons(value);

      if (autoConvert && value && !quantityM3) {
        const convertedM3 = convertTonsToM3(value, species);
        setQuantityM3(convertedM3);
      }

      if (quantityM3 && value) {
        const valid = validateQuantityConsistency(quantityM3, value, species);
        setIsValid(valid);
      } else {
        setIsValid(true);
      }
    },
    [quantityM3, species, autoConvert]
  );

  const reset = useCallback(() => {
    setQuantityM3('');
    setQuantityTons('');
    setIsValid(true);
  }, []);

  const hasValue = quantityM3 || quantityTons;

  return {
    quantityM3,
    quantityTons,
    isValid,
    hasValue,
    handleM3Change,
    handleTonsChange,
    reset,
  };
};
```

## Fase 4: Testes

### 4.1 Testes Unitários

**Arquivo: `src/utils/__tests__/woodConversion.test.ts`** (NOVO)

```typescript
import { convertM3ToTons, convertTonsToM3, validateQuantityConsistency } from '../woodConversion';

describe('woodConversion', () => {
  describe('convertM3ToTons', () => {
    it('should convert m³ to tons correctly', () => {
      expect(convertM3ToTons('1,000')).toBe('0,500');
      expect(convertM3ToTons('2,500', 'eucalipto')).toBe('1,500');
    });

    it('should handle invalid input', () => {
      expect(convertM3ToTons('')).toBe('');
      expect(convertM3ToTons('invalid')).toBe('');
    });
  });

  describe('convertTonsToM3', () => {
    it('should convert tons to m³ correctly', () => {
      expect(convertTonsToM3('0,500')).toBe('1,000');
      expect(convertTonsToM3('1,500', 'eucalipto')).toBe('2,500');
    });
  });

  describe('validateQuantityConsistency', () => {
    it('should validate consistent quantities', () => {
      expect(validateQuantityConsistency('1,000', '0,500')).toBe(true);
      expect(validateQuantityConsistency('1,000', '0,600')).toBe(false);
    });

    it('should return true for empty values', () => {
      expect(validateQuantityConsistency('', '0,500')).toBe(true);
      expect(validateQuantityConsistency('1,000', '')).toBe(true);
    });
  });
});
```

### 4.2 Testes de Componente

**Arquivo: `src/components/Input/__tests__/QuantityInput.test.tsx`** (NOVO)

```typescript
import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../translate/i18n';
import QuantityInput from '../QuantityInput';

const renderWithI18n = (component: React.ReactElement) => {
  return render(<I18nextProvider i18n={i18n}>{component}</I18nextProvider>);
};

describe('QuantityInput', () => {
  it('should render correctly', () => {
    const mockOnChange = jest.fn();

    renderWithI18n(<QuantityInput label="Test Quantity" value="" onChange={mockOnChange} cy="test-quantity" />);

    expect(screen.getByLabelText('Test Quantity')).toBeInTheDocument();
  });

  it('should call onChange when value changes', () => {
    const mockOnChange = jest.fn();

    renderWithI18n(<QuantityInput label="Test Quantity" value="" onChange={mockOnChange} cy="test-quantity" />);

    const input = screen.getByLabelText('Test Quantity');
    fireEvent.change(input, { target: { value: '1,000' } });

    expect(mockOnChange).toHaveBeenCalledWith('1,000');
  });
});
```

## Fase 5: Migração e Deploy

### 5.1 Script de Migração de Banco

```sql
-- Adicionar colunas para quantidade em toneladas
ALTER TABLE supply_chain_products
ADD COLUMN product_quantity_tons DECIMAL(15,3) NULL
COMMENT 'Quantidade do produto em toneladas';

ALTER TABLE supply_chain_species
ADD COLUMN mp_qtde_tons DECIMAL(15,3) NULL
COMMENT 'Quantidade da matéria-prima em toneladas';

ALTER TABLE materia_prima
ADD COLUMN quantidade_tons DECIMAL(15,3) NULL
COMMENT 'Quantidade da matéria-prima em toneladas';

-- Índices para performance (opcional)
CREATE INDEX idx_supply_chain_products_quantity_tons
ON supply_chain_products(product_quantity_tons);

CREATE INDEX idx_supply_chain_species_qtde_tons
ON supply_chain_species(mp_qtde_tons);

-- Verificar integridade dos dados após migração
SELECT
  COUNT(*) as total_products,
  COUNT(product_quantity) as with_m3,
  COUNT(product_quantity_tons) as with_tons
FROM supply_chain_products;
```

### 5.2 Atualização das APIs Backend

**Exemplo de endpoint atualizado:**

```php
// Controller: SupplyChainController.php
public function updateProduct(Request $request, $uuid)
{
    $validated = $request->validate([
        'product_description' => 'required|string|max:255',
        'product_quantity' => 'nullable|numeric|min:0',
        'product_quantity_tons' => 'nullable|numeric|min:0',
    ]);

    // Validação: pelo menos uma quantidade deve ser preenchida
    if (empty($validated['product_quantity']) && empty($validated['product_quantity_tons'])) {
        return response()->json([
            'message' => 'Pelo menos uma quantidade deve ser preenchida'
        ], 422);
    }

    $product = SupplyChainProduct::where('uuid', $uuid)->firstOrFail();
    $product->update($validated);

    return response()->json(['data' => $product]);
}
```

### 5.3 Checklist de Deploy

#### Backend

- [ ] **Migrations executadas**

  - [ ] Colunas adicionadas às tabelas
  - [ ] Índices criados
  - [ ] Dados existentes preservados

- [ ] **APIs atualizadas**

  - [ ] Endpoints de criação suportam novos campos
  - [ ] Endpoints de atualização suportam novos campos
  - [ ] Validações implementadas
  - [ ] Documentação da API atualizada

- [ ] **Testes Backend**
  - [ ] Testes unitários dos controllers
  - [ ] Testes de integração das APIs
  - [ ] Testes de validação

#### Frontend

- [ ] **Interfaces TypeScript**

  - [ ] Interfaces de serviço atualizadas
  - [ ] Tipos de dados atualizados
  - [ ] Imports corrigidos

- [ ] **Componentes**

  - [ ] Campos de toneladas implementados
  - [ ] Validações client-side funcionando
  - [ ] Máscaras de input aplicadas
  - [ ] Responsividade testada

- [ ] **Traduções**

  - [ ] Labels em português adicionadas
  - [ ] Labels em inglês adicionadas
  - [ ] Mensagens de erro traduzidas

- [ ] **Testes Frontend**
  - [ ] Testes unitários dos componentes
  - [ ] Testes de integração
  - [ ] Testes E2E dos fluxos principais

### 5.4 Plano de Rollback

Em caso de problemas, seguir os passos:

1. **Rollback do Frontend**

   ```bash
   # Reverter para versão anterior
   git revert <commit-hash>
   npm run build
   npm run deploy
   ```

2. **Rollback do Backend**
   ```sql
   -- Remover colunas adicionadas (CUIDADO: perda de dados)
   ALTER TABLE supply_chain_products DROP COLUMN product_quantity_tons;
   ALTER TABLE supply_chain_species DROP COLUMN mp_qtde_tons;
   ALTER TABLE materia_prima DROP COLUMN quantidade_tons;
   ```

## Fase 6: Monitoramento e Manutenção

### 6.1 Métricas a Acompanhar

- **Uso dos Campos**

  - Percentual de registros com apenas m³
  - Percentual de registros com apenas toneladas
  - Percentual de registros com ambos os campos

- **Performance**

  - Tempo de resposta das APIs
  - Tempo de carregamento das telas
  - Uso de memória

- **Erros**
  - Erros de validação
  - Inconsistências entre m³ e toneladas
  - Falhas de conversão

### 6.2 Logs Importantes

```typescript
// Exemplo de log para conversões
console.log('Conversion performed', {
  from: 'M3',
  to: 'TONS',
  originalValue: volumeM3,
  convertedValue: tons,
  species: species,
  timestamp: new Date().toISOString(),
});
```

## Considerações Finais

### Pontos de Atenção

1. **Compatibilidade**: Manter campos m³ como primários para não quebrar funcionalidades existentes
2. **Validação**: Implementar validações progressivamente, começando com avisos
3. **Performance**: Monitorar impacto das novas colunas nas consultas
4. **Usabilidade**: Coletar feedback dos usuários sobre a experiência

### Melhorias Futuras

1. **Conversão Automática**: Implementar conversão baseada na espécie de madeira
2. **Relatórios**: Adicionar campos de toneladas aos relatórios existentes
3. **Importação**: Suportar importação de dados em ambas as unidades
4. **Validação Avançada**: Alertas para inconsistências entre m³ e toneladas

### Cronograma Sugerido

- **Semana 1-2**: Implementação Backend (Fases 1)
- **Semana 3-4**: Implementação Frontend (Fase 2)
- **Semana 5**: Validações e Utilitários (Fase 3)
- **Semana 6**: Testes e Correções (Fase 4)
- **Semana 7**: Deploy e Monitoramento (Fase 5-6)

### Recursos Necessários

- **Desenvolvedor Backend**: 2-3 semanas
- **Desenvolvedor Frontend**: 3-4 semanas
- **QA/Tester**: 1-2 semanas
- **DevOps**: 0.5 semana para deploy

### Conclusão

A implementação dos campos de toneladas seguindo este guia garantirá:

- ✅ Funcionalidade completa e robusta
- ✅ Compatibilidade com sistema existente
- ✅ Experiência de usuário consistente
- ✅ Código testado e maintível
- ✅ Deploy seguro e monitorado

O sucesso da implementação dependerá do seguimento rigoroso das fases propostas e do feedback contínuo dos usuários finais.
