import { IGrupoDocumentoFlorestaSave } from "./documentos.service";
import HttpClient from "./httpClient";

export interface IFloresta {
    id?: number;
    uuid?: string;
    nome?: string;
    identificacao?: string;
    latitude?: string;
    longitude?: string;
    pais_origem?: string;
    status?: string;
    fornecedor_id?: number;
    materia_prima?: IpropsMateriaPrima[];
}

export interface IpropsMateriaPrima {
    id?: number;
    nome: string;
    nome_cientifico: string;
    quantidade?: string; // Mantém m³
    quantidade_tons?: string; // NOVO: Quantidade em toneladas
    status?: string;
}

export interface IFlorestaExport {
    uuid: string;
    id: string;
    nome: string;
    identificacao: string;
    pais_origem: string;
    status: string;
    fornecedor: string;
    coordenadas: string;
    car?: IFlorestaDocumento | null;
}

export interface IDocumento {
    id: number;
    uuid: string;
    nome: string;
    documento_path: string;
    extensao: string;
    descricao: string;
    vencimento: string;
    tamanho_arquivo: string;
    status: string;
    tipo_documento_id: number;
}

export interface IDocumentoLoad {
    id: string;
    uuid: string;
    nome: string;
    documento_path: string;
    extensao: string;
    descricao: string;
    vencimento: string;
    status: string;
    tipo_documento_id: string;
}

export interface IFlorestaDocumento {
    id?: number;
    uuid: string;
    nome: string;
    status: string;
    floresta_id?: string
    documento_id?: string;
    floresta?: IFloresta;
    documento?: IDocumento;
    geojson?: IGeoJson[];
}

export interface IMateriaPrima {
    id?: number;
    uuid?: string;
    nome: string;
    nome_cientifico: string;
    status: string;
    fornecedor_id: number;
}

export interface IGeoJson {
    id?: number;
    uuid?: string;
    coordenadas: string;
    area: Uint8Array | string;
    descricao?: string;
    documento_floresta_id?: string;
}

export interface materiaPrimaProps {
    uuid: string;
    id: string;
    nome: string;
    nome_cientifico: string;
    status: string;
    fornecedor: string;
}

class FlorestaService {

    static async getFloresta(idFornecedor: string): Promise<IFloresta> {
        const { data } = await HttpClient.api.get(`floresta/fornecedor/?fornecedor_id=${idFornecedor}`);

        return data;
    }

    static async getMateriaPrima(idFornecedor: string): Promise<IMateriaPrima> {
        const { data } = await HttpClient.api.get(`floresta/materiaPrima/?fornecedor_id=${idFornecedor}`);

        return data;
    }

    static async getFlorestaSelect(idFornecedor: string): Promise<any> {
        const { data } = await HttpClient.api.get(`floresta/fornecedor/?fornecedor_id=${idFornecedor}`);

        return data;
    }

    static async getMateriaPrimaSelect(idFloresta: string): Promise<any> {
        const { data } = await HttpClient.api.get(`floresta/materiaPrima/?floresta_id=${idFloresta}`);

        return data;
    }

    static async getDocumentoFloresta(idFornecedor: string): Promise<IFlorestaDocumento> {
        const { data } = await HttpClient.api.get(`floresta/fornecedor/${idFornecedor}`);

        return data;
    }

    static async saveFloresta(floresta: IFloresta): Promise<IFloresta> {
        const { data } = await HttpClient.api.post('floresta', floresta);

        return data.data;
    }

    static async salvarDocumento(formData: FormData): Promise<IGrupoDocumentoFlorestaSave> {
        try {
            const idDocumento = formData.get('id');

            if (idDocumento !== 'N') {
                const { data } = await HttpClient.api.post(`floresta/documento/update`, formData, {
                    headers: { 'Content-Type': 'multipart/form-data' },
                });
                return data;
            } else {
                const { data } = await HttpClient.api.post(`floresta/documento/`, formData, {
                    headers: { 'Content-Type': 'multipart/form-data' },
                });
                return data;
            }
        } catch (error: any) {
            // Estruturar o erro para melhor tratamento no frontend
            const errorResponse = {
                message: error?.response?.data?.message || error?.response?.data?.errors || 'Erro desconhecido no upload',
                status: error?.response?.status || 500,
                isPdfError: false,
                showHelpButton: false
            };

            // Verificar se é erro relacionado a PDF
            const errorMessage = errorResponse.message.toLowerCase();
            if (errorMessage.includes('pdf') ||
                errorMessage.includes('formato') ||
                errorMessage.includes('tamanho') ||
                errorMessage.includes('comprimido') ||
                errorMessage.includes('versão') ||
                errorMessage.includes('corrupted') ||
                errorMessage.includes('invalid')) {
                errorResponse.isPdfError = true;
                errorResponse.showHelpButton = true;
            }

            // Re-lançar o erro com informações estruturadas
            const enhancedError = new Error(errorResponse.message);
            (enhancedError as any).response = {
                data: errorResponse,
                status: errorResponse.status
            };

            throw enhancedError;
        }
    }

    static async deleteDocumentoFloresta(id: string): Promise<IFlorestaDocumento> {
        const { data } = await HttpClient.api.delete(`floresta/documento/${id}`);

        return data;
    }

    static async salvarCoordenadas(coordenadas: IGeoJson): Promise<IGeoJson> {
        const { data } = await HttpClient.api.post('floresta/geoJson', coordenadas);

        return data;
    }

    static async updateCoordenadas(coordenadas: IGeoJson): Promise<IGeoJson> {
        const { data } = await HttpClient.api.put('floresta/geoJson', coordenadas);

        return data;
    }

    static async updateFloresta(floresta: IFloresta): Promise<IFloresta> {
        const { data } = await HttpClient.api.put('floresta', floresta);

        return data;
    }

    static async deleteFloresta(id: string): Promise<IFloresta> {
        const { data } = await HttpClient.api.delete(`floresta/${id}`);

        return data;
    }

    static async saveMateriaPrima(mtPrima: IMateriaPrima): Promise<IMateriaPrima> {
        const { data } = await HttpClient.api.post('floresta/materiaPrima', mtPrima);

        return data;
    }

    static async updateMateriaPrima(mtPrima: IMateriaPrima): Promise<IMateriaPrima> {
        const { data } = await HttpClient.api.put('floresta/materiaPrima', mtPrima);

        return data;
    }

    static async deleteMateriaPrima(uuid: string): Promise<IFloresta> {
        const { data } = await HttpClient.api.delete(`floresta/materiaPrima/${uuid}`);

        return data;
    }

    static async materiaPrimaById(id: string): Promise<IMateriaPrima> {
        const { data } = await HttpClient.api.get(`floresta/materiaPrima/${id}`);

        return data.data;
    }

    static async documentoFlorestaById(id: string): Promise<IFlorestaDocumento> {
        const { data } = await HttpClient.api.get(`floresta/documento/${id}`);
        return data.data;
    }

    static async geoJsonByDocumentoFloresta(id: string): Promise<IFlorestaDocumento> {
        const { data } = await HttpClient.api.get(`floresta/geojson/${id}`);
        return data.data;
    }

    static async getShapesByFlorestaId(florestaId: string): Promise<any> {
        try {
            const response = await HttpClient.api.get(`/floresta/${florestaId}/shapes`);
            //eslint-disable-next-line
            console.log('response', response);
            return response;
        } catch (error) {
            throw error;
        }
    }
}

export default FlorestaService;
