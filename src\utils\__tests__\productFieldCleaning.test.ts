// Teste para validar a limpeza de campos vazios no produto

describe('Product Field Cleaning', () => {
  describe('Product data preparation for API', () => {
    // Simular a lógica de preparação dos dados do produto
    const prepareProductData = (quantity: string, quantityTons: string, description: string, isUpdate = false) => {
      const baseData = {
        product_description: description,
        product_quantity: quantity?.trim() ? quantity.replace(',', '.') : null,
        product_quantity_tons: quantityTons?.trim() ? quantityTons.replace(',', '.') : null,
      };

      if (isUpdate) {
        return {
          ...baseData,
          id: 'test-id'
        };
      } else {
        return {
          ...baseData,
          status: 'A',
          supply_chain_id: 'test-supply-chain-id'
        };
      }
    };

    // Simular a lógica do serviço
    const processServiceData = (productData: any) => {
      return {
        ...productData,
        product_quantity: productData.product_quantity || null,
        product_quantity_tons: productData.product_quantity_tons || null
      };
    };

    it('should send null for empty m³ quantity on update', () => {
      const productData = prepareProductData('', '150,500', 'Teste produto', true);
      const processedData = processServiceData(productData);

      expect(processedData.product_quantity).toBeNull();
      expect(processedData.product_quantity_tons).toBe('150.500');
    });

    it('should send null for empty tons quantity on update', () => {
      const productData = prepareProductData('252,252', '', 'Teste produto', true);
      const processedData = processServiceData(productData);

      expect(processedData.product_quantity).toBe('252.252');
      expect(processedData.product_quantity_tons).toBeNull();
    });

    it('should send null for both empty quantities on update', () => {
      const productData = prepareProductData('', '', 'Teste produto', true);
      const processedData = processServiceData(productData);

      expect(processedData.product_quantity).toBeNull();
      expect(processedData.product_quantity_tons).toBeNull();
    });

    it('should send both quantities when filled on update', () => {
      const productData = prepareProductData('252,252', '150,500', 'Teste produto', true);
      const processedData = processServiceData(productData);

      expect(processedData.product_quantity).toBe('252.252');
      expect(processedData.product_quantity_tons).toBe('150.500');
    });

    it('should send null for empty m³ quantity on create', () => {
      const productData = prepareProductData('', '150,500', 'Teste produto', false);
      const processedData = processServiceData(productData);

      expect(processedData.product_quantity).toBeNull();
      expect(processedData.product_quantity_tons).toBe('150.500');
      expect(processedData.status).toBe('A');
    });

    it('should send null for empty tons quantity on create', () => {
      const productData = prepareProductData('252,252', '', 'Teste produto', false);
      const processedData = processServiceData(productData);

      expect(processedData.product_quantity).toBe('252.252');
      expect(processedData.product_quantity_tons).toBeNull();
      expect(processedData.status).toBe('A');
    });

    it('should handle comma to dot conversion correctly', () => {
      const productData = prepareProductData('1.252,500', '2.150,750', 'Teste produto', true);
      const processedData = processServiceData(productData);

      expect(processedData.product_quantity).toBe('1.252.500');
      expect(processedData.product_quantity_tons).toBe('2.150.750');
    });

    it('should handle whitespace-only values as empty', () => {
      const productData = prepareProductData('   ', '  ', 'Teste produto', true);
      const processedData = processServiceData(productData);

      // Whitespace-only strings should be treated as empty
      expect(processedData.product_quantity).toBeNull();
      expect(processedData.product_quantity_tons).toBeNull();
    });
  });
});
