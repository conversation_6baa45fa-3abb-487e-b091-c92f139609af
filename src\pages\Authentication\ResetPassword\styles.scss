.forgot-password {
  height: 70vh;
  @media (max-width: 1440px) {
    max-width: 76rem;
  }
  @media (max-width: 1140px) {
    max-width: 52rem;
  }
  @media (max-width: 940px) {
    max-width: 36rem;
  }

  .box-forgot {
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 600px;

    .card {
      width: 100%;
      border-radius: 8px;

      .card-body {
        padding: 2.5rem 1.75rem 2rem 2rem;
        width: 100%;

        .logo {
          padding: 1.75rem 0;
        }

        h1 {
          color: var(--green-400);
          font-size: 2rem;
          text-align: center;
          font-weight: 600 !important;
        }
        .rowBtn {
          padding-left: 1rem;
        }
        .controlBtn {
          width: 100% !important;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: var(--green-400);
          height: 54px;
          border-radius: 8px;
          &:hover {
            background-color: #bac5c5;
          }
        }

        .titleReset {
          font-weight: 700;
          font-size: 48px;
          line-height: 72px;
          color: #201e40;
          padding-left: 0.625rem;
        }

        .labelForgot {
          color: #201e40;
          padding-left: 0.625rem;
        }

        .labelConfirm {
          color: #ffffffff;
          font-family: 'Poppins';
        }

        .notGetEmail {
          padding-left: 0.7rem;
        }

        form {
          button {
            background-color: var(--green-400);
            border: 0;
            transition: all 0.2s;

            &:hover {
              background-color: var(--green-800);
            }
          }
        }
      }
    }
  }

  a {
    color: #64a4ec;
    cursor: pointer;
  }
}
.form-control {
  line-height: 2;
}
