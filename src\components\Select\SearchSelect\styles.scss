.select {
  .css-1pahdxg-control {
    border: 0.06rem solid var(--gray-200);
    box-shadow: 0 0 0 0.125rem var(--green-200);

    &:hover {
      border-color: var(--gray-200);
    }
  }

  .css-1s2u09g-control,
  .css-1pahdxg-control {
    .css-1rhbuit-multiValue {
      background-color: var(--green-500);
      border-radius: 0.25rem;

      .css-12jo7m5 {
        color: var(--white-100);
        font-size: 1rem;
      }

      .css-xb97g8 {
        color: var(--white-100);
        font-size: 1rem;

        &:hover {
          background-color: var(--green-500);
        }
      }
    }
  }

  .css-26l3qy-menu {
    .css-4ljt47-MenuList {
      div {
        &:hover {
          background-color: var(--green-200);
        }
      }
    }
  }

  .css-1okebmr-indicatorSeparator {
    background-color: transparent;
  }

  .css-1n7v3ny-option,
  .css-9gakcf-option {
    background-color: var(--green-100);
    color: var(--gray-900);
  }
}

.select-error {
  &__title {
    color: var(--gray-800);
  }

  &__invalid-feedback {
    font-size: 0.87rem;
    color: var(--red-500);
    font-weight: var(--is-400);
  }

  .css-1s2u09g-control {
    border-color: var(--red-500);
    color: var(--red-500);
    font-weight: var(--is-400);
    background-image: url('../../../statics/icons/input-error.svg');
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    padding-right: calc(1.5em + 0.75rem);

    &:hover {
      border-color: var(--red-500);
    }

    &:focus {
      box-shadow: 0.5rem var(--green-200);
      border-color: var(--red-500);
      outline: var(--green-200) 0.125rem solid;
    }
  }

  .css-1pahdxg-control {
    box-shadow: 0.5rem var(--green-200);
    border-color: var(--red-500);
    outline: var(--green-200) 0.125rem solid;

    &:hover {
      border-color: var(--red-500);
    }
  }
}

.select-multi {
  .css-1s2u09g-control,
  .css-1pahdxg-control {
    max-height: 2.8rem;
    min-height: 2.8rem;
    overflow: auto;

    .css-1hb7zxy-IndicatorsContainer {
      display: none;
    }
  }
}

.selectDisabled {
  line-height: 1.4;
  border: 1px solid var(--secondary-secondary-200, #bac5c5);
  background: var(--secondary-secondary-100, #edf0f0);
  display: flex;
  padding: 0.75rem 1rem;
  align-items: center;
  gap: 0.625rem;
  align-self: stretch;
  color: var(--secondary-secondary-200, #bac5c5);
  font-feature-settings: 'clig' off, 'liga' off;
}
