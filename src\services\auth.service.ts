import HttpClient from './httpClient';
import { IAuth } from '../interfaces/IAuth';
import grecaptcha from './recaptcha.service';
import { IUserResponse } from '../interfaces';

interface IMessageResponse {
  message: string;
}

interface INewUser {
  name: string;
  email: string;
  profile_id: number;
  company_id: any;
  status_profile?: string;
  companies?: any;
}

export interface UserById {
  data: INewUser;
  email?: string;
}

class AuthsService {
  static async sign(username: string, password: string): Promise<IAuth> {
    try {
      const tokeRecaptcha = await grecaptcha('login');

      const { data } = await HttpClient.api.post<IAuth>('/oauth/token', {
        username,
        password,
        grant_type: 'password',
        recaptcha_token: tokeRecaptcha,
      });

      return data;
    } catch (error) {
      console.error('Erro ao fazer login:', error);
      throw error;
    }
  }

  static async sendPasswordResetEmail(email: string): Promise<IMessageResponse> {
    const tokeRecaptcha = await grecaptcha('login');

    const { data } = await HttpClient.api.post<IMessageResponse>('/users/esqueciMinhaSenhaEudr', {
      email,
      recaptcha_token: tokeRecaptcha,
      isAdm: false,
    });

    return data;
  }

  static async resetPassword(password: string, confirmPassword: string, token: string): Promise<IMessageResponse> {
    const tokeRecaptcha = await grecaptcha('login');

    const { data } = await HttpClient.api.put<IMessageResponse>(`/users/gerarNovaSenha/${token}`, {
      password,
      c_password: confirmPassword,
      recaptcha_token: tokeRecaptcha,
    });

    return data;
  }

  static async resetPasswordFirstAccess(
    atualPassword: string,
    password: string,
    confirmPassword: string,
    token: string
  ): Promise<IMessageResponse> {
    const tokeRecaptcha = await grecaptcha('login');

    const { data } = await HttpClient.api.post<IMessageResponse>(`/users/alterarMinhaSenha/${token}`, {
      senha_atual: atualPassword,
      password,
      c_password: confirmPassword,
      recaptcha_token: tokeRecaptcha,
    });

    return data;
  }

  static async newUser(user: INewUser): Promise<void> {
    const { data } = await HttpClient.api.post('users/eudr', { ...user });

    return data;
  }

  static async updateUser(user: INewUser, id: string): Promise<void> {
    const { data } = await HttpClient.api.put(`/users/${id}`, { ...user });
    return data;
  }

  static async getById(id: string): Promise<UserById> {
    const { data } = await HttpClient.api.get(`/user/${id}`);
    return data;
  }

  static async getUsers(
    page: number,
    perPage: number,
    query: string,
    order_by: string,
    sort: string,
    new_users?: boolean,
    idClient?: string,
  ): Promise<IUserResponse> {
    const { data } = await HttpClient.api.get(
      `/users?page=${page}&perPage=${perPage}&query=${query}&order_by=${order_by}&sort=${sort}&new_users=${new_users}&idClient=${idClient}`
    );

    return data;
  }

  static async sendEmailForCustomer(email: string): Promise<IMessageResponse> {
    const tokeRecaptcha = await grecaptcha('login'); //localStorage.getItem('_grecaptcha');

    const { data } = await HttpClient.api.post<IMessageResponse>('/users/esqueciMinhaSenhaEudr', {
      email,
      recaptcha_token: tokeRecaptcha,
      isAdm: false,
    });

    return data;
  }
}

export default AuthsService;
