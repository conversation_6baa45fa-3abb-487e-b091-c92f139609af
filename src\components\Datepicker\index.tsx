import React from 'react';
import CustomDatePicker from './CustomDatePicker';
import { Form } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import ptBR from 'date-fns/locale/pt-BR';
import './styles.scss';

interface IProp {
  title?: string;
  id?: string;
  className?: string;
  onChange: (value: React.SetStateAction<Date | null>) => void;
  selected?: Date | null | undefined;
  isInvalid?: boolean | false;
  msg?: string;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  showYearDropdown?: boolean;
  showMonthDropdown?: boolean;
  placeholder?: string;
  minDate?: Date | null;
  excludeDates?: Date[] | undefined;
}

const DatePicker = ({
  title,
  id,
  className,
  selected,
  onChange,
  isInvalid,
  msg,
  onBlur,
  disabled,
  showYearDropdown,
  showMonthDropdown,
  placeholder,
  minDate,
  excludeDates,
}: IProp): React.ReactElement => {
  const { t } = useTranslation();

  return (
    <Form.Group controlId={id} className="datePicker mb-3">
      <Form.Label className={disabled ? 'disabled-label mb-0' : 'mb-0'}>{title}</Form.Label>
      <CustomDatePicker
        className={`form-control ${isInvalid ? 'invalid-border' : ''} ${classNames(className)}`}
        selected={selected}
        onChange={onChange}
        onBlur={onBlur}
        locale={ptBR}
        dateFormat={t('format.date')}
        placeholderText={placeholder}
        disabled={disabled}
        showYearDropdown={showYearDropdown}
        showMonthDropdown={showMonthDropdown}
        minDate={minDate}
        excludeDates={excludeDates}
      />
      <Form.Label className={classNames(isInvalid && 'dt-picker__label-error')}>{isInvalid ? msg : ''}</Form.Label>
    </Form.Group>
  );
};

DatePicker.defaultProps = { showYearDropdown: false, showMonthDropdown: false };

export default DatePicker;
