.new-user {
  max-width: 96rem;
  margin-left: auto;
  margin-right: auto;
  padding: 1.4rem;
  @media (max-width: 1440px) {
    max-width: 76rem;
  }
  @media (max-width: 1140px) {
    max-width: 52rem;
  }
  @media (max-width: 940px) {
    max-width: 36rem;
  }

  .css-319lph-ValueContainer {
    line-height: 2rem;
  }

  li {
    margin-left: 1rem;
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    text-align: initial;
    line-height: 34px;
    color: #201e40;
  }

  .textFree {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
    text-transform: uppercase;
    color: #54666f;
  }

  .textSign {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;

    color: #201e40;
  }

  .divInfo {
    height: auto;
    @media (max-width: 768px) {
      display: none;
    }
  }
  .divCrud {
    padding-left: 2.4rem;
  }

  .btnCreate {
    min-height: 3rem;
    border-radius: 8px;
    margin-right: 1rem;
  }

  .gapItensCrud {
    padding-right: 2rem;
    @media (max-width: 768px) {
      padding-right: 0.7rem;
      margin-bottom: 1rem;
    }
  }
  .displayImg {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .labelInfoCountry {
    color: #18b680;
    text-decoration: none;
  }
  .labelInfoImport {
    margin-top: 1rem;
    color: #201e40;
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 700;
    font-size: 32px;
    line-height: 48px;
    letter-spacing: 0.5px;
  }

  .infoLogin {
    color: #ffffffff;
    font-family: 'Lato';
  }

  .linkLoginHere {
    color: #201e40 !important;
    font-weight: 700;
  }

  .alignItemsInfos {
    flex-direction: column;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 3rem;
    margin-bottom: 4rem;
    @media (max-width: 1056px) {
      padding-left: 1rem;
    }
  }
}
