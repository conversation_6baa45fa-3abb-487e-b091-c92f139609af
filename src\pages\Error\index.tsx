import React from 'react';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom';

import { Row, Col, Image } from 'react-bootstrap';
import img from '../../statics/image-asset.png';
import Section from '../../components/Section';
import Button from '../../components/Button';

const Error: React.FunctionComponent = () => {
  const { t } = useTranslation();
  const history = useHistory();

  return (
    <Section title="Página não encontrada" description="Página não encontrada">
      <Row>
        <Col className="d-flex align-items-center justify-content-center mt-2">
          <h1>{t('labels.labelPageNotFound')} </h1>
        </Col>
      </Row>
      <Row>
        <Col className="d-flex align-items-center justify-content-center mt-4">
          <p style={{ fontSize: '20px' }}>{t('labels.labelPageNotExists')}</p>
        </Col>
      </Row>
      <Row>
        <Col className="d-flex align-items-center justify-content-center mt-4 mb-4">
          <Image height={300} src={img} />
        </Col>
      </Row>
      <Row>
        <Col className="d-flex align-items-center justify-content-center mt-4">
          <Button cy="err" onClick={() => history.push('/')} variant="secondary">
            {t('buttons.goToHome')}
          </Button>
        </Col>
      </Row>
    </Section>
  );
};

export default Error;
