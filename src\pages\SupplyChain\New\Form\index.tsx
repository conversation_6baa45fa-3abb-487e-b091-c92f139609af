import React, { useState } from 'react';
import { Card, Container, Image, Row, Col, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import CustomInput from '../../../../components/Input/CustomInput';
import CustomCard from '../../../../components/Card';
import Select from '../../../../components/Select';
import './styles.scss';
import { ISelectOption } from '../../../../interfaces';
import Modal from '../../../../components/Modal';
import CustomButton from '../../../../components/Button';
import toastMsg, { ToastType } from '../../../../utils/toastMsg';
import { useLoader } from '../../../../contexts/LoaderContext';
import { CardContent } from '@mui/material';
import { Upload, FileIcon } from 'lucide-react';
import DatePicker from '../../../../components/Datepicker';
import InactiveUser from '../../../../statics/delete.svg';
import DocumentosService from '../../../../services/documentos.service';
import SupplyChainService from '../../../../services/supplyChain.service';
import CompaniesService from '../../../../services/companies.service';
import renderSelectValues from '../../../../utils/renderSelectValues';
import { useAuth } from '../../../../contexts/AuthContext';
import { useHistory } from 'react-router-dom';

import { formatDateToString } from '../../../../utils/formatDateToString';

interface IPropsCard {
  loadCrudDetail?: any;
  supplyChain?: any;
}

interface FileItem {
  uuid?: string;
  file: File;
  preview?: string;
  vencimento?: Date;
  path?: string;
  tamanho_arquivo?: string;
}

const CardIdentifySupply = ({ loadCrudDetail, supplyChain }: IPropsCard): React.ReactElement => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const history = useHistory();
  const [show, setShow] = useState<any>({ show: false, client: null, idDoc: null });
  const { setShowLoader } = useLoader();
  const [files, setFiles] = useState<FileItem[]>([] as FileItem[]);
  const [pedido, setPedido] = useState<string>('');
  const [invoice, setInvoice] = useState<string>('');
  const [bl, setBl] = useState<string>('');
  const [importerSelect, setImporterSelect] = useState<ISelectOption>({} as ISelectOption);
  //const [dataVenimento, setDataVencimento] = useState<Date | null>(null);
  let arquivoValido = 'F';
  //eslint-disable-next-line
  const [page, setPage] = React.useState(1);
  //eslint-disable-next-line
  const [limit, setLimit] = React.useState(100);
  //eslint-disable-next-line
  const [query, setQuery] = React.useState<string>('');

  // Estados para controle de erro de upload
  const [uploadError, setUploadError] = useState<any>(null);
  const [showHelpModal, setShowHelpModal] = useState<boolean>(false);

  // Função para limpar erro de upload
  const clearUploadError = () => {
    setUploadError(null);
    localStorage.removeItem('uploadError');
  };

  // Verificar se há erro salvo no localStorage ao montar o componente
  React.useEffect(() => {
    const savedError = localStorage.getItem('uploadError');
    if (savedError) {
      try {
        const errorObj = JSON.parse(savedError);
        setUploadError(errorObj);
      } catch (e) {
        localStorage.removeItem('uploadError');
      }
    }
  }, []);

  const selectCompanies = async (value: string): Promise<ISelectOption[]> => {
    const res = await CompaniesService.getCompaniesByExporter(query, page, limit, 'customer');
    const options = res.data.map((item) => ({
      label: item.name,
      value: item?.id,
    }));

    return renderSelectValues(value, options);
  };

  const handleDeleteDocument = async (id: any): Promise<void> => {
    if (!id) {
      return;
    }

    try {
      setShowLoader(true);
      //await FlorestaService.deleteFloresta(id);
      setShowLoader(false);
      window.location.reload();
      //history.push('/new-fornecedor/' + uuidFornecedor, { reload: true });
      toastMsg(ToastType.Success, t('response.deleteSuccess'));
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  };

  const handleChangeImporter = (selectedOption: any): void => {
    setImporterSelect(selectedOption);
    importerSelect.value = selectedOption.value;
  };

  const setValuesUpdate = React.useCallback(async () => {
    setPedido(supplyChain.pedido);
    setInvoice(supplyChain.invoice);
    setBl(supplyChain.bl);
    //setDataVencimento(new Date(supplyChain.vencimento));
    setImporterSelect({
      value: supplyChain?.importador.id,
      label: supplyChain?.importador.nome,
    });
  }, [setImporterSelect, supplyChain]);

  React.useEffect(() => {
    let controle = true;
    if (supplyChain.id !== 0 && controle) {
      setValuesUpdate();
    }

    return () => {
      controle = false;
    };
  }, [setValuesUpdate, supplyChain]);

  const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (event.dataTransfer.files) {
      if (event.dataTransfer.files[0].type !== 'application/pdf' || event.dataTransfer.files[0].size < 1024) {
        toastMsg(ToastType.Error, t('exceptions.pdfType'));
        return;
      }
      const newFiles = Array.from(event.dataTransfer.files).map((file) => ({
        file,
        preview: URL.createObjectURL(file),
        vencimento: new Date(),
      }));
      setFiles([...files, ...newFiles]);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const fileTemp = event.target.files[0];
      const buffer = await fileTemp.slice(0, 5).arrayBuffer();
      const header = new Uint8Array(buffer);
      const pdfHeader = String.fromCharCode.apply(null, Array.from(header));

      if (
        !pdfHeader.startsWith('%PDF-') ||
        event.target.files[0].type !== 'application/pdf' ||
        event.target.files[0].size < 1024
      ) {
        toastMsg(ToastType.Error, t('exceptions.pdfType'));
        return;
      }
      const newFiles = Array.from(event.target.files).map((file) => ({
        file,
        preview: URL.createObjectURL(file),
        vencimento: new Date(),
      }));
      setFiles([...files, ...newFiles]);
    }
  };

  const validateFiles = (tamanhoArquivo: string) => {
    if (tamanhoArquivo !== '') {
      arquivoValido = 'T';
    }
    return tamanhoArquivo;
  };

  const formatFileSize = (bytes: number) => {
    // Validate file type and set arquivoValido
    files.forEach((file) => {
      if (file.file.type === 'application/pdf') {
        if (bytes < 1024) {
          arquivoValido = 'F';
        } else {
          arquivoValido = 'T';
        }
      } else if (file.file.type === '') {
        arquivoValido = 'T';
      }
    });

    // Format file size
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];

    if (bytes < k) {
      return bytes + ' ' + sizes[0];
    } else if (bytes < k * k) {
      return parseFloat((bytes / k).toFixed(2)) + ' ' + sizes[1];
    } else if (bytes < k * k * k) {
      return parseFloat((bytes / (k * k)).toFixed(2)) + ' ' + sizes[2];
    } else {
      return parseFloat((bytes / (k * k * k)).toFixed(2)) + ' ' + sizes[3];
    }
  };

  const removeFile = (index: number) => {
    const newFiles = [...files];
    URL.revokeObjectURL(newFiles[index].preview || '');
    newFiles.splice(index, 1);
    setFiles(newFiles);
  };

  const getDocumentNameLimited = (name: string): string => {
    if (name.length <= 45) {
      return name;
    }

    // Extract the file extension
    const lastDotIndex = name.lastIndexOf('.');
    const extension = lastDotIndex !== -1 ? name.substring(lastDotIndex) : '';

    // Calculate how much of the name we can keep
    const maxNameLength = 45 - extension.length;

    if (maxNameLength <= 0) {
      // If extension is too long, just truncate the whole name
      return name.substring(0, 45);
    }

    // Truncate name and append original extension
    return name.substring(0, maxNameLength) + extension;
  };

  const getDocumentName = (path: string): string => {
    const parts = path.split('/');
    if (parts.length > 0) {
      return getDocumentNameLimited(parts[parts.length - 1]);
    }
    return '';
  };

  const getTipoDocumento = React.useCallback(async (idTipo: string): Promise<any> => {
    try {
      const res: any = await DocumentosService.getTiposDocumento(idTipo);

      if (res) {
        return res;
      }
    } catch (error) {
      //eslint-disable-next-line
      console.log(error);
    }
  }, []);

  async function atualizrDataVencimento(): Promise<void> {
    const newFiles = files.map((file) => {
      return file;
    });
    setFiles([...newFiles]);
  }

  async function loadDocSave(): Promise<void> {
    try {
      const newFiles = await Promise.all(
        Array.from(supplyChain.documentos).map(async (file: any) => {
          const fileName = file.documento_path.split('/').pop() || 'documento.pdf';
          return {
            uuid: file.uuid,
            file: new File([file.documento_path], fileName),
            preview: URL.createObjectURL(new File([file.documento_path], file.documento_path)),
            vencimento: new Date(file.vencimento),
            path: file.documento_path,
            tamanho_arquivo: file.tamanho_arquivo,
          };
        })
      );

      setFiles([...newFiles]);
    } catch (error) {
      toastMsg(ToastType.Error, 'Erro ao carregar documentos');
      console.error('Erro em loadDocSave:', error);
    }
  }

  React.useEffect(() => {
    let controle = true;

    if (supplyChain.id !== 0 && controle) {
      loadDocSave();
    }

    return () => {
      controle = false;
    };
  }, [setFiles, supplyChain]);

  const handleSubmit = React.useCallback(async () => {
    try {
      setShowLoader(true);

      if (importerSelect === undefined || importerSelect === null) {
        toastMsg(ToastType.Error, 'Selecione um Importador');
        setShowLoader(false);
        return;
      }

      if (pedido === '' || pedido === undefined || pedido === null) {
        toastMsg(ToastType.Error, 'Preencha o Pedido');
        setShowLoader(false);
        return;
      }

      if (files.length > 0) {
        if (files.length > 0 && arquivoValido === 'F') {
          toastMsg(ToastType.Error, t('exceptions.fileInvalid'));
          return;
        }
      }

      const formData = new FormData();

      if (files.length > 0) {
        //formData.append('files', files);
        files.forEach((file, index) => {
          formData.append('files[' + index + ']', file.file);
        });
      }

      if (supplyChain.id !== 0) {
        formData.append('id', JSON.stringify(supplyChain.id));
      }

      const newSupply: any = {
        uuid: '',
        pedido: pedido,
        bl: bl,
        invoice: invoice,
        status: 'A',
        importador_id: importerSelect?.value,
        cliente_id: user.default_company?.id,
      };

      formData.append('newSupply', JSON.stringify(newSupply));

      const tipoDocumento = await getTipoDocumento('32');

      const documentos = Array.from(files).map((file: any) => ({
        nome: tipoDocumento.data.titulo,
        descricao: tipoDocumento.data.descricao,
        status: 'A',
        documento: {
          uuid: file.uuid,
          nome: tipoDocumento.data.titulo,
          descricao: tipoDocumento.data.descricao,
          descricao_en: tipoDocumento.data.descricao_en,
          extensao: '',
          tamanho_arquivo: formatFileSize(file.file.size),
          vencimento: formatDateToString(new Date(file.vencimento)),
          tipo_documento_id: String(tipoDocumento.data.id),
          status: 'A',
        },
        supply_chain_id: '',
      }));

      formData.append('documentoSupply', JSON.stringify(documentos));

      const newSypply = await SupplyChainService.salvarDocSupply(formData);
      toastMsg(ToastType.Success, 'Cadeia de Fornecimento salvo com sucesso!');
      loadCrudDetail();

      setValuesUpdate();
      loadDocSave();

      history.push(`/new-supply/${newSypply?.id}`);
    } catch (error: any) {
      console.error('Erro ao salvar specie:', error);

      // Verificar se é erro de upload de PDF
      const errorData = error?.response?.data;
      if (errorData && (errorData.isPdfError || errorData.showHelpButton)) {
        // Setar uploadError para exibir a mensagem de erro
        const uploadErrorObj = {
          message: errorData.message || error?.message || 'Erro no upload do PDF',
          showHelpButton: errorData.showHelpButton || false,
          isPdfError: errorData.isPdfError || false,
        };

        setUploadError(uploadErrorObj);

        // Salvar no localStorage para persistir entre recarregamentos
        localStorage.setItem('uploadError', JSON.stringify(uploadErrorObj));

        // NÃO fechar o modal para que o usuário veja a mensagem de erro
      } else {
        // Para outros tipos de erro, exibir toast normalmente
        toastMsg(ToastType.Error, error?.response?.data?.errors || error?.message || 'Erro desconhecido');

        // Para erros não relacionados a PDF, também manter o modal aberto
        // para que o usuário possa corrigir e tentar novamente
      }
    } finally {
      setShowLoader(false);
    }
  }, [pedido, bl, invoice, importerSelect, files]);

  const downloadDoc = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <CustomCard
      cy="card-test-product"
      renderBody={
        <Row>
          <Col md={12} className="mt-2">
            <Select
              id={`importerSelect${t('language') || supplyChain.id}`}
              renderKey
              loadOptions={selectCompanies}
              cacheOptions
              defaultOptions
              title={`${t('labels.importerLabel')}*`}
              placeholder={t('labels.selectImporter')}
              noOptionsMessage={() => t('labels.noOptions')}
              loadingMessage={() => 'Carregando...'}
              onChange={handleChangeImporter}
              value={
                Object.values(importerSelect || {}).length
                  ? { value: importerSelect?.value, label: importerSelect?.label }
                  : null
              }
              cy="test-selectImporterSelect"
            />
          </Col>
          <Col md={12} className="mt-2">
            <CustomInput
              cy="test-pedido"
              id="pedido"
              name="pedido"
              label={`${t('labels.order')}*`}
              placeholder={t('labels.sendOrder')}
              value={pedido}
              onChange={(e) => {
                setPedido(e.target.value);
              }}
              type="text"
              maxLength={255}
            />
          </Col>
          <Col md={12} className="mt-2">
            <Row style={{ marginLeft: '1px' }}>
              <Col md={6}>
                <CustomInput
                  cy="test-bl"
                  id="bl"
                  name="bl"
                  label={`BL`}
                  placeholder={t('labels.sendBl')}
                  value={bl}
                  onChange={(e) => {
                    setBl(e.target.value);
                  }}
                  type="text"
                  maxLength={255}
                />
              </Col>
              <Col md={6}>
                <CustomInput
                  cy="test-invoice"
                  id="invoice"
                  name="invoice"
                  label={`Invoice`}
                  placeholder={t('labels.sendInvoice')}
                  value={invoice}
                  onChange={(e) => {
                    setInvoice(e.target.value);
                  }}
                  type="text"
                  maxLength={255}
                />
              </Col>
            </Row>
          </Col>

          <Col md={12} className="mt-2">
            <Card>
              <Modal
                show={show.show}
                handleClose={() => setShow({ ...show, show: false })}
                title={t('modal.removeFlorestaTitle')}
                size="lg"
                className="styleModalConfirm"
              >
                <Container>
                  <Row className="mt-2 p-2">
                    <Col className="d-flex align-items-center ">
                      <h6>
                        <br />
                        {t('modal.removeFlorestaSubTitle') + show.titulo + '?'}
                        <br />
                        {t('modal.removeProductDescription')}
                      </h6>
                    </Col>
                  </Row>
                </Container>

                <Row className="mt-2">
                  <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
                    <CustomButton
                      cy="btn-cancel"
                      type="button"
                      variant="outline-green"
                      onClick={() => {
                        setShow(false);
                      }}
                    >
                      {t('buttons.cancel')}
                    </CustomButton>
                    <CustomButton
                      cy="btn-save"
                      type="button"
                      variant="danger"
                      onClick={() => handleDeleteDocument(show.idDoc)}
                    >
                      {t('buttons.delete')}
                    </CustomButton>
                  </Col>
                </Row>
              </Modal>
              {/* Mensagem de erro do upload */}
              {uploadError && (
                <Col md={12} className="mt-2">
                  <div
                    className="alert alert-danger d-flex justify-content-between align-items-center mb-3"
                    role="alert"
                  >
                    <div className="d-flex align-items-center">
                      <i className="fas fa-exclamation-triangle me-2"></i>
                      <span>{uploadError.message}</span>
                    </div>
                    <div className="d-flex gap-2">
                      {uploadError.showHelpButton && (
                        <Button variant="outline-primary" size="sm" onClick={() => setShowHelpModal(true)}>
                          Como resolver
                        </Button>
                      )}
                      <Button variant="outline-secondary" size="sm" onClick={clearUploadError}>
                        ✕
                      </Button>
                    </div>
                  </div>
                </Col>
              )}

              <CardContent>
                <div>
                  <div className="flex items-center gap-2">
                    <label className="text-sm">
                      {t('modal.anexarNotaPedido')}
                      <br />
                    </label>
                  </div>

                  <div
                    className="border-2 border-dashed rounded-lg p-6 text-center"
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                  >
                    <div
                      className="flex flex-col items-center gap-2"
                      style={{
                        display: 'flex',
                        alignContent: 'flex-start',
                        flexDirection: 'row',
                        alignItems: 'flex-start',
                        flexWrap: 'nowrap',
                        justifyContent: 'center',
                      }}
                    >
                      <div>
                        <Upload className="h-10 w-10 text-muted-foreground" />
                      </div>
                      <div style={{ marginLeft: '40px' }}>
                        <p>{t('labels.selecioneArquivo')}</p>
                        <p className="text-sm text-muted-foreground">PDF File {t('exceptions.maxSizeDocment')}</p>
                      </div>
                      <div style={{ marginLeft: '50px' }}>
                        <Button
                          onClick={() => document.getElementById(`file-upload${supplyChain?.id}`)?.click()}
                          variant="outline-success"
                          className="mt-2"
                        >
                          {t('buttons.selectFile')}
                        </Button>
                      </div>
                    </div>
                    <div>
                      <input
                        id={'file-upload' + supplyChain?.id}
                        type="file"
                        className="hidden"
                        onChange={handleFileSelect}
                        accept=".pdf"
                        multiple
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4" style={{ marginTop: '20px' }}>
                  <h3 className="font-bold text-sm">{t('labels.labelArquivoAdicionado')}</h3>
                  <div className="border rounded-lg" style={{ fontSize: '12px' }}>
                    <div
                      className="grid grid-cols-12 border-b bg-muted/50"
                      style={{ paddingTop: '10px', paddingBottom: '10px' }}
                    >
                      <div className="col-span-1" style={{ display: 'flex', justifyContent: 'center' }}>
                        {t('table.type')}
                      </div>
                      <div className="col-span-6">{t('table.name')}</div>
                      <div className="col-span-2">{t('table.size')}</div>
                      <div className="col-span-2">{t('table.dtEmissao')}</div>
                      <div className="col-span-1" style={{ display: 'flex', justifyContent: 'center' }}>
                        {t('modal.modalTitleActions')}
                      </div>
                    </div>
                    {files.map((file, index) => (
                      <div key={index} className="grid grid-cols-12 items-center">
                        <div className="col-span-1" style={{ display: 'flex', justifyContent: 'center' }}>
                          <FileIcon className="h-6 w-6" />
                        </div>
                        <div className="col-span-6 flex items-center gap-2">
                          <Button variant="link" onClick={() => downloadDoc(file.path || '')}>
                            {file.path ? getDocumentName(file.path || '') : getDocumentNameLimited(file.file.name)}
                          </Button>
                        </div>
                        <div className="col-span-2">
                          {file?.tamanho_arquivo ? validateFiles(file.tamanho_arquivo) : formatFileSize(file.file.size)}
                        </div>
                        <div className="col-span-2" style={{ paddingTop: '10px', fontSize: '12px' }}>
                          <DatePicker
                            onChange={(start) => {
                              if (start) {
                                file.vencimento = new Date(start.toString());
                                atualizrDataVencimento();
                              }
                            }}
                            selected={file.vencimento || new Date()}
                            className="dt-picker"
                          />
                        </div>
                        <div className="col-span-1" style={{ display: 'flex', justifyContent: 'center' }}>
                          <Button variant="ghost" title={t('titles.removeDocument')} onClick={() => removeFile(index)}>
                            <Image src={InactiveUser} height={20} />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="d-flex justify-content-end" style={{ marginTop: '20px' }}>
                  <Button variant="outline-secondary" onClick={handleSubmit}>
                    {t('buttons.onlySave')}
                  </Button>
                </div>
              </CardContent>

              {/* Modal de ajuda para conversão de PDF */}
              <Modal
                show={showHelpModal}
                handleClose={() => setShowHelpModal(false)}
                title="Como resolver problemas com PDF"
                size="lg"
                className="styleModalConfirm"
              >
                <div className="p-4">
                  <div className="alert alert-info mb-4">
                    <strong>Problemas mais comuns:</strong>
                    <ul className="mb-0 mt-2">
                      <li>Arquivo muito grande (limite: 10MB)</li>
                      <li>PDF corrompido ou com erro</li>
                      <li>Versão do PDF incompatível</li>
                      <li>Arquivo protegido por senha</li>
                    </ul>
                  </div>

                  <h5>Soluções recomendadas:</h5>
                  <div className="mt-3">
                    <h6>1. Corrigir erros no PDF (Recomendado):</h6>
                    <div className="ms-3 mb-3">
                      <strong>Ferramentas online gratuitas:</strong>
                      <ul>
                        <li>
                          <strong>1°. Reduzir compressão do PDF: </strong>
                          <a href="https://www.pdfyeah.com/decompress-pdf/" target="_blank" rel="noreferrer">
                            https://www.pdfyeah.com/decompress-pdf/
                          </a>
                        </li>
                        <li>
                          <strong>2°. SmallPDF: </strong>
                          <a href="https://smallpdf.com/compress-pdf" target="_blank" rel="noreferrer">
                            smallpdf.com/compress-pdf
                          </a>
                        </li>
                        <li>
                          <strong>3°. ILovePDF: </strong>
                          <a href="https://ilovepdf.com/compress_pdf" target="_blank" rel="noreferrer">
                            ilovepdf.com/compress_pdf
                          </a>
                        </li>
                        <li>
                          <strong>4°. PDF24: </strong>
                          <a href="https://tools.pdf24.org/pt/comprimir-pdf" target="_blank" rel="noreferrer">
                            tools.pdf24.org/pt/comprimir-pdf
                          </a>
                        </li>
                      </ul>
                      <div className="alert alert-success mt-2">
                        <small>
                          <strong>Dica:</strong> Essas ferramentas otimizam o PDF, desconprimindo ou reduzindo o tamanho
                          e mantendo a qualidad e.
                        </small>
                      </div>
                    </div>

                    <h6>2. Usando Adobe Acrobat (Se disponível):</h6>
                    <div className="ms-3 mb-3">
                      <ol>
                        <li>Abra o PDF no Adobe Acrobat</li>
                        <li>Vá em &quot;Arquivo&quot; → &quot;Salvar como outro&quot; → &quot;PDF otimizado&quot;</li>
                        <li>Nas configurações, reduza a qualidade das imagens para 150 DPI</li>
                        <li>Desmarque opções desnecessárias</li>
                        <li>Salve o arquivo</li>
                      </ol>
                    </div>

                    <h6>3. Verificações antes do upload:</h6>
                    <div className="ms-3">
                      <ul>
                        <li>✅ Arquivo em formato PDF</li>
                        <li>✅ Tamanho menor que 10MB</li>
                        <li>✅ PDF não protegido por senha</li>
                        <li>✅ Arquivo não corrompido (abre normalmente)</li>
                        <li>✅ Conteúdo legível e completo</li>
                      </ul>
                    </div>
                  </div>

                  <div className="d-flex justify-content-end mt-4">
                    <Button variant="outline-secondary" onClick={() => setShowHelpModal(false)}>
                      Fechar
                    </Button>
                  </div>
                </div>
              </Modal>
            </Card>
          </Col>
        </Row>
      }
    />
  );
};

export default CardIdentifySupply;
