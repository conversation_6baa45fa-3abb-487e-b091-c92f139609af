import React from 'react';
import { OverlayTrigger, Tooltip } from 'react-bootstrap';
import { IconType } from 'react-icons';

interface IconTooltipProps {
  icon: IconType;
  text?: string;
  placement?: 'top' | 'right' | 'bottom' | 'left';
  color?: string;
  size?: number;
}

const IconTooltip: React.FC<IconTooltipProps> = ({ icon: IconComponent, text, placement = 'top', color, size }) => (
  <OverlayTrigger placement={placement} overlay={<Tooltip id={`tooltip-${text}`}>{text}</Tooltip>}>
    <span style={{ cursor: 'pointer' }}>
      <IconComponent color={color} size={size} />
    </span>
  </OverlayTrigger>
);

export default IconTooltip;
