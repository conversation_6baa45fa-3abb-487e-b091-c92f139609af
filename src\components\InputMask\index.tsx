import React from 'react';
import { Form } from 'react-bootstrap';
import classNames from 'classnames';
import ReactInputMask from 'react-input-mask';
import './styles.scss';

interface IProp {
  value: string;
  placeholder: string;
  id: string;
  label?: string;
  readOnly?: boolean | false;
  required?: boolean | false;
  disabled?: boolean | false;
  isInvalid?: boolean | false;
  desc: string;
  mask: string;
  msg?: string;
  onChange?: React.ChangeEventHandler<HTMLInputElement> | undefined;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onKeyPress?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onKeyUp?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  cy: string;
  tabIndex?: number;
}

const InputMask = ({
  value,
  placeholder,
  id,
  label,
  readOnly,
  required,
  disabled,
  onChange,
  onBlur,
  onKeyDown,
  onKeyPress,
  onKeyUp,
  isInvalid,
  desc,
  mask,
  msg,
  cy,
  tabIndex,
}: IProp): React.ReactElement => (
  <div className="m-inputs inputMask form-group">
    <Form.Label
      style={{ marginBottom: '0' }}
      className={classNames((isInvalid && 'inputMask__label-error') || (disabled && 'inputMask__label-disabled'))}
      htmlFor={id}
    >
      {label}
    </Form.Label>
    <ReactInputMask
      mask={mask}
      alwaysShowMask={false}
      id={id}
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      onKeyDown={onKeyDown}
      onKeyPress={onKeyPress}
      onKeyUp={onKeyUp}
      placeholder={placeholder}
      readOnly={readOnly}
      required={required}
      disabled={disabled}
      aria-describedby={desc}
      className={classNames(`form-control ${isInvalid && 'inputMask__error'}`)}
      data-cy={cy}
      tabIndex={tabIndex}
    />
    {isInvalid && <div className="invalid-feedback d-block">{msg}</div>}
  </div>
);

InputMask.defaultProps = { tabIndex: 0 };

export default InputMask;
