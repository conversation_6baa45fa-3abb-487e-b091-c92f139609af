import React from 'react';
import { useAnalytics } from '../../contexts/AnalyticsContext';
import LogoWhatsapp from '../../statics/whatsapp.png';
import ImageFluid from '../ImageFluid';
import './styles.scss';

const WhatsappButton = (): React.ReactElement => {
  const { trackEvent } = useAnalytics();

  return (
    <div className="boxFlutuante">
      <a
        href="https://api.whatsapp.com/send?phone=5541996858551"
        target="_blank"
        rel="noreferrer"
        onClick={() => {
          trackEvent('Order Whatsapp', {
            action: 'Clicou no botão do Whatsapp à direita da página',
          });
        }}
      >
        <ImageFluid url={LogoWhatsapp} />
      </a>
    </div>
  );
};

export default WhatsappButton;
