.icButton {
  margin-left: auto;
  display: flex;
  border: none;
  background: transparent;
  justify-self: flex-end;
}

.alignButton {
  display: flex;
  justify-content: flex-end;
  margin-top: 4rem;
  margin-bottom: 1.4rem;
  text-align: center;
  button {
    padding: 0.5rem 1rem;
    height: 2.125rem;
  }
}

.without__border__button {
  width: fit-content;
  margin-right: 1rem;
  padding: 0.5rem 1rem;
  border: 1px solid black;
}

.hidden__input {
  display: none;
}

.crop__image {
  display: flex;
  justify-content: center;
  img {
    object-fit: contain;
  }
}

.style {
  .modal-header {
    background-color: #201e40;
    .modal-title {
      color: var(--white-100);
    }
  }
  .modal-content {
    overflow: auto;
  }
}
