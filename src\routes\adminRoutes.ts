import React, { lazy } from 'react';
import Dashboard from '../pages/Dashboard';

const ConfirmAccount = lazy(() => import('../pages/Authentication/ConfirmAccount'));
const Login = lazy(() => import('../pages/Authentication/Login'));
const ForgotPassword = lazy(() => import('../pages/Authentication/ForgotPassword'));
const ResetPassword = lazy(() => import('../pages/Authentication/ResetPassword'));

const Error = lazy(() => import('../pages/Error'));

interface IProp {
  path: string;
  component: React.FunctionComponent;
  public?: boolean;
}

export const adminRoutes: IProp[] = [
  {
    path: '/confirm-account',
    component: ConfirmAccount,
    public: true,
  },
  {
    path: '/forgotPassword',
    component: ForgotPassword,
    public: true,
  },
  {
    path: '/resetPassword/:token',
    component: ResetPassword,
    public: true,
  },
  {
    path: '/registration/:token',
    component: ResetPassword,
    public: true,
  },
  {
    path: '/login/:hash?',
    component: Login,
    public: true,
  },
  {
    path: '/login/new-quote',
    component: Login,
    public: true,
  },
  {
    path: '/',
    component: Dashboard,
  },
  {
    path: '/dashboard',
    component: Dashboard,
  },
  {
    path: '*',
    component: Error,
    public: true,
  },
];
