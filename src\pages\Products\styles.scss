.table {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: 8px;
  overflow-y: hidden;

  & .rs-table-cell-header-icon-sort {
    color: #bac5c5;
  }

  & .rs-table-scrollbar-vertical {
    opacity: unset;

    & .rs-table-scrollbar-handle {
      background-color: #201e40;
    }
  }

  & .rs-table-cell-content {
    vertical-align: text-bottom !important;
  }

  & .rs-table-scrollbar-horizontal {
    opacity: unset;
    border-radius: 4px;

    @media (max-width: 820px) {
      padding-left: 0rem !important;
      padding-right: 0rem !important;
      margin-left: 0rem !important;
      margin-right: 0rem !important;
    }

    & .rs-table-scrollbar-handle {
      background-color: #201e40;
    }
  }

  & .rs-table-cell {
    font-family: 'Poppins';
    border-right: 1px solid #dfdfdf !important;
  }

  & .rs-table-body-row-wrapper {
    border-top: none;
  }

  & .rs-table-row {
    color: #bac5c5;
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    padding-left: 0;
  }
}

.table > :not(caption) > * > * {
  padding: 0;
}

.custom-row .rs-table-cell {
  background: #f1f1f1;
}

.filter {
  & .rs-picker-toggle {
    padding-bottom: 4rem;
  }
}

.alingImageTable {
  border-radius: 2px;
}

.btnCollums {
  color: '#201E40';
  font-family: 'Lato';
  background-color: #ffffff;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  font-size: 14px;
  font-family: 'Poppins';
  color: #201e40;
  line-height: 21px;
  padding: 0;
}

.columns {
  flex-direction: column;
  background-color: #ffffffff;
  border-radius: 8px;
  padding: 1rem 0;
  position: absolute;
  z-index: 999;
  top: 3rem;
  right: 3rem;
  box-shadow: 3px 3px 10px rgba(0 0 0 / 0.5);
}

.downloadIC {
  border-radius: 50%;
  display: inline-block;
  height: 2rem;
  width: 2rem;
  background-color: var(--white-100);
  cursor: pointer;

  @media (max-width: 820px) {
    display: none !important;
  }
}

.containerBackground {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  padding: 0;
  margin: 0;

  & .pagination {
    padding: 20px;
    justify-content: space-between;
    width: 100%;

    span {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 300;
      font-size: 14px;
      line-height: 21px;
      margin-right: 8px;

      @media (max-width: 820px) {
        margin-right: 2px !important;
        white-space: nowrap;
      }
    }

    & .pageGroup {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 2;

      & .selectPerPage button {
        background-color: #ffffff;
        color: #323c32;
        border: 1px solid #bac5c5;
        border-radius: 4px;

        font-family: 'Poppins';
        font-style: normal;
        font-weight: 300;
        font-size: 14px;
        line-height: 21px;

        @media (max-width: 820px) {
          margin-right: 15px !important;
        }
      }

      .dropdown-toggle::after {
        color: #18b680;
      }
    }

    .paginator {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .navigator {
      font-size: 1.8rem;
      cursor: pointer;
      color: #18b680;
    }
  }
}

.headerTitle {
  text-transform: uppercase !important;
  font-family: 'Poppins' !important;
  color: #8e8e93;
}

.productsLimit {
  max-width: 96rem;
  margin-left: auto;
  margin-right: auto;
}

.selectFilter {
  z-index: 999;
  position: relative;
  .css-1s2u09g-control {
    min-height: 2.3rem !important;
    max-height: 2.3rem !important;
    overflow: auto !important;
    z-index: 999;
    border: none !important;
    border-radius: 1px;
  }
  .css-1pahdxg-control {
    min-height: 2.3rem !important;
    max-height: 2.3rem !important;
  }
}

.customInput {
  border: none;
  line-height: 2.2;
  margin-top: 0.5rem;
  min-width: 31.25rem;
  border-radius: 2px;
  @media (max-width: 768px) {
    min-width: 100% !important;
    margin-left: 0.9rem !important;
    margin-bottom: 0.5rem !important;
  }
}

.imagePadding {
  .rs-table-cell-content {
    padding: 4px 7px;
  }
}
input:focus {
  outline: none !important;
  border-color: #719ece;
  box-shadow: 0 0 10px #719ece;
}
.selectMulti {
  max-width: 20rem;
  width: 20rem;
  @media (max-width: 768px) {
    width: 100% !important;
    margin-left: 0.9rem !important;
    max-width: 100% !important;
  }
}

.imageFilter {
  background-color: white;
  min-height: 2.3rem;
  min-width: 2rem;
  text-align: center;
  margin-top: 0.5rem;

  @media (max-width: 768px) {
    display: none;
  }
}

.colorImage {
  margin-top: 0.5rem;
  margin-left: 2rem;
  width: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  @media (max-width: 768px) {
    display: none;
  }
}
.selectCustomer {
  z-index: 999;
  position: relative;
  margin-bottom: 0.1rem;
  margin-left: 1rem;
  .css-1s2u09g-control {
    min-height: 2.3rem;
    max-height: 2.3rem;
    overflow: auto !important;
    z-index: 999;
    border: none !important;
    border-radius: 1px;
  }
  .css-1pahdxg-control {
    min-height: 2.3rem;
    max-height: 2.3rem;
  }
}

.alignMobileSS {
  padding-left: 1rem;
}

.visible {
  padding-right: 8px;
  padding-left: 8px;
  background-color: #95daaf;
  border-radius: 4px;
  color: #212121;
  width: max-content;
  font-family: 'Poppins' !important;
}

.occult {
  padding-right: 8px;
  padding-left: 8px;
  background-color: #bac5c5;
  border-radius: 4px;
  color: #212121;
  width: max-content;
  font-family: 'Poppins' !important;
}
.specieStyle {
  color: var(--secondary-secondary-500, #203245);
  font-feature-settings: 'clig' off, 'liga' off;

  font-family: Poppins;
  font-size: 0.8125rem;
  font-style: normal;
  font-weight: 400;
  line-height: 1.25rem;
}
