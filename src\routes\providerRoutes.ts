import React, { lazy } from 'react';
import Dashboard from '../pages/Dashboard';
import Products from '../pages/Products';
import Customers from '../pages/Customers';
import SupplyChain from '../pages/SupplyChain';
import DashboardImporter from '../pages/DashboardImporter';
import ViewSupply from '../pages/DashboardImporter/Visualizar';
const NewClient = lazy(() => import('../pages/Customers/NewCustomer'));
const AddProfile = lazy(() => import('../pages/Products/Actions'));
const NewSupply = lazy(() => import('../pages/SupplyChain/New'));
const Fornecedores = lazy(() => import('../pages/Fornecedores'));
const NovoFornecedor = lazy(() => import('../pages/Fornecedores/NewSupplier'));
const Industry = lazy(() => import('../pages/Industry'));
const ConfirmAccount = lazy(() => import('../pages/Authentication/ConfirmAccount'));
const Login = lazy(() => import('../pages/Authentication/Login'));
const ForgotPassword = lazy(() => import('../pages/Authentication/ForgotPassword'));
const ResetPassword = lazy(() => import('../pages/Authentication/ResetPassword'));
const Error = lazy(() => import('../pages/Error'));
const Landing = lazy(() => import('../pages/Landing'));
const NewUserForm = lazy(() => import('../pages/Authentication/NewUserForm'));
const RootRedirect = lazy(() => import('../components/RootRedirect'));

interface IProp {
  path: string;
  component: React.FunctionComponent;
  public?: boolean;
}

export const providerRoutes: IProp[] = [
  {
    path: '/',
    component: RootRedirect,
    public: true,
  },
  {
    path: '/landing',
    component: Landing,
    public: true,
  },
  {
    path: '/confirm-account',
    component: ConfirmAccount,
    public: true,
  },
  {
    path: '/forgotPassword',
    component: ForgotPassword,
    public: true,
  },
  {
    path: '/resetPassword/:token',
    component: ResetPassword,
    public: true,
  },
  {
    path: '/registration/:token',
    component: ResetPassword,
    public: true,
  },
  {
    path: '/login/:hash?',
    component: Login,
    public: true,
  },
  {
    path: '/login/new-quote',
    component: Login,
    public: true,
  },
  {
    path: '/dashboard',
    component: Dashboard,
  },
  {
    path: '/dashboard-importer',
    component: DashboardImporter,
  },
  {
    path: '/view-supply/:id',
    component: ViewSupply,
  },
  {
    path: '/industry',
    component: Industry,
  },
  {
    path: '/clients',
    component: Customers,
  },
  {
    path: '/new-client/:id?',
    component: NewClient,
  },
  {
    path: '/products',
    component: Products,
  },
  {
    path: '/add-product',
    component: AddProfile,
  },
  {
    path: '/supplychain',
    component: SupplyChain
  },
  {
    path: '/new-supply/:id?',
    component: NewSupply,
  },
  {
    path: '/fornecedor',
    component: Fornecedores,
  },
  {
    path: '/new-fornecedor/:id?',
    component: NovoFornecedor,
  },
  {
    path: '/newUserForm',
    component: NewUserForm,
    public: true,
  },
  {
    path: '*',
    component: Error,
    public: true,
  },
];
