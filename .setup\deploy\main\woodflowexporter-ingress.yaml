apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: woodflowexporter-web-ingress
  namespace: main
  annotations:
    kubernetes.io/ingress.class: 'nginx'
spec:
  rules:
    - host: www.woodflowexporter.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: woodflowexporter-web-service
                port:
                  number: 80
