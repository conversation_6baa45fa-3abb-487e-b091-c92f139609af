import { Links, Meta } from './IMeta';

export interface ISelectOption {
  length?: any;
  label?: string;
  value?: number | boolean | string | object;
  specie?: any;
  certificates?: any;
  quality?: any;
  glue?: any;
  layers?: any;
}

export interface IProduct {
  produto_id?: number;
  cod_produto?: string;
  nom_produto_pt?: string;
  nom_produto_en?: string;
  cod_produto_tp?: string;
  cod_especie?: string;
  cod_qualidade?: string;
  cod_cola?: string;
  list_certificado?: string;
}

export interface IProductSelectsDetail {
  id: number;
  description: string;
  description_ptbr: string;
}

export interface IProductSelects {
  data: IProductSelectsDetail[];
  links: Links;
  meta: Meta;
}

export interface Type {
  id: number;
  description: string;
  description_ptbr: string;
}

export interface Specie {
  id: number;
  description: string;
  description_ptbr: string;
}

export interface Quality {
  id: number;
  description: string;
  description_ptbr: string;
}

export interface Purpose {
  id: number;
  description: string;
  description_ptbr: string;
}

export interface Glue {
  id: number;
  description: string;
  description_ptbr: string;
}

export interface ProductComplete {
  id: number;
  uuid: string;
  description: string;
  description_ptbr: string;
  layers_observation: string;
  observation?: string;
  certificates: any[];
  thickness: {
    max: number;
    min: number;
    type: string;
  }[];
  length: {
    max: number;
    min: number;
    type: string;
  }[];
  width: {
    value: any;
    max: number;
    min: number;
    type: string;
  }[];
  layers: string[];
  created_at: string;
  inactived: number;
  image: any;
  visibility: number;
  name: string;
  name_ptbr: string;
  species: any;
  glues: any;
  qualities: any;
  finishes: any;
  type: any;
  specie: any;
  quality: any;
  purpose: any;
  glue: any;
  company: {
    id: number;
    uuid: string;
    name: string;
    phone: any;
    email: string;
    address: any;
    city: any;
    state: any;
    country: any;
    zip_code: any;
    coordinates: any;
    rules: string;
    notes: any;
    status: string;
    created_at: string;
  };
}

export interface ResponseProduct {
  data: ProductComplete[];
  links: Links;
  meta: Meta;
}

export interface ProjectById {
  data: ProductComplete;
}

export interface CompaniesProduct {
  id: number;
  length: number;
  width: number;
  thickness: number;
  layers?: number;
  description: string;
  type: string;
  specie: string;
  quality: string;
  glue: string;
  certificates: string;
}

export interface CompaniesProductResponse {
  data: CompaniesProduct[];
}
