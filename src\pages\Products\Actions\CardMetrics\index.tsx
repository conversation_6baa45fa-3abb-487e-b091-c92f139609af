import React from 'react';
import { Col, Row } from 'react-bootstrap';
import { TagsInput } from 'react-tag-input-component';
import { useTranslation } from 'react-i18next';
import Card from '../../../../components/Card';
import Text from '../../../../components/Text';
import Select from '../../../../components/Select';
import InputCurrency from '../../../../components/InputCurrency';
import checkObjectIsEmpty from '../../../../utils/checkObjectIsEmpty';
import { ISelectOption } from '../../../../interfaces';
import { handleBeforeAddValidate } from '../../../../utils/handleBeforeAddValidade';
import '../styles.scss';

const CardMetrics = ({
  selectMetrics,
  setTypeThickness,
  typeThickness,
  setTypeLength,
  typeLength,
  typeWidth,
  setTypeWidth,
  thickness,
  setThickness,
  width,
  setWidth,
  length,
  setLength,
  selectMetricsFiltered,
}: any): React.ReactElement => {
  const { t } = useTranslation();
  const [showErrorLength, setShowErrorLength] = React.useState<boolean>(false);
  const [showErrorWidth, setShowErrorWidth] = React.useState<boolean>(false);
  const [showErrorThickness, setShowErrorThickness] = React.useState<boolean>(false);

  // const convertArrayToNumbers = (arr: any[]): number[] => arr.map(item => Number(item));
  const convertArrayToNumbers = (arr: string[]): number[] =>
    arr.map((item) => {
      if (item.endsWith(',00')) {
        const num = Number(item.slice(0, -3).replace('.', ''));
        return isNaN(num) ? 0 : num;
      }
      const num = Number(item.replace('.', '').replace(',', '.'));
      return isNaN(num) ? 0 : num;
    });

  const formatToCurrency = (numbers: number[]): string[] =>
    numbers.sort((a, b) => a - b).map((num) => num.toLocaleString('pt-BR', { minimumFractionDigits: 2 }));

  const handleTagsThicknessChange = (newTags: string[]): void => {
    const thicknessTmp = convertArrayToNumbers(newTags);
    const thicknessStr = formatToCurrency(thicknessTmp);

    if (thicknessStr.length <= 100) {
      setThickness({ ...thickness, list: thicknessStr });
      setShowErrorThickness(false);
    } else {
      setThickness({ ...thickness, list: thicknessStr.slice(0, 100) });
      setShowErrorThickness(false);
    }
  };

  const handleTagsWidthChange = (newTags: string[]): void => {
    const widthTmp = convertArrayToNumbers(newTags);
    const widthStr = formatToCurrency(widthTmp);

    if (widthStr.length <= 100) {
      setWidth({ ...width, list: widthStr });
      setShowErrorWidth(false);
    } else {
      setWidth({ ...width, list: widthStr.slice(0, 100) });
      setShowErrorWidth(false);
    }
  };

  const handleTagsLengthChange = (newTags: string[]): void => {
    const lengthTmp = convertArrayToNumbers(newTags);
    const lengthStr = formatToCurrency(lengthTmp);

    if (lengthStr.length <= 100) {
      setLength({ ...length, list: lengthStr });
      setShowErrorLength(false);
    } else {
      setLength({ ...length, list: lengthStr.slice(0, 100) });
      setShowErrorLength(false);
    }
  };

  return (
    <Card
      cy="card-test-product"
      className="style"
      renderBody={
        <Col md={12}>
          <Text as="span" size="1.2rem" weight={600} color="#201E40">
            {t('labels.dimensions')}
          </Text>
          <Row>
            <Col md={3} className="mt-4">
              <Select
                id={`select${t('language')}`}
                renderKey
                loadOptions={selectMetrics}
                cacheOptions
                defaultOptions
                title={t('labels.typeMetricThickness')}
                placeholder={t('labels.selectMetric')}
                noOptionsMessage={() => 'Loading'}
                loadingMessage={() => 'Carregando...'}
                onChange={(event: ISelectOption) => {
                  setTypeThickness(event);
                }}
                value={!checkObjectIsEmpty(typeThickness) ? typeThickness : null}
                cy="test-selectMetrics"
              />
            </Col>
            {typeThickness?.value === 'list' && (
              <Col md={9} className="mt-4">
                <span>{t('labels.thickness')}</span>
                <Col md={12} className="mt-2">
                  <TagsInput
                    value={thickness.list}
                    onChange={handleTagsThicknessChange}
                    name="layers"
                    placeHolder={t('labels.sendThickness')}
                    beforeAddValidate={(tag) => handleBeforeAddValidate(tag, setShowErrorThickness)}
                  />
                  {showErrorThickness && <div className="error-message">{t('labels.genericErrorTag')}.</div>}
                </Col>
              </Col>
            )}
            {typeThickness?.value === 'unique' && (
              <Col md={9} className="mt-4">
                <Col md={3} className="mt-1">
                  <InputCurrency
                    id="thickness"
                    cy="cy-thickness"
                    label={t('labels.onlyThickness')}
                    placeholder={t('labels.sendOnlyThickness')}
                    value={thickness.unique}
                    onChange={(e) => {
                      setThickness({ ...thickness, unique: e.target.value });
                    }}
                  />
                </Col>
              </Col>
            )}
            {typeThickness?.value === 'range' && (
              <Col md={9} className="d-flex gap-4 mt-4">
                <Col md={3} className="mt-1">
                  <InputCurrency
                    id="thickness"
                    cy="cy-thickness"
                    label={t('labels.of')}
                    placeholder={t('labels.sendOnlyThickness')}
                    value={thickness.range.min || ''}
                    onChange={(e) => {
                      setThickness({
                        ...thickness,
                        range: {
                          min: e.target.value,
                          max: thickness.range.max || '',
                        },
                      });
                    }}
                  />
                </Col>
                <Col md={3} className="mt-1">
                  <InputCurrency
                    id="thickness"
                    cy="cy-thickness"
                    label={t('labels.until')}
                    placeholder={t('labels.sendOnlyThickness')}
                    value={thickness.range.max || ''}
                    onChange={(e) => {
                      setThickness({
                        ...thickness,
                        range: {
                          min: thickness.range.min || '',
                          max: e.target.value,
                        },
                      });
                    }}
                  />
                </Col>
                <Col md={6} className="mt-4">
                  <p className="genericMessage mt-2">{t('labels.genericMessageValues')}</p>
                </Col>
              </Col>
            )}
          </Row>
          <Row>
            <Col md={3} className="mt-4">
              <Select
                id={`select${t('language')}`}
                renderKey
                loadOptions={selectMetricsFiltered}
                cacheOptions
                defaultOptions
                title={t('labels.typeMetricWidth')}
                placeholder={t('labels.selectMetric')}
                noOptionsMessage={() => 'Loading'}
                loadingMessage={() => 'Carregando...'}
                onChange={(event: ISelectOption) => {
                  setTypeWidth(event);
                }}
                value={!checkObjectIsEmpty(typeWidth) ? typeWidth : null}
                cy="test-selectMetrics"
              />
            </Col>
            {typeWidth?.value === 'list' && (
              <Col md={9} className="mt-4">
                <span>{t('labels.width')}</span>
                <Col md={12} className="mt-2">
                  <TagsInput
                    value={width.list}
                    onChange={handleTagsWidthChange}
                    name="width"
                    placeHolder={t('labels.sendWidth')}
                    beforeAddValidate={(tag) => handleBeforeAddValidate(tag, setShowErrorWidth)}
                  />
                  {showErrorWidth && <div className="error-message">{t('labels.genericErrorTag')}.</div>}
                </Col>
              </Col>
            )}
            {typeWidth?.value === 'unique' && (
              <Col md={9} className="mt-4">
                <Col md={3} className="mt-1">
                  <InputCurrency
                    id="width"
                    cy="cy-width"
                    label={t('labels.onlyWidth')}
                    placeholder={t('labels.sendOnlyWidth')}
                    value={width.unique}
                    onChange={(e) => {
                      setWidth({ ...width, unique: e.target.value });
                    }}
                  />
                </Col>
              </Col>
            )}
            {typeWidth?.value === 'range' && (
              <Col md={9} className="d-flex gap-4 mt-4">
                <Col md={3} className="mt-1">
                  <InputCurrency
                    id="width"
                    cy="cy-width"
                    label={t('labels.of')}
                    placeholder={t('labels.sendOnlyWidth')}
                    value={width.range.min || ''}
                    onChange={(e) => {
                      setWidth({
                        ...width,
                        range: {
                          min: e.target.value,
                          max: width.range.max || '',
                        },
                      });
                    }}
                  />
                </Col>
                <Col md={3} className="mt-1">
                  <InputCurrency
                    id="width"
                    cy="cy-width"
                    label={t('labels.until')}
                    placeholder={t('labels.sendOnlyWidth')}
                    value={width.range.max || ''}
                    onChange={(e) => {
                      setWidth({
                        ...width,
                        range: {
                          min: width.range.min || '',
                          max: e.target.value,
                        },
                      });
                    }}
                  />
                </Col>
                <Col md={6} className="mt-4">
                  <p className="genericMessage mt-2">{t('labels.genericMessageValues')}</p>
                </Col>
              </Col>
            )}
          </Row>
          <Row>
            <Col md={3} className="mt-4">
              <Select
                id={`select${t('language')}`}
                renderKey
                loadOptions={selectMetricsFiltered}
                cacheOptions
                defaultOptions
                title={t('labels.typeMetricLength')}
                placeholder={t('labels.selectMetric')}
                noOptionsMessage={() => 'Loading'}
                loadingMessage={() => 'Carregando...'}
                onChange={(event: ISelectOption) => {
                  setTypeLength(event);
                }}
                value={!checkObjectIsEmpty(typeLength) ? typeLength : null}
                cy="test-selectMetrics"
              />
            </Col>
            {typeLength?.value === 'list' && (
              <Col md={9} className="mt-4">
                <span>{t('labels.length')}</span>
                <Col md={12} className="mt-2">
                  <TagsInput
                    value={length.list}
                    onChange={handleTagsLengthChange}
                    name="length"
                    placeHolder={t('labels.sendLength')}
                    beforeAddValidate={(tag) => handleBeforeAddValidate(tag, setShowErrorLength)}
                  />
                  {showErrorLength && <div className="error-message">{t('labels.genericErrorTag')}.</div>}
                </Col>
              </Col>
            )}
            {typeLength?.value === 'unique' && (
              <Col md={9} className="mt-4">
                <Col md={3} className="mt-1">
                  <InputCurrency
                    id="length"
                    cy="cy-length"
                    label={t('labels.onlyLength')}
                    placeholder={t('labels.sendOnlyLength')}
                    value={length.unique}
                    onChange={(e) => {
                      setLength({ ...length, unique: e.target.value });
                    }}
                  />
                </Col>
              </Col>
            )}
            {typeLength?.value === 'range' && (
              <Col md={9} className="d-flex gap-4 mt-4">
                <Col md={3} className="mt-1">
                  <InputCurrency
                    id="length"
                    cy="cy-length"
                    label={t('labels.of')}
                    placeholder={t('labels.sendOnlyLength')}
                    value={length.range.min || ''}
                    onChange={(e) => {
                      setLength({
                        ...length,
                        range: {
                          min: e.target.value,
                          max: length.range.max || '',
                        },
                      });
                    }}
                  />
                </Col>
                <Col md={3} className="mt-1">
                  <InputCurrency
                    id="length"
                    cy="cy-length"
                    label={t('labels.until')}
                    placeholder={t('labels.sendOnlyLength')}
                    value={length.range.max || ''}
                    onChange={(e) => {
                      setLength({
                        ...length,
                        range: {
                          min: length.range.min || '',
                          max: e.target.value,
                        },
                      });
                    }}
                  />
                </Col>
                <Col md={6} className="mt-4">
                  <p className="genericMessage mt-2">{t('labels.genericMessageValues')}</p>
                </Col>
              </Col>
            )}
          </Row>
        </Col>
      }
    />
  );
};

export default CardMetrics;
