import React from 'react';
import { Card, CardProps } from 'react-bootstrap';
import './styles.scss';

export interface IProp extends CardProps {
  renderBody?: React.ReactNode;
  cy: string;
}

const CardComponent = ({ renderBody, cy, ...props }: IProp): React.ReactElement => (
  <Card data-testid="card" className="card-component" {...props} data-cy={cy}>
    <Card.Body className="card-component__body">{renderBody}</Card.Body>
  </Card>
);

CardComponent.defaultProps = { renderBody: '' };

export default CardComponent;
