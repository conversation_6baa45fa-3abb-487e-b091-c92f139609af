import React, { useState } from 'react';
import { Container, Row, Col, Form, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom';
import { isValidEmail } from '@brazilian-utils/brazilian-utils';

import Section from '../../../components/Section';
import CustomInput from '../../../components/Input/CustomInput';
import InputMask from '../../../components/InputMask';
import Text from '../../../components/Text';
import { useLoader } from '../../../contexts/LoaderContext';
import toastMsg, { ToastType } from '../../../utils/toastMsg';
import './styles.scss';
import UsersService, { INewUserForm } from '../../../services/users.service';
import Recaptcha from '../../../components/Recaptcha';
import grecaptcha from '../../../services/recaptcha.service';

const NewUserForm = (): React.ReactElement => {
  const { t } = useTranslation();
  const { setShowLoader } = useLoader();
  const history = useHistory();

  // Company data
  const [companyName, setCompanyName] = useState<string>('');
  const [cnpj, setCnpj] = useState<string>('');

  // User data
  const [userName, setUserName] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [emailError, setEmailError] = useState<boolean>(false);
  const [phone, setPhone] = useState<string>('');
  const [terms, setTerms] = useState<boolean>(false);

  const MSG = t('exceptions.invalidEmail');

  // Função para remover máscara do CNPJ
  const removeCnpjMask = (value: string): string => {
    return value.replace(/\D/g, '');
  };

  // Função para remover máscara do telefone
  const removePhoneMask = (value: string): string => {
    return value.replace(/\D/g, '');
  };

  const handleSubmit = async (): Promise<void> => {
    try {
      setShowLoader(true);

      try {
        // Obter token do reCAPTCHA
        const recaptchaToken = await grecaptcha('register');

        const userData: INewUserForm = {
          name: userName,
          email: email,
          company_name: companyName,
          cnpj: removeCnpjMask(cnpj),
          phone: removePhoneMask(phone),
          recaptcha_token: recaptchaToken,
        };

        // Usar try/catch específico para a chamada da API
        await UsersService.newUserForm(userData);

        // Em caso de sucesso, exibir mensagem e redirecionar para landing page
        toastMsg(ToastType.Success, t('response.newUserEUDR'));
        history.push('/');
      } catch (apiError) {
        toastMsg(ToastType.Error, t('exceptions.emailAlready'));
        // Não redireciona - permanece na página atual
      }
    } catch (error) {
      toastMsg(ToastType.Error, t('exceptions.generalError'));
    } finally {
      setShowLoader(false);
    }
  };

  return (
    <Section title="Cadastro de Exportador" description="Formulário de cadastro para exportadores">
      <Container className="py-5">
        <Row className="justify-content-center">
          <Col md={10} lg={8}>
            <div className="exporter-form bg-white p-4 p-md-5 rounded shadow">
              <Text className="mb-4 form-title" as="h2" size="2rem" weight={600} color="#201e40">
                {t('landing.newUserExporter')}
              </Text>

              <div className="mb-4 form-section">
                <Text as="h5" weight={600} color="#201e40" className="section-title">
                  {t('landing.companyData')}
                </Text>
                <hr />

                <div className="form-field">
                  <Row className="gx-4">
                    <Col md={8}>
                      <label htmlFor="companyName" className="text-left">
                        {t('labels.companyName')}*
                      </label>
                      <CustomInput
                        maxLength={255}
                        type="text"
                        value={companyName}
                        onChange={(e) => setCompanyName(e.target.value)}
                        id="companyName"
                        name="companyName"
                        desc="Company name"
                        required
                        cy="test-companyName"
                        className="full-width-input"
                      />
                    </Col>

                    <Col md={4} className="mt-2">
                      <InputMask
                        id="cnpj"
                        label="CNPJ*"
                        placeholder="00.000.000/0000-00"
                        mask="99.999.999/9999-99"
                        value={cnpj}
                        onChange={(e) => setCnpj(e.target.value)}
                        desc="CNPJ"
                        required
                        cy="test-cnpj"
                      />
                    </Col>
                  </Row>
                </div>
              </div>

              <div className="mb-4 form-section">
                <Text as="h5" weight={600} color="#201e40" className="section-title">
                  {t('landing.userData')}
                </Text>
                <hr />

                <div className="form-field">
                  <Row>
                    <Col md={12}>
                      <label htmlFor="userName">{t('landing.name')}*</label>
                      <CustomInput
                        maxLength={255}
                        type="text"
                        value={userName}
                        onChange={(e) => setUserName(e.target.value)}
                        id="userName"
                        name="userName"
                        desc="User name"
                        required
                        cy="test-userName"
                        className="full-width-input"
                      />
                    </Col>
                  </Row>
                </div>

                <div className="form-field">
                  <Row className="gx-4">
                    <Col md={8}>
                      <label htmlFor="email">{t('landing.email')}*</label>
                      <CustomInput
                        maxLength={255}
                        type="text"
                        value={email}
                        onChange={(e) => {
                          setEmailError(false);
                          setEmail(e.target.value);
                        }}
                        onBlur={() => {
                          if (!isValidEmail(email)) {
                            setEmailError(true);
                          }
                        }}
                        id="email"
                        name="email"
                        desc="E-mail"
                        required
                        cy="test-email"
                        isInvalid={emailError}
                        msg={MSG}
                        className="full-width-input"
                      />
                    </Col>
                    <Col md={4} className="mt-2">
                      <InputMask
                        id="phone"
                        label={`${t('landing.phone')}*`}
                        placeholder="(00) 00000-0000"
                        mask="(99) 99999-9999"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        desc="Phone"
                        required
                        cy="test-phone"
                      />
                    </Col>
                  </Row>
                </div>
              </div>

              <div className="mb-4 terms-section">
                <Form.Check
                  type="checkbox"
                  id="terms"
                  checked={terms}
                  onChange={() => setTerms(!terms)}
                  className="mb-3"
                  label={
                    <span>
                      Ao criar sua conta você está concordando com as nossas{' '}
                      <a
                        href="https://woodflow.com.br/politica-de-cookies/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="privacy-link"
                      >
                        Políticas de Privacidade
                      </a>
                    </span>
                  }
                />
              </div>

              <div className="d-flex justify-content-end">
                <Button variant="outline-secondary" className="me-2" onClick={() => history.push('/')}>
                  {t('buttons.cancel')}
                </Button>
                <Button
                  variant="success"
                  onClick={handleSubmit}
                  disabled={
                    !companyName ||
                    !cnpj ||
                    removeCnpjMask(cnpj).length !== 14 ||
                    !userName ||
                    !email ||
                    !phone ||
                    removePhoneMask(phone).length < 10 ||
                    !terms
                  }
                >
                  {t('buttons.save')}
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
      <Recaptcha />
    </Section>
  );
};

export default NewUserForm;
