import { User } from './IBusiness';
import { Meta, <PERSON>s } from './IMeta';

interface Status {
  etapa_id: number;
  nom_etapa_pt: string;
  nom_etapa_en: string;
  num_ordem: number;
  des_cor_hexa_fundo: string;
  des_cor_hexa_fonte: string;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface CreateBusiness {
  status_id: number;
  shipment_estimate: string;
  arrival_estimate: string;
  notes: string;
  send_notification: boolean;
  emails_notification: string[] | undefined;
}

interface FollowUp {
  uuid: string;
  business_id: number;
  notes: string;
  shipment_estimate: string;
  arrival_estimate: string;
  created_at: string;
  status: Status;
  user: User;
}

export interface IFollowUp {
  data: FollowUp[];
  links: Links;
  meta: Meta;
}
