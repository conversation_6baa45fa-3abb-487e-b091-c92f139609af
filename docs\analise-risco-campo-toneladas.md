# Análise e Risco - Adição de Campo "Total em Toneladas"

## Resumo Executivo

Este documento apresenta uma análise detalhada dos riscos e impactos da implementação de um novo campo "Total em Toneladas" nas telas de Cadeia de Fornecimento (aba Produtos) e Espécies de Madeira Utilizada. O objetivo é manter os campos existentes de quantidade em metros cúbicos (m³) e adicionar campos complementares para quantidade em toneladas, tanto para matéria-prima quanto para produtos finais.

## Escopo da Análise

### Telas Afetadas

1. **Cadeia de Fornecimento - Aba Produtos** (`src/pages/SupplyChain/New/Produto/index.tsx`)
2. **Espécies de Madeira Utilizada** após seleção de floresta (`src/pages/SupplyChain/New/Specie/index.tsx`)

### Campos Atuais Identificados

- **Quantidade em m³ do Produto**: Campo `quantity` com máscara decimal (formato: XXX.XXX,XXX)
- **Quantidade de Matéria-Prima em m³**: Campo `mp_qtde` nas espécies de madeira
- **Descrição do Produto**: Campo `productDescription`

## Análise Técnica

### 1. Estrutura de Dados Atual

#### Interfaces Existentes

```typescript
// src/services/supplyChain.service.ts
export interface IPropsSaveProductSupplyChain {
  product_description: string;
  product_quantity: string; // Atualmente em m³
  status: 'A';
  supply_chain_id: string;
}

export interface IPropsSaveSupplyChainSpecie {
  mp_qtde: string; // Quantidade matéria-prima em m³
  mp_nome: string;
  floresta_nome: string;
  fornecedor_nome: string;
  // ... outros campos
}

// src/services/floresta.service.ts
export interface IpropsMateriaPrima {
  id?: number;
  nome: string;
  nome_cientifico: string;
  quantidade?: string; // Atualmente em m³
  status?: string;
}
```

### 2. Componentes de Interface Atuais

#### Tela de Produtos (Supply Chain)

- **Localização**: `src/pages/SupplyChain/New/Produto/index.tsx`
- **Campo Quantidade**: Linhas 298-325
- **Máscara**: Formato decimal com 3 casas decimais
- **Label**: `t('labels.productQuantityM3')` (Quantidade do Produto (m³))

#### Tela de Espécies de Madeira

- **Localização**: `src/pages/SupplyChain/New/Specie/index.tsx`
- **Campo Quantidade MP**: Linhas 797-821
- **Máscara**: Mesmo formato decimal
- **Label**: `t('labels.productQuantityM3')` (reutilizada)

### 3. Padrões de Máscara Identificados

```typescript
// Padrão atual para campos de quantidade
mask={(rawValue) => {
  const digits = rawValue.replace(/[^\d]/g, '');
  const decimalPart = [/\d/, /\d/, /\d/]; // 3 casas decimais

  const integerPart = [];
  for (let i = 0; i < digits.length - 3; i++) {
    integerPart.push(/\d/);
    if ((digits.length - 3 - i) % 3 === 1 && i < digits.length - 4) {
      integerPart.push('.');
    }
  }

  return [...integerPart, ',', ...decimalPart];
}}
```

## Análise de Riscos

### 1. Riscos Técnicos

#### Alto Risco

- **Inconsistência de Dados**: Risco de divergência entre valores em m³ e toneladas se não houver validação cruzada
- **Migração de Dados**: Dados existentes precisarão ser mantidos e novos campos inicializados
- **Validação de Formulários**: Necessidade de validar ambos os campos ou permitir apenas um

#### Médio Risco

- **Performance**: Adição de novos campos pode impactar consultas de banco de dados
- **Compatibilidade**: APIs existentes podem precisar ser atualizadas para suportar novos campos

#### Baixo Risco

- **Interface**: Adição de campos similares aos existentes com padrões já estabelecidos
- **Tradução**: Necessidade de novas chaves de tradução

### 2. Riscos de Negócio

#### Alto Risco

- **Conformidade EUDR**: Alterações podem afetar relatórios de conformidade se não implementadas corretamente
- **Treinamento**: Usuários precisarão entender quando usar cada unidade de medida
- **Auditoria**: Histórico de alterações deve ser mantido para ambas as unidades

#### Médio Risco

- **Relatórios**: Relatórios existentes podem precisar ser atualizados
- **Integração**: Sistemas externos podem esperar apenas uma unidade de medida

### 3. Riscos de UX/UI

#### Médio Risco

- **Confusão do Usuário**: Dois campos de quantidade podem gerar confusão
- **Espaço na Tela**: Adição de campos pode impactar layout, especialmente em mobile
- **Fluxo de Trabalho**: Usuários podem não saber qual campo preencher

## Impactos Identificados

### 1. Backend/API

- **Novos Campos**: Adição de campos `product_quantity_tons` e `mp_qtde_tons`
- **Validação**: Implementar validação para pelo menos um dos campos ser preenchido
- **Migração**: Script de migração para inicializar novos campos

### 2. Frontend

- **Interfaces TypeScript**: Atualização de todas as interfaces relacionadas
- **Componentes**: Adição de novos campos de input com máscaras similares
- **Validação**: Implementar validação client-side
- **Tradução**: Novas chaves para labels em português e inglês

### 3. Banco de Dados

- **Tabelas Afetadas**:
  - `supply_chain_products` (adicionar `quantity_tons`)
  - `supply_chain_species` (adicionar `mp_qtde_tons`)
  - `materia_prima` (adicionar `quantidade_tons`)

### 4. Testes

- **Testes Unitários**: Novos testes para validação de campos
- **Testes de Integração**: Verificar funcionamento com APIs
- **Testes E2E**: Fluxos completos de cadastro

## Estimativa de Esforço

### Desenvolvimento

- **Backend**: 16-24 horas

  - Migração de banco: 4h
  - Atualização de APIs: 8h
  - Validações: 4h
  - Testes: 8h

- **Frontend**: 20-28 horas
  - Atualização de interfaces: 4h
  - Componentes de UI: 8h
  - Validações: 4h
  - Tradução: 2h
  - Testes: 10h

### Testes e QA

- **Testes Funcionais**: 12-16 horas
- **Testes de Regressão**: 8-12 horas
- **Testes de Performance**: 4-6 horas

### Total Estimado: 60-86 horas

## Recomendações

### 1. Implementação Faseada

1. **Fase 1**: Implementar campos opcionais sem validação obrigatória
2. **Fase 2**: Adicionar validações e conversões automáticas
3. **Fase 3**: Implementar relatórios com ambas as unidades

### 2. Validações Recomendadas

- Pelo menos um campo de quantidade deve ser preenchido
- Se ambos preenchidos, validar consistência (com margem de tolerância)
- Implementar conversão automática baseada em densidade padrão da madeira

### 3. UX/UI Recomendações

- Agrupar campos m³ e toneladas visualmente
- Adicionar tooltips explicativos
- Implementar conversão automática opcional
- Manter campos m³ como primários (já estabelecidos)

### 4. Monitoramento

- Logs de uso dos novos campos
- Métricas de adoção
- Feedback dos usuários

## Conclusão

A implementação do campo "Total em Toneladas" é tecnicamente viável com riscos controláveis. O maior desafio será manter a consistência dos dados e garantir uma experiência de usuário clara. Recomenda-se uma implementação faseada com foco inicial na funcionalidade básica, seguida de melhorias incrementais baseadas no feedback dos usuários.

A estimativa total de 60-86 horas de desenvolvimento é conservadora e inclui tempo adequado para testes e validações, essenciais para manter a qualidade e confiabilidade do sistema WoodFlow.
