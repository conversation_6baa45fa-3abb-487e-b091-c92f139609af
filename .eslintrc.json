{"env": {"browser": true, "es2021": true}, "extends": ["plugin:react/recommended", "plugin:@typescript-eslint/recommended", "eslint-config-prettier"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react", "@typescript-eslint", "react-hooks"], "rules": {"no-console": ["error", {"allow": ["warn", "error"]}], "@typescript-eslint/no-unused-vars": ["error"], "no-prototype-builtins": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-non-null-asserted-optional-chain": "off", "no-param-reassign": "off", "react/no-array-index-key": "off", "no-restricted-globals": "off", "jsx-a11y/no-noninteractive-element-interactions": "off", "jsx-a11y/click-events-have-key-events": "off", "@typescript-eslint/no-explicit-any": "off", "no-use-before-define": "off", "no-nested-ternary": "off", "jsx-a11y/anchor-is-valid": "off", "@typescript-eslint/no-use-before-define": ["error"], "@typescript-eslint/explicit-function-return-type": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "import/prefer-default-export": "off", "react/prop-types": "off", "no-plusplus": "off", "react/jsx-props-no-spreading": "off", "no-shadow": "off", "@typescript-eslint/no-shadow": "error", "camelcase": "off", "react/require-default-props": "off", "react/react-in-jsx-scope": "off"}}