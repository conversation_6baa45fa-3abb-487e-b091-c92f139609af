import React from 'react';
import { Form } from 'react-bootstrap';
import classNames from 'classnames';
import './styles.scss';

interface ICoordinateInputProps {
  value: string;
  placeholder: string;
  id: string;
  label?: string;
  readOnly?: boolean;
  required?: boolean;
  disabled?: boolean;
  isInvalid?: boolean;
  desc?: string;
  msg?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  cy: string;
  type: 'latitude' | 'longitude';
  maxLength?: number;
}

const CoordinateInput = ({
  value,
  placeholder,
  id,
  label,
  readOnly = false,
  required = false,
  disabled = false,
  isInvalid = false,
  desc,
  msg,
  onChange,
  onBlur,
  cy,
  type,
  maxLength = 20,
}: ICoordinateInputProps): React.ReactElement => {
  // Garante que value nunca seja null ou undefined
  const safeValue = value || '';

  // Estado interno para garantir que o valor seja exibido corretamente
  const [displayValue, setDisplayValue] = React.useState(safeValue);

  // Atualiza o valor de exibição quando a prop value muda
  React.useEffect(() => {
    setDisplayValue(safeValue);
  }, [safeValue]);
  const formatCoordinate = (inputValue: string, isDeletion = false): string => {
    if (!inputValue) return '';

    // Se é uma deleção e o valor já tem formatação, mantém como está
    if (isDeletion && /[°'"]/.test(inputValue)) {
      return inputValue;
    }

    // Remove caracteres inválidos, mantém apenas números, símbolos DMS e direções cardeais
    // Aceita direções em maiúsculo e minúsculo, mas converte tudo para maiúsculo
    const cleanValue = inputValue.replace(/[^0-9°'",NSEWOLnsewol\s]/g, '').toUpperCase();

    // Se já está formatado e não é uma adição de números, mantém como está
    if (isDeletion) {
      return cleanValue;
    }

    // Aplica formatação progressiva baseada no que foi digitado
    let formatted = '';
    const numbers = cleanValue.replace(/[^0-9]/g, '');
    const hasDirection = /[NSEWOL]/.test(cleanValue);
    const direction = cleanValue.match(/[NSEWOL]/)?.[0] || '';

    // Se tem números, aplica formatação DMS
    if (numbers.length > 0) {
      // Graus (máximo 3 dígitos para longitude)
      if (numbers.length >= 1) {
        const maxDegreeDigits = type === 'longitude' ? 3 : 2;
        const degrees = numbers.substring(0, Math.min(maxDegreeDigits, numbers.length));
        formatted += degrees + '°';

        // Minutos (máximo 2 dígitos)
        if (numbers.length > maxDegreeDigits) {
          const minutes = numbers.substring(maxDegreeDigits, Math.min(maxDegreeDigits + 2, numbers.length));
          formatted += minutes + "'";

          // Segundos (máximo 2 dígitos + 2 decimais)
          if (numbers.length > maxDegreeDigits + 2) {
            const seconds = numbers.substring(maxDegreeDigits + 2, Math.min(maxDegreeDigits + 4, numbers.length));
            formatted += seconds;

            // Decimais dos segundos (máximo 2 dígitos)
            if (numbers.length > maxDegreeDigits + 4) {
              const decimal = numbers.substring(maxDegreeDigits + 4, Math.min(maxDegreeDigits + 6, numbers.length));
              formatted += ',' + decimal;
            }

            formatted += '"';
          }
        }
      }
    }

    // Adiciona direção se presente
    if (hasDirection && formatted.includes('"')) {
      formatted += ' ' + direction;
    }

    return formatted || cleanValue;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    const currentValue = displayValue;

    // Verifica se é uma operação de deleção (valor atual é menor que o anterior)
    const isDeletion = inputValue.length < currentValue.length;

    // Aplica formatação considerando se é deleção ou adição
    const formattedValue = formatCoordinate(inputValue, isDeletion);

    // Atualiza o estado interno
    setDisplayValue(formattedValue);

    if (onChange) {
      onChange(formattedValue);
    }
  };

  const convertDMSToDecimal = (dmsString: string): number => {
    // Converte formato DMS para decimal para validação
    // Aceita tanto direções em inglês (NSEW) quanto em português (NSOL)
    const match = dmsString.match(/^(\d{1,3})°(\d{1,2})'(\d{1,2})(,\d{1,2})?" ([NSEWOL])$/);
    if (!match) return NaN;

    const degrees = parseInt(match[1]);
    const minutes = parseInt(match[2]);
    const seconds = parseFloat(match[3] + (match[4] || '').replace(',', '.'));
    const direction = match[5];

    let decimal = degrees + minutes / 60 + seconds / 3600;

    // Aplica sinal baseado na direção (inclui O=Oeste, L=Leste)
    if (direction === 'S' || direction === 'W' || direction === 'O') {
      decimal = -decimal;
    }

    return decimal;
  };

  const convertDecimalToDMS = (decimal: number, coordinateType: 'latitude' | 'longitude'): string => {
    const isNegative = decimal < 0;
    const absDecimal = Math.abs(decimal);

    const degrees = Math.floor(absDecimal);
    const minutesFloat = (absDecimal - degrees) * 60;
    const minutes = Math.floor(minutesFloat);
    const seconds = (minutesFloat - minutes) * 60;

    // Usando O (Oeste) e L (Leste) em português
    const direction = coordinateType === 'latitude' ? (isNegative ? 'S' : 'N') : isNegative ? 'O' : 'L';

    // Formata segundos com 2 casas decimais
    const secondsStr = seconds < 10 ? `0${seconds.toFixed(2)}` : seconds.toFixed(2);

    // Para longitude, usa padding apenas se necessário (não força 3 dígitos)
    // Latitude: sempre 2 dígitos (00-90)
    // Longitude: 2 dígitos para valores < 100, 3 dígitos para valores >= 100
    let degreesStr;
    if (coordinateType === 'longitude') {
      degreesStr = degrees < 100 ? degrees.toString().padStart(2, '0') : degrees.toString();
    } else {
      degreesStr = degrees.toString().padStart(2, '0');
    }

    return `${degreesStr}°${minutes.toString().padStart(2, '0')}'${secondsStr.replace('.', ',')}" ${direction}`;
  };

  const validateRange = (inputValue: string, coordinateType: 'latitude' | 'longitude'): string => {
    if (!inputValue) return inputValue;

    // Se está no formato DMS, converte para decimal para validar
    const decimal = convertDMSToDecimal(inputValue);
    if (isNaN(decimal)) return inputValue;

    let validatedDecimal = decimal;
    if (coordinateType === 'latitude') {
      if (decimal > 90) validatedDecimal = 90;
      if (decimal < -90) validatedDecimal = -90;
    } else if (coordinateType === 'longitude') {
      if (decimal > 180) validatedDecimal = 180;
      if (decimal < -180) validatedDecimal = -180;
    }

    // Se o valor foi alterado, converte de volta para DMS
    if (validatedDecimal !== decimal) {
      return convertDecimalToDMS(validatedDecimal, coordinateType);
    }

    return inputValue;
  };

  const handleBlur = () => {
    // Valida o range apenas quando o usuário sai do campo
    const validatedValue = validateRange(value, type);
    if (validatedValue !== value && onChange) {
      onChange(validatedValue);
    }

    if (onBlur) {
      onBlur();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const char = e.key;

    // Teclas de controle que sempre devem ser permitidas (não formatam)
    const controlKeys = [
      'Backspace',
      'Delete',
      'Tab',
      'Enter',
      'Escape',
      'ArrowLeft',
      'ArrowRight',
      'ArrowUp',
      'ArrowDown',
      'Home',
      'End',
      'PageUp',
      'PageDown',
    ];

    // Permite teclas de controle sem formatação
    if (controlKeys.includes(char)) {
      return; // Permite a ação padrão (deletar, navegar, etc.)
    }

    // Permite teclas com Ctrl (Ctrl+A, Ctrl+C, Ctrl+V, etc.)
    if (e.ctrlKey || e.metaKey) {
      return;
    }

    // Permite apenas caracteres válidos para coordenadas DMS
    // Aceita direções cardeais em maiúsculo ou minúsculo (N,S,E,W,O,L,n,s,e,w,o,l)
    if (
      /[0-9]/.test(char) ||
      char === ',' ||
      char === '°' ||
      char === "'" ||
      char === '"' ||
      /[NSEWOLnsewol]/.test(char) ||
      char === ' '
    ) {
      return; // Permite o caractere
    }

    // Bloqueia qualquer outro caractere
    e.preventDefault();
  };

  const convertPastedCoordinate = (pastedText: string): string => {
    // Primeiro converte para maiúsculo para padronizar, mas aceita entrada em minúsculo
    const cleanText = pastedText.trim().toUpperCase();

    // Formato decimal com direção: "-23.550520" ou "23.550520 S" (aceita s, S, o, O, etc.)
    const decimalWithDirection = cleanText.match(/^(-?\d+\.?\d*)\s*([NSEWOL]?)$/);
    if (decimalWithDirection) {
      const decimal = parseFloat(decimalWithDirection[1]);
      let direction = decimalWithDirection[2];

      // Se não tem direção, determina baseado no tipo e sinal
      if (!direction) {
        if (type === 'latitude') {
          direction = decimal >= 0 ? 'N' : 'S';
        } else {
          direction = decimal >= 0 ? 'L' : 'O'; // Usando L (Leste) e O (Oeste)
        }
      }

      return convertDecimalToDMS(Math.abs(decimal), type).replace(/[NSEWOL]$/, direction);
    }

    // Formato DMS sem formatação: "275905S" ou "27 59 05 s" (aceita minúsculo)
    const dmsUnformatted = cleanText.match(/^(\d{1,3})\s*(\d{1,2})\s*(\d{1,2})(\s*,?\s*\d{1,2})?\s*([NSEWOL])$/);
    if (dmsUnformatted) {
      const degrees = dmsUnformatted[1];
      const minutes = dmsUnformatted[2];
      const seconds = dmsUnformatted[3];
      const decimal = dmsUnformatted[4] ? dmsUnformatted[4].replace(/[^0-9]/g, '').padEnd(2, '0') : '00';
      const direction = dmsUnformatted[5]; // Já convertido para maiúsculo

      // Aplica padding inteligente para graus
      let degreesStr;
      if (type === 'longitude') {
        const degreesNum = parseInt(degrees);
        degreesStr = degreesNum < 100 ? degrees.padStart(2, '0') : degrees;
      } else {
        degreesStr = degrees.padStart(2, '0');
      }

      return `${degreesStr}°${minutes.padStart(2, '0')}'${seconds.padStart(2, '0')},${decimal}" ${direction}`;
    }

    // Se não conseguiu converter, aplica formatação normal
    return formatCoordinate(cleanText);
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();

    const pastedText = e.clipboardData.getData('text');

    // Verifica se o texto colado já está no formato DMS correto (aceita direções em português e inglês)
    const dmsPattern = /^(\d{1,3})°(\d{1,2})'(\d{1,2})(,\d{1,2})?" ([NSEWOL])$/;
    const match = pastedText.match(dmsPattern);

    if (match) {
      // Se já está no formato correto, usa diretamente
      if (onChange) {
        onChange(pastedText);
      }
    } else {
      // Tenta converter outros formatos comuns
      const convertedValue = convertPastedCoordinate(pastedText);
      if (convertedValue && onChange) {
        onChange(convertedValue);
      }
    }
  };

  return (
    <div className="coordinate-input form-group">
      {label && (
        <Form.Label
          style={{ marginBottom: '0' }}
          className={classNames(
            (isInvalid && 'coordinate-input__label-error') || (disabled && 'coordinate-input__label-disabled')
          )}
          htmlFor={id}
        >
          {label}
        </Form.Label>
      )}
      <Form.Control
        id={id}
        value={displayValue}
        onChange={handleChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        onPaste={handlePaste}
        placeholder={placeholder}
        readOnly={readOnly}
        required={required}
        disabled={disabled}
        aria-describedby={desc}
        className={classNames(isInvalid && 'coordinate-input__error')}
        data-cy={cy}
        maxLength={maxLength}
        type="text"
        inputMode="decimal"
      />
      {isInvalid && msg && <div className="invalid-feedback d-block coordinate-input__invalid-feedback">{msg}</div>}
    </div>
  );
};

export default CoordinateInput;
