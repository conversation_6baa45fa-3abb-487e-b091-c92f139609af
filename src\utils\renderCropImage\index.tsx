import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import <PERSON>act<PERSON>rop, { Crop, PixelCrop } from 'react-image-crop';
import { toast } from 'react-toastify';
import { Container } from 'react-bootstrap';
import Loader from '../../components/Loader';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import { createCropImage } from './createCropImage';
import 'react-image-crop/dist/ReactCrop.css';
import './styles.scss';

interface ICropImageProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  onSave: (file: File) => void;
  image: any;
}

const DEFAULT_CROP_SETTINGS = {
  unit: 'px',
  width: 288,
  height: 288,
  x: 0,
  y: 0,
};
const CropImage: React.FC<ICropImageProps> = ({ setShowModal, showModal, image, onSave }) => {
  const cancel = (): void => {
    setShowModal(false);
  };
  const [cropSettings, setCropSettings] = useState<Crop>(DEFAULT_CROP_SETTINGS as Crop);
  const [completedCropSettings, setCompletedCropSettings] = useState<PixelCrop>(DEFAULT_CROP_SETTINGS as PixelCrop);
  const [isLoading, setIsLoading] = useState(false);
  const [originalImage, setOriginalImage] = useState<any>(image);
  const { t } = useTranslation();

  const newImageRef = useRef<HTMLInputElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  const submitFile = (): void => {
    setIsLoading(true);
    if (imageRef.current && completedCropSettings) {
      createCropImage(imageRef.current, completedCropSettings)
        .then((file) => {
          if (file) {
            onSave(file);
          }
          setShowModal(false);
        })
        .catch(() => {
          toast.error('Ocorreu um problema. Por favor, tente novamente.');
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  };

  return (
    <Modal
      show={showModal}
      size="lg"
      handleClose={() => cancel()}
      className="style"
      title={t('labels.labelsEditPhoto')}
    >
      <Container className="mt-4">
        {isLoading && <Loader />}
        <input
          type="file"
          accept="image/jpg, image/jpeg, image/png"
          ref={newImageRef}
          className="hidden__input"
          onChange={() => {
            if (newImageRef?.current?.files?.length) {
              const file = newImageRef.current.files[0];
              setOriginalImage({ fileUrl: URL.createObjectURL(file), fileName: file.name });
              setCropSettings(DEFAULT_CROP_SETTINGS as Crop);
            }
          }}
        />
        <div className="crop__image">
          <ReactCrop
            crop={cropSettings}
            onChange={(crop, percentageCrop) => setCropSettings(percentageCrop)}
            onComplete={(completedCrop) => setCompletedCropSettings(completedCrop)}
            aspect={1 / 1}
            minHeight={240}
            minWidth={240}
            keepSelection
          >
            <img src={originalImage.fileUrl} ref={imageRef} alt={originalImage.fileName} />
          </ReactCrop>
        </div>

        <div className="alignButton">
          <Button
            cy="test-newImage"
            type="submit"
            variant="no-color"
            onClick={() => {
              newImageRef.current?.click();
            }}
            className="without__border__button"
          >
            {t('labels.labelNewPhoto')}
          </Button>
          <Button cy="test-save" type="submit" variant="success" onClick={submitFile}>
            {t('buttons.save')}
          </Button>
        </div>
      </Container>
    </Modal>
  );
};

export default CropImage;
