import React, { useState } from 'react';
import { Col, Container, Row } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import './styles.scss';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import { Box } from '@mui/material';
import Text from '../../../components/Text';
import { IParam } from '../../../interfaces';
import { useParams } from 'react-router';
import { useAuth } from '../../../contexts/AuthContext';
import CardProductSupply from './Produto';
import SupplyChainService from '../../../services/supplyChain.service';
import CustomButton from '../../../components/Button';
import CardIdentifySupply from './Form';
import { Download } from 'lucide-react';
import toastMsg from '../../../utils/toastMsg';
import { ToastType } from '../../../utils/toastMsg copy';
import { useLoader } from '../../../contexts/LoaderContext';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const NewSupplier = (): React.ReactElement => {
  const { t, i18n } = useTranslation();
  const { id } = useParams<IParam>();
  const { user } = useAuth();
  const [supplyChain, setSupplyChain] = useState<any[]>([]);
  const [valueTab, setValueTab] = React.useState(0);
  const { setShowLoader } = useLoader();

  const [supplyChainInfo, setSupplyChainInfo] = useState({
    id: 0,
    uuid: '',
    pedido: '',
    bl: '',
    invoice: '',
    status: '',
    importador_id: '',
    client_id: user.default_company?.id,
    supply_chain_id: '',
    liberado_download: 'N',
    importador: {
      id: 0,
      nome: '',
    },
    documentos: [
      {
        id: 0,
        uuid: '',
        nome: '',
        documento_path: '',
        extensao: '',
        descricao: '',
        vencimento: '',
        status: '',
        tipo_documento_id: 0,
      },
    ],
  });

  //eslint-disable-next-line
  const [page, setPage] = React.useState(1);
  //eslint-disable-next-line
  const [limit, setLimit] = React.useState(100);
  //eslint-disable-next-line
  const [query, setQuery] = React.useState<string>('');

  const supplyChainDetail = React.useCallback(async (idSupply: string): Promise<any> => {
    try {
      const res = await SupplyChainService.getSupplyById(idSupply);

      if (res) {
        return res;
      }
      return {};
    } catch (error) {
      return {};
    }
  }, []);

  const loadCrudDetail = React.useCallback(async (): Promise<void> => {
    if (id !== undefined) {
      const res = await supplyChainDetail(id);

      if (res) {
        setSupplyChain(res.data);
      }
    }
  }, [id, supplyChainDetail]);

  React.useEffect(() => {
    let isCleaningUp = false;

    // Limpa o estado quando o ID muda
    if (id) {
      setSupplyChain([]);
      setValueTab(0); // Reset para a primeira aba
      setSupplyChainInfo({
        id: 0,
        uuid: '',
        pedido: '',
        bl: '',
        invoice: '',
        status: '',
        importador_id: '',
        client_id: user.default_company?.id,
        supply_chain_id: '',
        liberado_download: 'N',
        importador: {
          id: 0,
          nome: '',
        },
        documentos: [
          {
            id: 0,
            uuid: '',
            nome: '',
            documento_path: '',
            extensao: '',
            descricao: '',
            vencimento: '',
            status: '',
            tipo_documento_id: 0,
          },
        ],
      });
    }

    if (id && !isCleaningUp) {
      loadCrudDetail();
    }

    return () => {
      isCleaningUp = true;
    };
  }, [id, loadCrudDetail, user.default_company?.id]);

  const setValuesUpdateSupply = React.useCallback(async () => {
    if (supplyChain[0]?.supply_chain === undefined) {
      setSupplyChainInfo({
        id: supplyChain[0]?.id,
        uuid: supplyChain[0]?.uuid,
        pedido: supplyChain[0]?.pedido,
        bl: supplyChain[0]?.bl,
        invoice: supplyChain[0]?.invoice,
        status: supplyChain[0]?.status,
        importador_id: '',
        client_id: user.default_company?.id,
        supply_chain_id: supplyChain[0]?.id,
        liberado_download: 'N',
        importador: {
          id: supplyChain[0]?.importador?.id,
          nome: supplyChain[0]?.importador?.name,
        },
        documentos: [],
      });
    } else {
      setSupplyChainInfo({
        id: supplyChain[0]?.supply_chain?.id,
        uuid: supplyChain[0]?.supply_chain?.uuid,
        pedido: supplyChain[0]?.supply_chain?.pedido,
        bl: supplyChain[0]?.supply_chain?.bl,
        invoice: supplyChain[0]?.supply_chain?.invoice,
        status: supplyChain[0]?.supply_chain?.status,
        importador_id: supplyChain[0]?.supply_chain?.importador_id,
        client_id: supplyChain[0]?.supply_chain?.client_id,
        supply_chain_id: supplyChain[0]?.supply_chain_id,
        liberado_download: supplyChain[0]?.supply_chain?.liberado_download,
        importador: {
          id: supplyChain[0]?.supply_chain?.importador?.id,
          nome: supplyChain[0]?.supply_chain?.importador?.name,
        },
        documentos: supplyChain.map((doc?: any) => ({
          id: doc?.documento?.id,
          uuid: doc?.documento?.uuid,
          nome: doc?.documento?.nome,
          documento_path: doc?.documento?.documento_path,
          extensao: doc?.documento?.extensao,
          descricao: doc?.documento?.descricao,
          vencimento: doc?.documento?.vencimento,
          status: doc?.documento?.status,
          tipo_documento_id: doc?.documento?.tipo_documento_id,
          tamanho_arquivo: doc?.documento?.tamanho_arquivo,
        })),
      });
    }
  }, [setSupplyChainInfo, supplyChain]);

  const atualizarSupplyChain = React.useCallback(async () => {
    if (id !== undefined) {
      const res = await supplyChainDetail(id);

      if (res) {
        const supplyTmp = res.data;

        if (supplyTmp[0]?.supply_chain === undefined) {
          setSupplyChainInfo({
            id: supplyTmp[0]?.id,
            uuid: supplyTmp[0]?.uuid,
            pedido: supplyTmp[0]?.pedido,
            bl: supplyTmp[0]?.bl,
            invoice: supplyTmp[0]?.invoice,
            status: supplyTmp[0]?.status,
            importador_id: '',
            client_id: user.default_company?.id,
            supply_chain_id: supplyTmp[0]?.id,
            liberado_download: 'N',
            importador: {
              id: supplyTmp[0]?.importador?.id,
              nome: supplyTmp[0]?.importador?.name,
            },
            documentos: [],
          });
        } else {
          setSupplyChainInfo({
            id: supplyTmp[0]?.supply_chain?.id,
            uuid: supplyTmp[0]?.supply_chain?.uuid,
            pedido: supplyTmp[0]?.supply_chain?.pedido,
            bl: supplyTmp[0]?.supply_chain?.bl,
            invoice: supplyTmp[0]?.supply_chain?.invoice,
            status: supplyTmp[0]?.supply_chain?.status,
            importador_id: supplyTmp[0]?.supply_chain?.importador_id,
            client_id: supplyTmp[0]?.supply_chain?.client_id,
            supply_chain_id: supplyTmp[0]?.supply_chain_id,
            liberado_download: supplyTmp[0]?.supply_chain?.liberado_download,
            importador: {
              id: supplyTmp[0]?.supply_chain?.importador?.id,
              nome: supplyTmp[0]?.supply_chain?.importador?.name,
            },
            documentos: supplyTmp.map((doc?: any) => ({
              id: doc?.documento?.id,
              uuid: doc?.documento?.uuid,
              nome: doc?.documento?.nome,
              documento_path: doc?.documento?.documento_path,
              extensao: doc?.documento?.extensao,
              descricao: doc?.documento?.descricao,
              vencimento: doc?.documento?.vencimento,
              status: doc?.documento?.status,
              tipo_documento_id: doc?.documento?.tipo_documento_id,
              tamanho_arquivo: doc?.documento?.tamanho_arquivo,
            })),
          });
        }
      }
    }
  }, [id, supplyChainDetail]);

  React.useEffect(() => {
    if (id !== undefined && supplyChain !== undefined) {
      setValuesUpdateSupply();
    }
  }, [setValuesUpdateSupply, id, supplyChain]);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    localStorage.removeItem('uploadError');
    setValueTab(newValue);
  };

  const a11yProps = (index: number) => {
    return {
      id: `scrollable-auto-tab-${index}`,
      'aria-controls': `scrollable-auto-tabpanel-${index}`,
    };
  };

  function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
      >
        {value === index && <Box sx={{ pt: 3, pb: 3, pl: 0, pr: 0 }}>{children}</Box>}
      </div>
    );
  }

  const handleDownloadFile = async (): Promise<void> => {
    setShowLoader(true);
    try {
      await SupplyChainService.liberarDownloadSupplyChain('S', id as string);
      toastMsg(
        ToastType.Success,
        i18n.language === 'pt-BR' ? 'Documento EUDE liberado para download' : 'EUDE Document Available for Download'
      );
    } catch (error: unknown) {
      if (error instanceof Error) {
        toastMsg(ToastType.Error, error.message);
      } else {
        toastMsg(ToastType.Error, t('uploadFiles.downloadError'));
      }
    } finally {
      setShowLoader(false);
    }
  };

  return (
    <div className="dashboard-container">
      <div className="flex gap-2">
        <div>
          <Text className="pages-title" as="h1" size="2rem" weight={500} color="#203245">
            {t('titles.newSupplierChain')}
          </Text>
        </div>
        <div>
          <CustomButton
            cy=""
            variant="outline-secondary"
            size="sm"
            className="flex items-center gap-1"
            onClick={() => id && handleDownloadFile()}
            disabled={id === undefined || supplyChainInfo.liberado_download === 'S'}
          >
            <span>{t('buttons.toDownload')}</span>
            <Download size={16} />
          </CustomButton>
        </div>
      </div>
      <Container className="card-style-default" style={{ backgroundColor: '#ffffff', marginTop: '2rem' }}>
        <Row className="d-flex flex-row justify-content-between mt-4" style={{ paddingLeft: '1rem' }}>
          <Col>
            <Tabs value={valueTab} onChange={handleChange} variant="fullWidth" centered>
              <Tab label={t('labels.labelDataSupply')} {...a11yProps(0)} style={{ textTransform: 'none' }} />
              <Tab
                disabled={id === undefined}
                label={t('table.produtos')}
                {...a11yProps(1)}
                style={{ textTransform: 'none' }}
              />
            </Tabs>

            <CustomTabPanel value={valueTab} index={0}>
              <Col md={12} className="mt-2">
                <CardIdentifySupply loadCrudDetail={atualizarSupplyChain} supplyChain={supplyChainInfo} />
              </Col>
            </CustomTabPanel>

            <CustomTabPanel value={valueTab} index={1}>
              <Col md={12} className="mt-2">
                <label className="titleCard">{t('table.produtos')}</label>
                <label className="descricaoCard">{t('messages.productSpecies')}</label>
                {supplyChainInfo.id !== 0 && <CardProductSupply supplyChain={supplyChainInfo} />}
              </Col>
            </CustomTabPanel>
          </Col>

          <Col md={12} className="d-flex justify-content-end gap-2 mb-2 mt-4" style={{ paddingBottom: '25px' }}>
            <CustomButton
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={(event) => handleChange(event, valueTab - 1)}
              disabled={valueTab === 0}
            >
              {t('pagination.previous')}
            </CustomButton>
            <CustomButton
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={(event) => handleChange(event, valueTab + 1)}
              disabled={valueTab === 1 || id === undefined}
            >
              {t('pagination.next')}
            </CustomButton>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default NewSupplier;
