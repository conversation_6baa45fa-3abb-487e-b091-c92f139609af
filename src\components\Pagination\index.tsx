import React from 'react';
import { HiOutlineChevronLeft, HiOutlineChevronRight } from 'react-icons/hi';
import { useTranslation } from 'react-i18next';

import './styles.scss';
import { useAnalytics } from '../../contexts/AnalyticsContext';

interface IProp {
  page: number;
  setPage: (page: number) => void;
  count: number;
  local?: string;
}

const Pagination = ({ page, setPage, count, local }: IProp): React.ReactElement => {
  const { t } = useTranslation();
  const { trackEvent } = useAnalytics();

  const setPageNumber = (type: string): void => {
    if (type === 'next') {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  return (
    <div className="pagination">
      <ul className="pagination__list d-flex align-items-center">
        <li
          className={`pagination__list--arrow ${page <= 1 && 'd-none'}`}
          role="presentation"
          onClick={() => {
            setPageNumber('prev');
            trackEvent('Prev Page', {
              action: `Prev page in ${local}`,
            });
          }}
          data-cy="test-prevPage"
        >
          <HiOutlineChevronLeft size={12} /> {t('pagination.previous')}
        </li>
        <li className="pagination__list--active">{page}</li>
        <li>{t('language') === 'USA' ? 'of' : 'de'}</li>
        <li className="pagination__list">{count}</li>
        <li
          className={`${page >= count ? 'pagination__list__disabled' : 'pagination__list--arrow'}`}
          role="presentation"
          onClick={() => {
            setPageNumber('next');
            trackEvent(`Next Page in ${local}`, {
              action: `Next page`,
            });
          }}
          data-cy="test-nextPage"
        >
          {t('pagination.next')} <HiOutlineChevronRight size={12} />
        </li>
      </ul>
    </div>
  );
};

export default Pagination;
