import { convertM3ToTons, convertTonsToM3, validateQuantityConsistency } from '../woodConversion';

describe('woodConversion', () => {
  describe('convertM3ToTons', () => {
    it('should convert m³ to tons with default density', () => {
      expect(convertM3ToTons('1,000')).toBe('0,500');
      expect(convertM3ToTons('2,000')).toBe('1,000');
    });

    it('should convert m³ to tons with eucalipto density', () => {
      expect(convertM3ToTons('1,000', 'eucalipto')).toBe('0,600');
      expect(convertM3ToTons('2,500', 'eucalipto')).toBe('1,500');
    });

    it('should convert m³ to tons with pinus density', () => {
      expect(convertM3ToTons('1,000', 'pinus')).toBe('0,450');
      expect(convertM3ToTons('2,000', 'pinus')).toBe('0,900');
    });

    it('should handle empty input', () => {
      expect(convertM3ToTons('')).toBe('');
    });

    it('should handle invalid input', () => {
      expect(convertM3ToTons('invalid')).toBe('');
      expect(convertM3ToTons('abc')).toBe('');
    });
  });

  describe('convertTonsToM3', () => {
    it('should convert tons to m³ with default density', () => {
      expect(convertTonsToM3('0,500')).toBe('1,000');
      expect(convertTonsToM3('1,000')).toBe('2,000');
    });

    it('should convert tons to m³ with eucalipto density', () => {
      expect(convertTonsToM3('0,600', 'eucalipto')).toBe('1,000');
      expect(convertTonsToM3('1,500', 'eucalipto')).toBe('2,500');
    });

    it('should convert tons to m³ with pinus density', () => {
      expect(convertTonsToM3('0,450', 'pinus')).toBe('1,000');
      expect(convertTonsToM3('0,900', 'pinus')).toBe('2,000');
    });

    it('should handle empty input', () => {
      expect(convertTonsToM3('')).toBe('');
    });

    it('should handle invalid input', () => {
      expect(convertTonsToM3('invalid')).toBe('');
      expect(convertTonsToM3('xyz')).toBe('');
    });
  });

  describe('validateQuantityConsistency', () => {
    it('should validate consistent quantities with default density', () => {
      expect(validateQuantityConsistency('1,000', '0,500')).toBe(true);
      expect(validateQuantityConsistency('2,000', '1,000')).toBe(true);
    });

    it('should validate consistent quantities with eucalipto density', () => {
      expect(validateQuantityConsistency('1,000', '0,600', 'eucalipto')).toBe(true);
      expect(validateQuantityConsistency('2,500', '1,500', 'eucalipto')).toBe(true);
    });

    it('should detect inconsistent quantities', () => {
      expect(validateQuantityConsistency('1,000', '0,800')).toBe(false);
      expect(validateQuantityConsistency('1,000', '0,200')).toBe(false);
    });

    it('should return true for empty values', () => {
      expect(validateQuantityConsistency('', '0,500')).toBe(true);
      expect(validateQuantityConsistency('1,000', '')).toBe(true);
      expect(validateQuantityConsistency('', '')).toBe(true);
    });

    it('should handle tolerance parameter', () => {
      // Com tolerância de 20% (0.2), deve aceitar variação maior
      expect(validateQuantityConsistency('1,000', '0,600', undefined, 0.2)).toBe(true);
      expect(validateQuantityConsistency('1,000', '0,400', undefined, 0.2)).toBe(true);

      // Com tolerância de 2% (0.02), deve ser mais restritivo
      expect(validateQuantityConsistency('1,000', '0,520', undefined, 0.02)).toBe(false);
      expect(validateQuantityConsistency('1,000', '0,510', undefined, 0.02)).toBe(false);

      // Valores próximos ao esperado (0,500) devem passar com tolerância pequena
      expect(validateQuantityConsistency('1,000', '0,505', undefined, 0.02)).toBe(true);
      expect(validateQuantityConsistency('1,000', '0,495', undefined, 0.02)).toBe(true);
    });
  });
});
