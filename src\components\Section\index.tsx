import React from 'react';
import { Container } from 'react-bootstrap';
import classnames from 'classnames';
import Metatag from '../Metatag';
import './styles.scss';

interface IProp {
  className?: string;
  title?: string;
  description?: string;
  children?: React.ReactNode | React.ReactNode[];
}

const Section: React.FunctionComponent<IProp> = ({ children, className, title, description }): React.ReactElement => (
  <>
    <Metatag title={title} description={description} data-testid="meta-title" />
    <section data-testid="section" className={classnames('section', className)}>
      <Container>{children}</Container>
    </section>
  </>
);

Section.defaultProps = { className: '', title: '', description: '' };

export default Section;
