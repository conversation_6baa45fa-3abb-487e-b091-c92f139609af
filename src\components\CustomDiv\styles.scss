.custom-component {
  width: 100%;
  height: 86px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  @media (max-width: 768px) {
    flex-wrap: wrap;
    height: auto;
    padding-left: 1rem;
  }
}

.text {
  text-align: left;
  padding-left: 1rem;
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 27px;
  span {
    text-decoration: underline;
    color: #201e40;
    font-weight: 700;
    cursor: pointer;
  }
}

.buttonInformate {
  @media (max-width: 768px) {
    margin-top: 2rem;
    margin-left: 1rem;
    padding-bottom: 1rem;
  }
}
