import React from 'react';
import { useTranslation } from 'react-i18next';
import { Row, Col, Image } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import debounce from 'lodash.debounce';
import Text from '../../components/Text';
import Button from '../../components/Button';
import { useLoader } from '../../contexts/LoaderContext';
import { ISupplyChainServiceResponse } from '../../interfaces';
import { useAnalytics } from '../../contexts/AnalyticsContext';
import SearchIC from '../../statics/search.svg';
import './styles.scss';
import { IColumnsProps } from './TableColumnProperties';
import { TableComponent } from './TableSupplyChain';
import SupplyChainService from '../../services/supplyChain.service';
import { useAuth } from '../../contexts/AuthContext';

const Customers: React.FunctionComponent = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const history = useHistory();
  const [query, setQuery] = React.useState<string>('');
  const [isMobile, setIsMobile] = React.useState<boolean>(window.innerWidth <= 768);
  const [page, setPage] = React.useState(1);
  const { trackEvent } = useAnalytics();
  const [tableLength, setTableLength] = React.useState(0);
  const [lastPage, setLastPage] = React.useState(1);
  const [limit, setLimit] = React.useState(10);
  const [suplyChains, setSuplyChains] = React.useState<any>([]);
  const { setShowLoader, showLoader } = useLoader();

  const handleWindowSizeChange = (): void => {
    if (window.innerWidth <= 768) {
      setIsMobile(true);
    } else {
      setIsMobile(false);
    }
  };
  React.useEffect(() => {
    window.addEventListener('resize', handleWindowSizeChange);
  }, []);

  const collums: IColumnsProps[] = [
    {
      field: 'importador',
      headerName: t('table.importador'),
      color: '#201E40',
      flexGrow: 6,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'order',
      headerName: t('table.pedido'),
      color: '#201E40',
      flexGrow: 3,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'invoice',
      headerName: 'Invoice',
      color: '#201E40',
      flexGrow: 3,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'bl',
      headerName: 'BL',
      color: '#201E40',
      flexGrow: 3,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'actions',
      headerName: t('modal.modalTitleActions'),
      color: '#201E40',
      flexGrow: 2,
      fixed: false,
      resizable: !!isMobile,
    },
  ];

  const getSupplyChain = React.useCallback(async (): Promise<ISupplyChainServiceResponse> => {
    try {
      setShowLoader(true);
      const res = await SupplyChainService.getSupplyChainByIndustry(user.default_company?.id ?? 0, query, page, limit);
      if (res) {
        setShowLoader(false);
        return res;
      }
      return {} as ISupplyChainServiceResponse;
    } catch (error) {
      setShowLoader(false);
      return {} as ISupplyChainServiceResponse;
    }
  }, [limit, page, query, setShowLoader]);

  async function loadList(): Promise<void> {
    const res = await getSupplyChain();
    if (res) {
      const rows: any = res.data.map((item: any, index: number) => ({
        importador: item.importador?.name || t('labels.notInformed'),
        order: item.pedido || t('labels.notInformed'),
        invoice: item.invoice || t('labels.notInformed'),
        bl: item.bl || t('labels.notInformed'),
        className: index % 2 === 0 ? 'custom-row' : '',
        uuid: item.uuid,
        id: item.id,
      }));
      setSuplyChains(rows);
      setLastPage(res.meta.last_page);
      setTableLength(res.meta.total);
    }
  }

  React.useEffect(() => {
    let isCleaningUp = false;

    if (!isCleaningUp) {
      loadList();
    }

    return () => {
      isCleaningUp = true;
    };
  }, [getSupplyChain, t]);

  const handleChangeLimit = (dataKey: number): void => {
    setPage(1);
    setLimit(dataKey);
    trackEvent('Quotes per page', {
      action: `See ${dataKey} per page`,
      param: 'page',
    });
  };

  const handleDownPage = (): void => {
    const newPage = page - 1;
    if (newPage >= 1) {
      setPage(newPage);
    }

    trackEvent('Quotes Previus Page', {
      action: `Clicou previus page`,
      param: 'Page',
    });
  };

  const handleUpPage = (): void => {
    const newPage = page + 1;
    if (page <= tableLength / limit) {
      setPage(newPage);
    }

    trackEvent('Quotes Next Page', {
      action: `Clicou next page`,
      param: 'Page',
    });
  };

  const changeHandler = (event: any): any => {
    setQuery(event.target.value);
  };

  const debouncedChangeHandler = React.useMemo(() => debounce(changeHandler, 300), []);

  return (
    <div className="dashboard-container">
    
      <Row>
        <Col md={12} className="d-flex justify-content-between mb-4">
          <Text className="pages-title" as="b" size="2rem" weight={600} color="black">
            {t('titles.supply')}
          </Text>

          <div className="d-flex gap-4">
            <Button
              variant="primary"
              cy="test-newClient"
              onClick={() => {
                trackEvent('Client - New client', {
                  action: `Clicou para criar novo cliente`,
                });
                history.push('/new-supply');
              }}
            >
              <Col className="d-flex justify-content-between gap-2">
                <Text as="span" size="0.9rem" weight={600}>
                  + {t('buttons.newSupply')}
                </Text>
              </Col>
            </Button>
          </div>
        </Col>

        <Col className="mb-4">
          <Col className="d-flex">
            <div className="colorImage">
              <Image src={SearchIC} />
            </div>

            <input
              type="text"
              className="customInput"
              onChange={debouncedChangeHandler}
              placeholder={t('placeholders.holderFilterSupply')}
              required
              id="query"
            />
          </Col>
        </Col>

        <TableComponent
          isMobile={isMobile}
          data={suplyChains}
          tableColumns={collums}
          page={page}
          lastPage={lastPage}
          limit={limit}
          handleChangeLimit={handleChangeLimit}
          handleDownPage={handleDownPage}
          handleUpPage={handleUpPage}
          showLoader={showLoader}
          atualizarSupplyChain={loadList}
        />
      </Row>
    
    </div>

  );
};

export default Customers;
