/**
 * @description
 * Remove tudo que não for números
 * Exemplo de uso:
 * onlyNumbers(11.21);
 * @param {String | Number} input
 * @return {String} 1121
 */

const onlyNumbers = (input: string, only = false, decimal = 2): string => {
  if (!input) return '';

  if (only) {
    return String(input).replace(/[^\d]/g, '');
  }

  let newValue = input.replace(/\D/g, '');

  newValue = `${(Number(newValue) / 100).toFixed(decimal)}`;
  newValue = newValue.replace(/[^0-9.]/g, '');
  newValue = newValue.replace(/(\..?)\..*/g, '$1');
  newValue = newValue.replace(/(\d{8})./g, '$1');
  return newValue;
};

export default onlyNumbers;
