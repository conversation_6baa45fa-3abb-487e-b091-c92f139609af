import React from 'react';
import { useTranslation } from 'react-i18next';
import { Row, Col, Image } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import debounce from 'lodash.debounce';
import Text from '../../components/Text';
import Button from '../../components/Button';
import { useLoader } from '../../contexts/LoaderContext';
import { ICompanyServiceResponse } from '../../interfaces';
import { useAnalytics } from '../../contexts/AnalyticsContext';
import CompaniesService from '../../services/companies.service';
import { TableComponent } from './TableCustomers';
import SearchIC from '../../statics/search.svg';
import './styles.scss';
import { IColumnsProps } from './utils';

const Customers: React.FunctionComponent = () => {
  const { t } = useTranslation();
  const history = useHistory();
  const [query, setQuery] = React.useState<string>('');
  const [isMobile, setIsMobile] = React.useState<boolean>(window.innerWidth <= 768);
  const [page, setPage] = React.useState(1);
  const { trackEvent } = useAnalytics();
  const [tableLength, setTableLength] = React.useState(0);
  const [lastPage, setLastPage] = React.useState(1);
  const [limit, setLimit] = React.useState(10);
  const [customers, setCustomers] = React.useState<any>([]);
  const { setShowLoader, showLoader } = useLoader();

  const handleWindowSizeChange = (): void => {
    if (window.innerWidth <= 768) {
      setIsMobile(true);
    } else {
      setIsMobile(false);
    }
  };
  React.useEffect(() => {
    window.addEventListener('resize', handleWindowSizeChange);
  }, []);

  const collums: IColumnsProps[] = [
    {
      field: 'company',
      headerName: t('table.company'),
      fixed: true,
      color: '#201E40',
      flexGrow: 4,
      resizable: !!isMobile,
    },
    {
      field: 'contact',
      headerName: t('table.contact'),
      color: '#201E40',
      flexGrow: 4,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'phone',
      headerName: t('table.phone'),
      color: '#201E40',
      flexGrow: 3,
      fixed: false,
      resizable: !!isMobile,
    },

    {
      field: 'email',
      headerName: 'E-mail',
      color: '#201E40',
      flexGrow: 5,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'address',
      headerName: t('labels.labelsAdress'),
      color: '#201E40',
      flexGrow: 4,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'actions',
      headerName: t('modal.modalTitleActions'),
      color: '#201E40',
      flexGrow: 2,
      fixed: false,
      resizable: !!isMobile,
    },
  ];

  const getCustomers = React.useCallback(async (): Promise<ICompanyServiceResponse> => {
    try {
      setShowLoader(true);
      const res = await CompaniesService.getCompaniesByExporter(query, page, limit, 'customer');
      if (res) {
        setShowLoader(false);
        return res;
      }
      setShowLoader(false);
      return {} as ICompanyServiceResponse;
    } catch (error) {
      setShowLoader(false);
      return {} as ICompanyServiceResponse;
    }
  }, [limit, page, query, setShowLoader]);

  async function loadList(): Promise<void> {
    const res = await getCustomers();
    if (res) {
      const rows: any = res.data.map((item: any, index: number) => ({
        company: item.name || t('labels.notInformed'),
        contact: item.contact_fullname || t('labels.notInformed'),
        phone: item.phone || t('labels.notInformed'),
        email: item.email || t('labels.notInformed'),
        address: item.address || t('labels.notInformed'),
        className: index % 2 === 0 ? 'custom-row' : '',
        uuid: item.uuid,
        id: item.id,
      }));
      setCustomers(rows);
      setLastPage(res.meta.last_page);
      setTableLength(res.meta.total);
    }
  }

  React.useEffect(() => {
    let mounted = true;
    if (mounted) {
      loadList();
    }

    return () => {
      mounted = false;
    };
  }, [getCustomers, t]);

  const handleChangeLimit = (dataKey: number): void => {
    setPage(1);
    setLimit(dataKey);
    trackEvent('Quotes per page', {
      action: `See ${dataKey} per page`,
      param: 'page',
    });
  };

  const handleDownPage = (): void => {
    const newPage = page - 1;
    if (newPage >= 1) {
      setPage(newPage);
    }

    trackEvent('Quotes Previus Page', {
      action: `Clicou previus page`,
      param: 'Page',
    });
  };

  const handleUpPage = (): void => {
    const newPage = page + 1;
    if (page <= tableLength / limit) {
      setPage(newPage);
    }

    trackEvent('Quotes Next Page', {
      action: `Clicou next page`,
      param: 'Page',
    });
  };

  const changeHandler = (event: any): any => {
    setQuery(event.target.value);
  };

  const debouncedChangeHandler = React.useMemo(() => debounce(changeHandler, 300), []);

  return (
    <div className="dashboard-container">
      <Row>
        <Col md={12} className="d-flex justify-content-between mb-4">
          <h1 className="dashboard-title"> {t('titles.clients')}</h1>

          <div className="d-flex gap-4">
            <Button
              variant="primary"
              cy="test-newClient"
              onClick={() => {
                trackEvent('Client - New client', {
                  action: `Clicou para criar novo cliente`,
                });
                history.push('/new-client');
              }}
            >
              <Col className="d-flex justify-content-between gap-2">
                <Text as="span" size="0.9rem" weight={600}>
                  + {t('buttons.newClient')}
                </Text>
              </Col>
            </Button>
          </div>
        </Col>

        <Col className="mb-4">
          <Col className="d-flex">
            <div className="colorImage">
              <Image src={SearchIC} />
            </div>

            <input
              type="text"
              className="customInput"
              onChange={debouncedChangeHandler}
              placeholder={t('placeholders.holderFilterClients')}
              required
              id="query"
            />
          </Col>
        </Col>

        <TableComponent
          isMobile={isMobile}
          data={customers}
          tableColumns={collums}
          page={page}
          lastPage={lastPage}
          limit={limit}
          handleChangeLimit={handleChangeLimit}
          handleDownPage={handleDownPage}
          handleUpPage={handleUpPage}
          showLoader={showLoader}
          loadList={loadList}
        />
      </Row>
    </div>
  );
};

export default Customers;
