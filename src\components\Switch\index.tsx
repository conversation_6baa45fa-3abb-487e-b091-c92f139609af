import React from 'react';
import { Form } from 'react-bootstrap';
import './styles.scss';

interface IProp {
  label: string;
  name?: string;
  desc?: string;
  msg?: string;
  disabled?: boolean | false;
  checked?: boolean | false;
  inline?: boolean | false;
  isInvalid?: boolean | false;
  onChange?: React.ChangeEventHandler<HTMLInputElement> | undefined;
  readOnly?: boolean;
  cy: string;
}

const Switch = ({
  disabled,
  checked,
  label,
  desc,
  onChange,
  inline,
  name,
  isInvalid,
  msg,
  readOnly,
  cy,
}: IProp): React.ReactElement => (
  <>
    <Form.Check
      role="switch"
      type="switch"
      className="input-switch"
      disabled={disabled}
      checked={checked}
      id={`custom-switch-${label.trim()}`}
      name={name}
      label={label}
      inline={inline}
      aria-checked={checked}
      aria-labelledby={desc}
      onChange={onChange}
      isInvalid={isInvalid}
      readOnly={readOnly}
      data-cy={cy}
    />
    {isInvalid && <span className="switch-error">{msg}</span>}
  </>
);

export default Switch;
