import React from 'react';
import { Row, Col } from 'react-bootstrap';
import './styles.scss';
import { useAuth } from '../../contexts/AuthContext';
import { useHistory } from 'react-router';

import { FaUserAlt, FaTruck, FaUserTie, FaLink, FaFileAlt } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const { user, getImporter } = useAuth();
  const history = useHistory();

  React.useEffect(() => {
    if (getImporter().includes(user?.profileId as number)) {
      history.push('/dashboard-importer', { reload: true });
    }
  }, [user?.profileId]);

  return (
    <div className="dashboard-container">
      <h1 className="dashboard-title">Dashboard</h1>

      <div className="supply-chain-steps">
        <h2 className="steps-title">{t('titles.titleDashboard')}</h2>
        <hr className="divider" />

        <Row className="steps-row">
          <Col className="step-col cursor-pointer" onClick={() => history.push('/industry')}>
            <div className="step-icon">
              <FaUserAlt />
            </div>
            <div className="step-arrow">→</div>
            <div className="step-number">1. {t('labels.labelPerfil').toUpperCase()}</div>
            <p className="step-description">{t('headers.headerProfile')}</p>
            <p className="step-note">{t('headers.headerProfile2')}</p>
          </Col>

          <Col className="step-col cursor-pointer" onClick={() => history.push('/new-fornecedor')}>
            <div className="step-icon">
              <FaTruck />
            </div>
            <div className="step-arrow">→</div>
            <div className="step-number">2. {t('labels.exporterName').toUpperCase()}</div>
            <p className="step-description">{t('headers.headerSupplier')}</p>
          </Col>

          <Col className="step-col cursor-pointer" onClick={() => history.push('/new-client')}>
            <div className="step-icon">
              <FaUserTie />
            </div>
            <div className="step-arrow">→</div>
            <div className="step-number">3. {t('titles.clients').toUpperCase()}</div>
            <p className="step-description">{t('headers.headerImporter')}</p>
          </Col>

          <Col className="step-col cursor-pointer" onClick={() => history.push('/new-supply')}>
            <div className="step-icon">
              <FaLink />
            </div>
            <div className="step-arrow">→</div>
            <div className="step-number">4. {t('titles.eudr').toUpperCase()}</div>
            <p className="step-description">{t('headers.headerSupplyChain')}</p>
          </Col>

          <Col className="step-col cursor-pointer" onClick={() => history.push('/supplychain')}>
            <div className="step-icon">
              <FaFileAlt />
            </div>
            <div className="step-number">5. {t('titles.supply').toUpperCase()}</div>
            <p className="step-description">{t('headers.headerReports')}</p>
          </Col>
        </Row>
      </div>

      <div className="simple-process">
        <h2 className="simple-title">{t('headers.headerDashboard')}</h2>
        <hr className="divider" />
        <p className="simple-description">{t('messages.responsabilidade')}</p>
      </div>
    </div>
  );
};

export default Dashboard;
