// Teste para debug do problema de API do produto

describe('Product API Debug', () => {
  describe('Data preparation for API calls', () => {
    // Simular exatamente a lógica usada no componente
    const prepareUpdateData = (quantity: string, quantityTons: string, description: string, productSave: any) => {
      return {
        id: productSave?.id,
        product_description: description,
        product_quantity: quantity?.trim() ? quantity.replace(',', '.') : null,
        product_quantity_tons: quantityTons?.trim() ? quantityTons.replace(',', '.') : null,
      };
    };

    const prepareCreateData = (quantity: string, quantityTons: string, description: string, supplyChainId: string) => {
      return {
        product_description: description,
        product_quantity: quantity?.trim() ? quantity.replace(',', '.') : null,
        product_quantity_tons: quantityTons?.trim() ? quantityTons.replace(',', '.') : null,
        status: 'A',
        supply_chain_id: supplyChainId,
      };
    };

    // Simular a lógica do serviço
    const processServiceData = (productData: any) => {
      return {
        ...productData,
        product_quantity: productData.product_quantity === null ? null : productData.product_quantity,
        product_quantity_tons: productData.product_quantity_tons === null ? null : productData.product_quantity_tons
      };
    };

    it('should prepare update data correctly with both quantities', () => {
      const mockProductSave = { id: 'test-id', uuid: 'test-uuid' };
      const updateData = prepareUpdateData('252,252', '150,500', 'Teste produto', mockProductSave);
      const processedData = processServiceData(updateData);

      expect(processedData).toEqual({
        id: 'test-id',
        product_description: 'Teste produto',
        product_quantity: '252.252',
        product_quantity_tons: '150.500'
      });
    });

    it('should prepare update data correctly with only m³', () => {
      const mockProductSave = { id: 'test-id', uuid: 'test-uuid' };
      const updateData = prepareUpdateData('252,252', '', 'Teste produto', mockProductSave);
      const processedData = processServiceData(updateData);

      expect(processedData).toEqual({
        id: 'test-id',
        product_description: 'Teste produto',
        product_quantity: '252.252',
        product_quantity_tons: null
      });
    });

    it('should prepare update data correctly with only tons', () => {
      const mockProductSave = { id: 'test-id', uuid: 'test-uuid' };
      const updateData = prepareUpdateData('', '150,500', 'Teste produto', mockProductSave);
      const processedData = processServiceData(updateData);

      expect(processedData).toEqual({
        id: 'test-id',
        product_description: 'Teste produto',
        product_quantity: null,
        product_quantity_tons: '150.500'
      });
    });

    it('should prepare create data correctly with both quantities', () => {
      const createData = prepareCreateData('252,252', '150,500', 'Teste produto', 'supply-chain-id');
      const processedData = processServiceData(createData);

      expect(processedData).toEqual({
        product_description: 'Teste produto',
        product_quantity: '252.252',
        product_quantity_tons: '150.500',
        status: 'A',
        supply_chain_id: 'supply-chain-id'
      });
    });

    it('should prepare create data correctly with only m³', () => {
      const createData = prepareCreateData('252,252', '', 'Teste produto', 'supply-chain-id');
      const processedData = processServiceData(createData);

      expect(processedData).toEqual({
        product_description: 'Teste produto',
        product_quantity: '252.252',
        product_quantity_tons: null,
        status: 'A',
        supply_chain_id: 'supply-chain-id'
      });
    });

    it('should prepare create data correctly with only tons', () => {
      const createData = prepareCreateData('', '150,500', 'Teste produto', 'supply-chain-id');
      const processedData = processServiceData(createData);

      expect(processedData).toEqual({
        product_description: 'Teste produto',
        product_quantity: null,
        product_quantity_tons: '150.500',
        status: 'A',
        supply_chain_id: 'supply-chain-id'
      });
    });

    it('should handle validation correctly', () => {
      // Simular a validação atualizada (apenas para novos produtos)
      const validateQuantities = (quantity: string, quantityTons: string, isUpdate: boolean = false) => {
        // Para atualizações, permitir ambos os campos vazios
        if (isUpdate) {
          return true;
        }
        // Para novos produtos, pelo menos um campo deve estar preenchido
        return !(!quantity?.trim() && !quantityTons?.trim());
      };

      // Testes para novos produtos (validação ativa)
      expect(validateQuantities('252,252', '', false)).toBe(true);
      expect(validateQuantities('', '150,500', false)).toBe(true);
      expect(validateQuantities('252,252', '150,500', false)).toBe(true);
      expect(validateQuantities('', '', false)).toBe(false);
      expect(validateQuantities('   ', '   ', false)).toBe(false);

      // Testes para atualizações (validação desabilitada)
      expect(validateQuantities('252,252', '', true)).toBe(true);
      expect(validateQuantities('', '150,500', true)).toBe(true);
      expect(validateQuantities('252,252', '150,500', true)).toBe(true);
      expect(validateQuantities('', '', true)).toBe(true);
      expect(validateQuantities('   ', '   ', true)).toBe(true);
    });
  });
});
