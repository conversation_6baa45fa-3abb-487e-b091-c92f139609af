import React, { useState } from "react";
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";
import { Captions, Download, Fullscreen, Share, Thumbnails, Zoom } from "yet-another-react-lightbox/plugins";
import "yet-another-react-lightbox/plugins/captions.css";
import "yet-another-react-lightbox/plugins/thumbnails.css";
import Images from "./images";


interface ListaImagens {
  data: Slide[],
}

interface Slide {
  src: string;
  title?: string;
}


const LightboxImgInspection = ({data}: ListaImagens): JSX.Element => {
  const [index, setIndex] = useState<number>(-1)
  return (
    <>
      <Images data={data} onClick={(currentIndex) => setIndex(currentIndex)} />

      <Lightbox
        plugins={[Captions, Download, Fullscreen, Zoom, Thumbnails, Share]}
        captions={{showToggle: true, descriptionTextAlign: 'end'}}
        index={index}
        open={index >= 0}
        close={() => setIndex(-1)}
        slides={data}
        styles={{ container: { backgroundColor: "rgba(0, 0, 0, .9)" } }}
      />
    </>
  )
}

export default LightboxImgInspection;
