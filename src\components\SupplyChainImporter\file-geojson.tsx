import { Download, Eye } from 'lucide-react';
import Button from '../Button';
import { useLoader } from '../../contexts/LoaderContext';
import SupplyChainService from '../../services/supplyChain.service';
import toastMsg, { ToastType } from '../../utils/toastMsg';
import { useTranslation } from 'react-i18next';
import React from 'react';
import FlorestaService from '../../services/floresta.service';

interface FileAttachmentProps {
  filename: string;
  fileSize: string;
  filepath: string;
  documentId: string;
}

export default function FileGeoJson({ filename, fileSize, filepath, documentId }: FileAttachmentProps) {
  const { setShowLoader } = useLoader();
  const { t } = useTranslation();
  const [geoJsonData, setGeoJsonData] = React.useState<any>([]);

  const getGeoJsonByDocumentoFloresta = React.useCallback(async (): Promise<any> => {
    try {
      //setShowLoader(true);
      const res = await FlorestaService.geoJsonByDocumentoFloresta(String(documentId));
      setGeoJsonData(res);
    } catch (error) {
      toastMsg(ToastType.Error, t('exceptions.errorLoadingGeoJson'));
      return {};
    } finally {
      //setShowLoader(false);
    }
  }, [documentId, t, setGeoJsonData]);

  React.useEffect(() => {
    let controle = true;

    if (controle) {
      getGeoJsonByDocumentoFloresta();
    }

    return () => {
      controle = false;
    };
  }, [getGeoJsonByDocumentoFloresta]);

  const handleDownloadGeoJson = async (id: string, tipo: string, name: string): Promise<void> => {
    try {
      setShowLoader(true);
      await SupplyChainService.downloadGeoJson(id, tipo, name);
      toastMsg(ToastType.Success, t('uploadFiles.downloadSuccess'));
    } catch (error: unknown) {
      if (error instanceof Error) {
        toastMsg(ToastType.Error, error.message);
      }
    } finally {
      setShowLoader(false);
    }
  };

  const alterName = (name: string) => {
    if (name === 'Cobertura_do_Solo') {
      return 'Ground Cover';
    } else if (name === 'Area_de_Uso_Restrito') {
      return 'Restricted Use Area';
    } else if (name === 'Reserva_Legal') {
      return 'Legal Reserve';
    } else if (name === 'Area_de_Preservacao_Permanente') {
      return 'Permanent Preservation Area';
    } else if (name === 'Area_do_Imovel') {
      return 'AProperty Area';
    } else {
      return name;
    }
  };

  const downloadDoc = (url: string) => {
    window.open(url, '_blank');
  };
  return (
    <>
      <div className="flex items-center justify-between p-2 border rounded-md">
        <div className="flex items-center gap-2">
          <div className="bg-gray-100 p-2 rounded">
            <svg width="16" height="20" viewBox="0 0 16 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 0H2C0.9 0 0 0.9 0 2V18C0 19.1 0.9 20 2 20H14C15.1 20 16 19.1 16 18V6L10 0Z" fill="#333333" />
              <path d="M10 0V6H16L10 0Z" fill="#666666" />
            </svg>
          </div>
          <div>
            <p className="text-sm font-medium">{filename}</p>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500">• Preview</span>
              <span className="text-xs text-gray-500">{fileSize}</span>
            </div>
          </div>
        </div>
        <div className="flex gap-1">
          <Button cy="test-newClient" variant="gost" className="h-8 w-8" onClick={() => downloadDoc(filepath)}>
            <Eye size={16} />
          </Button>
        </div>
      </div>

      {geoJsonData.map((data: any, index: number) => (
        <div className="flex items-center justify-between p-2 border rounded-md" key={index}>
          <div className="flex items-center gap-2">
            <div className="bg-gray-100 p-2 rounded">
              <svg width="16" height="20" viewBox="0 0 16 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M10 0H2C0.9 0 0 0.9 0 2V18C0 19.1 0.9 20 2 20H14C15.1 20 16 19.1 16 18V6L10 0Z"
                  fill="#333333"
                />
                <path d="M10 0V6H16L10 0Z" fill="#666666" />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium">{alterName(data.descricao)}</p>
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-500">• Preview</span>
                <span className="text-xs text-gray-500"></span>
              </div>
            </div>
          </div>
          <div className="flex gap-1" key={index}>
            <Button
              cy="test-newClient"
              variant="gost"
              className="h-8 w-8"
              onClick={() => handleDownloadGeoJson(data.id, 'area', data.descricao)}
            >
              <Eye size={16} />
            </Button>
            <Button
              cy=""
              variant="gost"
              size="sm"
              className="flex items-center gap-1"
              onClick={() => handleDownloadGeoJson(data.id, 'geojson', data.descricao)}
            >
              <span>GeoJson</span>
              <Download size={16} />
            </Button>
          </div>
        </div>
      ))}
    </>
  );
}
