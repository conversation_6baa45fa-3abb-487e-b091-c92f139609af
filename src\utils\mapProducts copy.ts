import { IBusiness } from '../interfaces';
import { convertNumber, convertNumberForBusiness } from './convertNumber';
import { renderProductName } from './renderNameProduct';

function productConvert(data: any, t: string, hasRenderProductName: boolean): any {
  return {
    produto_id: data.produto_id,
    qtd_palete: data.qtd_palete || '',
    cod_unid_medida_preco: data.cod_unid_medida_preco || '',
    val_preco_unit: data.val_preco_unit || '',
    val_comprimento_mm: data.val_comprimento_mm || '',
    val_espessura_mm: data.val_espessura_mm || '',
    val_largura_mm: data.val_largura_mm || '',
    val_preco_prod: data.val_preco_prod || '',
    val_volume_m3_prod: data.val_volume_m3_prod || '',
    val_volume_m3_prod_palete: data.val_volume_m3_prod_palete || '',
    des_negocio_prod: data.des_negocio_prod || '',
    productName: hasRenderProductName
      ? t === 'pt-BR'
        ? data.produto.name_ptbr
        : data.produto.name
      : renderProductName(data, t),
    negocio_produto_id: data.negocio_produto_id || '',
    negocio_id: data?.negocio_id || '',
    qtd_peca: data?.qtd_peca || '',
    qtd_peca_palete: data?.qtd_peca_palete || '',
    certificates: data?.certificates,
    qualities: data?.qualities,
    finishes: data?.finishes,
    specie: data?.specie,
    specie_id: data?.specie_id,
    glue_id: data?.glue_id,
    cola: data?.glue,
    layers: data?.layers,
    thickness: data?.thickness,
    length: data?.length,
    width: data?.width,
    produto: data?.produto || '',
  };
}

export function mapProducts(res: IBusiness, t: string, hasRenderProductName = false): any {
  return res.data
    ? res.data.negocio_prod?.map((fil: any) => productConvert(fil, t, hasRenderProductName))
    : res.negocio_prod?.map((fil: any) => productConvert(fil, t, hasRenderProductName));
}

export function mapSelectProducts(data: any, t: string): any {
  return data?.map((product: any) => {
    const returnMap = {
      label: t === 'USA' ? `${product?.type?.description}` : `${product?.type?.description_ptbr}`,
      productLabel: t === 'USA' ? `${product?.type?.description}` : `${product?.type?.description_ptbr}`,
      value: product?.id,
      certificates: product?.certificates,
      specie: product?.specie,
      quality: product?.quality,
      glue: product?.glue,
      width: product?.width,
      length: product?.length,
      layers: product?.layers,
      oldLayers: product?.layers,
      thickness: product?.thickness,
      finishes: product?.finishes,
    };

    return returnMap;
  });
}

export function formatProductToBusiness(products: any): any {
  return products.map((prods: any) => {
    const returnQualities = prods?.qualities?.map((valueId: any) =>
      valueId?.value ? Number(valueId?.value) : Number(valueId?.id)
    );
    const returnCertificates = prods?.certificates?.map((valueId: any) =>
      valueId?.value ? Number(valueId?.value) : Number(valueId?.id)
    );
    const returnFinishes = prods?.finishes?.map((valueId: any) =>
      valueId?.value ? Number(valueId?.value) : Number(valueId?.id)
    );

    return {
      cod_unid_medida_preco: prods.cod_unid_medida_preco,
      qtd_peca: prods.qtd_peca ? convertNumberForBusiness(prods.qtd_peca) : null,
      qtd_peca_palete: prods.qtd_peca_palete ? convertNumberForBusiness(prods.qtd_peca_palete) : null,
      produto_id: prods.produto_id,
      qtd_palete: prods.qtd_palete ? convertNumberForBusiness(prods.qtd_palete) : null,
      val_comprimento_mm: prods.val_comprimento_mm,
      val_espessura_mm: prods.val_espessura_mm,
      val_largura_mm: prods.val_largura_mm,
      val_preco_unit: convertNumberForBusiness(prods.val_preco_unit),
      val_preco_prod: convertNumberForBusiness(prods.val_preco_prod),
      val_volume_m3_prod: convertNumberForBusiness(prods.val_volume_m3_prod),
      val_volume_m3_prod_palete: convertNumberForBusiness(prods.val_volume_m3_prod_palete),
      des_negocio_prod: prods.des_negocio_prod,
      negocio_produto_id: prods?.negocio_produto_id || null,
      thickness: Array.isArray(prods.thickness)
        ? ''
        : typeof prods.thickness === 'number'
        ? prods.thickness
        : convertNumber(prods.thickness),
      length: Array.isArray(prods.length)
        ? ''
        : typeof prods.length === 'number'
        ? prods.length
        : convertNumber(prods.length),
      width: Array.isArray(prods.width)
        ? ''
        : typeof prods.width === 'number'
        ? prods.width
        : convertNumber(prods.width),
      certificates: returnCertificates,
      qualities: returnQualities,
      finishes: returnFinishes,
      layers: Array.isArray(prods?.layers) || prods?.layers === undefined ? null : prods?.layers,
      glue_id: prods.glue_id,
      specie_id: prods.specie_id,
    };
  });
}
