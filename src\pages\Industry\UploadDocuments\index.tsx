import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import Card from '../../../components/Card';
import './styles.scss';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import { Box } from '@mui/material';
import CompaniesService from '../../../services/companies.service';
import CardInfoCompany from './DadosCadastrais';
import Button from '../../../components/Button';
import { IColumnsProps } from './TableColumnProperties';
import { useLoader } from '../../../contexts/LoaderContext';
import DocumentosService from '../../../services/documentos.service';
import { useAuth } from '../../../contexts/AuthContext';
import { UploadArquivo } from './UploadArquivo';
import { format } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const CardUploaDocuments = (): React.ReactElement => {
  const { user } = useAuth();
  const { t, i18n } = useTranslation();
  const [isMobile] = React.useState<boolean>(window.innerWidth <= 768);
  const [valueTab, setValueTab] = React.useState(0);
  const [grupos, setGrupos] = React.useState({} as any);

  const [tipo1, setTipo1] = React.useState({} as any);
  const [tipo2, setTipo2] = React.useState({} as any);
  const [tipo3, setTipo3] = React.useState({} as any);
  const [tipo4, setTipo4] = React.useState({} as any);
  const { setShowLoader, showLoader } = useLoader();
  const [company, setCompany] = useState({} as any);
  const [companyInfo, setCompanyInfo] = React.useState({
    uuid: '',
    razao: '',
    fantasia: '',
    email: '',
    cnpj: '',
    address: '',
    numero: '',
    state: '',
    city: '',
    cep: '',
    telefone: '',
    inscricaoEstadual: '',
  });

  const collums: IColumnsProps[] = [
    {
      field: 'recebido',
      headerName: t('table.recebido'),
      fixed: true,
      color: '#201E40',
      flexGrow: 2,
      resizable: !!isMobile,
    },
    {
      field: 'documento',
      headerName: t('table.documento'),
      color: '#201E40',
      flexGrow: 6,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'ondeEmitir',
      headerName: t('table.ondeEmitir'),
      color: '#201E40',
      flexGrow: 2,
      fixed: false,
      resizable: !!isMobile,
    },

    {
      field: 'vencimento',
      headerName: t('table.vencimento'),
      color: '#201E40',
      flexGrow: 2,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'enviarDocumento',
      headerName: t('table.enviarDocumento'),
      color: '#201E40',
      flexGrow: 6,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'actions',
      headerName: t('modal.modalTitleActions'),
      color: '#201E40',
      flexGrow: 2,
      fixed: false,
      resizable: !!isMobile,
    },
  ];

  const getDocumentos = React.useCallback(async (idExportador: number): Promise<any> => {
    try {
      const res: any = await DocumentosService.getDocumentoExportador(idExportador);

      if (res) {
        return res;
      }
    } catch (error) {
      //eslint-disable-next-line
      console.log(error);
    }
  }, []);

  const getGruposDocumento = React.useCallback(async (): Promise<any> => {
    try {
      const res: any = await DocumentosService.getGruposDocumento();

      if (res) {
        return res;
      }
    } catch (error) {
      //eslint-disable-next-line
      console.log(error);
    }
  }, []);

  const getCompanieById = React.useCallback(async (uuid: string): Promise<any> => {
    try {
      const res = await CompaniesService.getCompanieByUiid(uuid);

      if (res) {
        return res;
      }
    } catch (error) {
      //eslint-disable-next-line
      console.log(error);
    }
  }, []);

  // Função local para processar documentos sem depender do estado
  const addDocumentoTipoLocal = (tipos: any, documentosData: any): any => {
    tipos.forEach((tp: any) => {
      if (documentosData) {
        documentosData.forEach((doc: any) => {
          if (doc.documento.tipo_documento_id === tp.idTipo) {
            tp.recebido = 'true';
            tp.enviarDocumento = 'false';
            tp.vencimento = format(utcToZonedTime(doc.documento.vencimento, 'America/Sao_Paulo'), t('format.date'));
            tp.idDocumento = doc.id;
            tp.tamanho_arquivo = doc.documento.tamanho_arquivo;
          }
        });
      }
    });
    return tipos;
  };

  async function loadAllData(): Promise<void> {
    try {
      setShowLoader(true);

      // Carregar grupos e documentos primeiro
      const resGrupos = await getGruposDocumento();
      const gruposData = resGrupos.data;
      setGrupos(gruposData);

      let documentosData: any[] = [];
      if (user?.default_company?.id !== undefined) {
        const resDocumentos = await getDocumentos(user.default_company.id);
        documentosData = resDocumentos.data;
      }

      // Carregar dados da empresa
      if (user?.default_company?.uuid) {
        const importador = await getCompanieById(user?.default_company.uuid);
        setCompany(importador.data);
      }

      // Processar os tipos de documentos
      if (gruposData) {
        gruposData.forEach((grupo: any) => {
          const rows: any = grupo.tipos_grupo.map((item: any, index: number) => ({
            recebido: 'false',
            enviarDocumento: 'true',
            idTipo: item.id,
            documento: item.titulo,
            descricao: item.descricao,
            site: item.site,
            orgao: item.orgao,
            local: item.local,
            ondeEmitir: t('table.ondeEmitir'),
            vencimento: '',
            idDocumento: '',
            idExportador: user?.default_company?.id ?? '',
            className: index % 2 === 0 ? 'custom-row' : '',
          }));

          // Aplicar documentos existentes
          const processedRows = addDocumentoTipoLocal(rows, documentosData);

          if (grupo.id === 1) {
            setTipo1(processedRows);
          } else if (grupo.id === 2) {
            setTipo2(processedRows);
          } else if (grupo.id === 3) {
            setTipo3(processedRows);
          } else if (grupo.id === 4) {
            setTipo4(processedRows);
          }
        });
      }

      setShowLoader(false);
    } catch (error) {
      setShowLoader(false);
    }
  }

  // UseEffect único para carregar todos os dados iniciais
  React.useEffect(() => {
    if (user?.default_company?.id) {
      loadAllData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.default_company?.id]);

  const setValuesUpdate = React.useCallback(async () => {
    setCompanyInfo({
      uuid: company.uuid,
      razao: company.name,
      fantasia: company.name_sender,
      email: company.email,
      cnpj: company.tax_identifier_number,
      address: company.street,
      numero: company.address_number,
      state: company.state,
      city: company.city,
      cep: company.zip_code,
      telefone: company.phone,
      inscricaoEstadual: company.address_complement,
    });
  }, [setCompanyInfo, company]);

  React.useEffect(() => {
    if (company !== undefined) {
      setValuesUpdate();
    }
  }, [setValuesUpdate, company]);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    localStorage.removeItem('uploadError');
    setValueTab(newValue);
  };

  const a11yProps = (index: number) => {
    return {
      id: `scrollable-auto-tab-${index}`,
      'aria-controls': `scrollable-auto-tabpanel-${index}`,
    };
  };

  function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
      >
        {value === index && <Box sx={{ pt: 3, pb: 3, pl: 0, pr: 0 }}>{children}</Box>}
      </div>
    );
  }

  return (
    <Card
      cy="card-test-product"
      className="style card-style-default"
      renderBody={
        <Row className="d-flex flex-row justify-content-between">
          <Row>
            <Col>
              <Tabs value={valueTab} onChange={handleChange} variant="fullWidth" centered>
                <Tab label={t('titles.dadosCadastrais')} {...a11yProps(0)} style={{ textTransform: 'none' }} />
                <Tab label={t('titles.gestaoNegocio')} {...a11yProps(1)} style={{ textTransform: 'none' }} />
                <Tab label={t('titles.meioAmbiente')} {...a11yProps(2)} style={{ textTransform: 'none' }} />
                <Tab label={t('titles.gestaoPessoas')} {...a11yProps(3)} style={{ textTransform: 'none' }} />
                <Tab label={t('titles.clima')} {...a11yProps(4)} style={{ textTransform: 'none' }} />
              </Tabs>

              <CustomTabPanel value={valueTab} index={0}>
                <Col md={12} className="mt-2">
                  <label className="titleCard">
                    {i18n.language === 'pt-BR' ? 'Dados Cadastrais' : 'Company Information'}
                  </label>
                  <label className="descricaoCard">
                    {i18n.language === 'pt-BR'
                      ? 'Os dados cadastrais são informações essenciais para a identificação e registro de uma pessoa ou empresa. Manter essas informações atualizadas é fundamental para garantir uma comunicação eficiente e evitar problemas administrativos.'
                      : 'Registration data is essential information for identifying and registering a person or company. Keeping this information up to date is essential to ensure efficient communication and avoid administrative problems.'}
                  </label>
                  <CardInfoCompany company={companyInfo} loadList={loadAllData} setValuesUpdate={setValuesUpdate} />
                </Col>
              </CustomTabPanel>

              <CustomTabPanel value={valueTab} index={1}>
                <Col md={12} className="mt-2">
                  <label className="titleCard">
                    {i18n.language === 'pt-BR' ? grupos[0]?.titulo : grupos[0]?.titulo_en}
                  </label>
                  <label className="descricaoCard">
                    {i18n.language === 'pt-BR' ? grupos[0]?.descricao : grupos[0]?.descricao_en}
                  </label>
                  <UploadArquivo
                    isMobile={isMobile}
                    data={tipo1}
                    tableColumns={collums}
                    showLoader={showLoader}
                    loadList={loadAllData}
                  />
                </Col>
              </CustomTabPanel>
              <CustomTabPanel value={valueTab} index={2}>
                <Col md={12} className="mt-2">
                  <label className="titleCard">
                    {i18n.language === 'pt-BR' ? grupos[1]?.titulo : grupos[1]?.titulo_en}
                  </label>
                  <label className="descricaoCard">
                    {i18n.language === 'pt-BR' ? grupos[1]?.descricao : grupos[1]?.descricao_en}
                  </label>
                  <UploadArquivo
                    isMobile={isMobile}
                    data={tipo2}
                    tableColumns={collums}
                    showLoader={showLoader}
                    loadList={loadAllData}
                  />
                </Col>
              </CustomTabPanel>
              <CustomTabPanel value={valueTab} index={3}>
                <Col md={12} className="mt-2">
                  <label className="titleCard">
                    {i18n.language === 'pt-BR' ? grupos[2]?.titulo : grupos[2]?.titulo_en}
                  </label>
                  <label className="descricaoCard">
                    {i18n.language === 'pt-BR' ? grupos[2]?.descricao : grupos[2]?.descricao_en}
                  </label>
                  <UploadArquivo
                    isMobile={isMobile}
                    data={tipo3}
                    tableColumns={collums}
                    showLoader={showLoader}
                    loadList={loadAllData}
                  />
                </Col>
              </CustomTabPanel>
              <CustomTabPanel value={valueTab} index={4}>
                <Col md={12} className="mt-2">
                  <label className="titleCard">
                    {i18n.language === 'pt-BR' ? grupos[3]?.titulo : grupos[3]?.titulo_en}
                  </label>
                  <label className="descricaoCard">
                    {i18n.language === 'pt-BR' ? grupos[3]?.descricao : grupos[3]?.descricao_en}
                  </label>
                  <UploadArquivo
                    isMobile={isMobile}
                    data={tipo4}
                    tableColumns={collums}
                    showLoader={showLoader}
                    loadList={loadAllData}
                  />
                </Col>
              </CustomTabPanel>
            </Col>
          </Row>
          <Row>
            <Col md={12} className="d-flex justify-content-end gap-2 mb-2 mt-4">
              <Button
                cy="btn-cancel"
                type="button"
                variant="outline-green"
                onClick={(event) => handleChange(event, valueTab - 1)}
                disabled={valueTab === 0}
              >
                {t('pagination.previous')}
              </Button>
              <Button
                cy="btn-cancel"
                type="button"
                variant="outline-green"
                onClick={(event) => handleChange(event, valueTab + 1)}
                disabled={valueTab === 4}
              >
                {t('pagination.next')}
              </Button>
            </Col>
          </Row>
        </Row>
      }
    />
  );
};

export default CardUploaDocuments;
