import React, { useState } from 'react';
import { Table } from 'rsuite';
import { Col, Container, Dropdown, Image, Row } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, ArrowRight } from '@mui/icons-material';
import EmptyState from '../../../components/EmptyState';
import EmptyStateImage from '../../../statics/emptyState.png';
import { FiEdit2 } from 'react-icons/fi';
import InactiveUser from '../../../statics/BsXCircle.svg';
import PdfPNG from '../../../statics/pdf.png';
import Modal from '../../../components/Modal';
import Button from '../../../components/Button';
import '../styles.scss';
import { useLoader } from '../../../contexts/LoaderContext';
import toastMsg, { ToastType } from '../../../utils/toastMsg';
import SupplyChainService from '../../../services/supplyChain.service';

export const TableComponent = ({
  sortColumn,
  sortType,
  handleSortColumn,
  isMobile,
  data,
  tableColumns,
  page,
  lastPage,
  limit,
  handleChangeLimit,
  handleDownPage,
  handleUpPage,
  showLoader,
  atualizarSupplyChain,
}: any): React.ReactElement => {
  const { Column, HeaderCell, Cell } = Table;
  const { t } = useTranslation();
  const history = useHistory();
  const [show, setShow] = useState<any>({ show: false, client: null });
  const { setShowLoader } = useLoader();

  const handleDeleteProduct = async (client: any): Promise<void> => {
    if (!client?.id) {
      return;
    }

    try {
      setShowLoader(true);

      await SupplyChainService.delete(client?.id);
      toastMsg(ToastType.Success, t('response.deleteSuccess'));
      setShowLoader(false);
      setShow(false);

      atualizarSupplyChain();
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  };

  const handleDownloadFile = async (id: string): Promise<void> => {
    /*try {
      setShowLoader(true);
      await SupplyChainService.downloadDocumento(id);
      toastMsg(ToastType.Success, t('uploadFiles.downloadSuccess'));
    } catch (error: unknown) {
      if (error instanceof Error) {
        toastMsg(ToastType.Error, error.message);
      }
    } finally {
      setShowLoader(false);
    }*/

    setShowLoader(true);
    try {
      await SupplyChainService.downloadDocumento(id);
      toastMsg(ToastType.Success, t('uploadFiles.downloadSuccess'));
    } catch (error: any) {
      //toastMsg(ToastType.Warning, 'Documento com erro!');
    } finally {
      setShowLoader(false);
    }
  };

  return (
    <Container className="containerBackgroundCustomers">
      <Modal
        show={show.show}
        handleClose={() => setShow({ ...show, show: false })}
        title={t('modal.removeClientTitle')}
        size="lg"
        className="styleModalConfirm"
        colorIcon
      >
        <Container>
          <Row className="mt-4 p-2">
            <Col className="d-flex align-items-center ">
              <h6>
                {t('modal.removeSupplyChain')}
                <br />
                {t('modal.removeProductDescription')}
              </h6>
            </Col>
          </Row>
        </Container>

        <Row className="mt-4">
          <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
            <Button
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={() => {
                setShow(false);
              }}
            >
              {t('buttons.cancel')}
            </Button>
            <Button cy="btn-save" type="button" variant="danger" onClick={() => handleDeleteProduct(show.client)}>
              {t('buttons.delete')}
            </Button>
          </Col>
        </Row>
      </Modal>

      {data.length > 0 ? (
        <>
          <Table
            bordered
            cellBordered
            sortColumn={sortColumn}
            sortType={sortType}
            onSortColumn={handleSortColumn}
            autoHeight={isMobile}
            height={500}
            affixHorizontalScrollbar={!isMobile}
            data={data}
            className="table"
            rowClassName={(rowData: any) => rowData?.className || ''}
          >
            {tableColumns.map((column: any) => {
              const { field, headerName, color, cursor, textDecoration, fixed, ...rest } = column;

              if (field === 'actions') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            <div
                              onClick={() => {
                                localStorage.removeItem('uploadError');
                                history.push(`/new-supply/${rowData.id}`);
                              }}
                              role="presentation"
                              title={t('titles.editSupplyChain')}
                              style={{ cursor: 'pointer', color: '#494747' }}
                            >
                              <FiEdit2 size={20} color="green" />
                            </div>
                            <div
                              onClick={() => setShow({ show: true, client: rowData })}
                              role="presentation"
                              title={t('titles.removeSupplyChain')}
                              style={{ cursor: 'pointer' }}
                            >
                              <Image src={InactiveUser} height={20} />
                            </div>
                            <div
                              onClick={() => handleDownloadFile(rowData.id)}
                              role="presentation"
                              title={t('titles.supplyGenerate')}
                              style={{ cursor: 'pointer' }}
                            >
                              <Image src={PdfPNG} style={{ border: 'none', maxWidth: '80%', marginBottom: '10px' }} />
                            </div>
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              return (
                <>
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap />
                  </Column>
                </>
              );
            })}
          </Table>
          <div className="pagination">
            <div className="pageGroup">
              {!isMobile && <span>{t('pagination.clientsPerPage')}</span>}
              <Dropdown className="selectPerPage">
                <Dropdown.Toggle id="dropdown-limit">{limit}</Dropdown.Toggle>
                <Dropdown.Menu>
                  <Dropdown.Item onClick={() => handleChangeLimit(10)}>10</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(30)}>30</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(50)}>50</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(70)}>70</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(100)}>100</Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            </div>
            <div className="paginator">
              <span>
                {t('pagination.page')} {page} {t('pagination.of')} {lastPage}
              </span>
              <ArrowLeft className="navigator" onClick={handleDownPage} />
              <ArrowRight className="navigator" onClick={handleUpPage} />
            </div>
          </div>
        </>
      ) : (
        <>
          {!showLoader && (
            <EmptyState
              text={t('labels.labelCustomerNotFound')}
              secondaryText={t('labels.labelDescCustomerNotFound')}
              img={EmptyStateImage}
            />
          )}
        </>
      )}
    </Container>
  );
};
