import { IBusiness } from '../interfaces';

export const formatDateToObject = (data: any): any => {
  if (data) {
    const date = new Date(data.getFullYear(), data.getMonth(), data.getDate());
    const formatedDate = date.toISOString().substring(0, 10);
    return formatedDate;
  }
  return '';
};

export function objectBusiness(payload: any, supplierId: any): any {
  if (payload?.freight && payload.freight.value) {
    const freightValue = payload.freight.value.toLowerCase();

    if (freightValue === 'Prepaid abroad' || freightValue === 'prepaid abroad') {
      payload.freight.value = 'prepaid_abroad';
    }
  }

  return {
    buyer_id: payload.importers ? Number(payload.importers.value) : null,
    usuario_id_fornecedor: payload.operator ? Number(payload.operator.value) : null,
    porto_id_carga: payload.portOfLoading ? Number(payload.portOfLoading?.value) : null,
    porto_id_destino: payload.portOfDestination ? Number(payload.portOfDestination?.value) : null,
    fornecedor_id: (payload.providers ? Number(payload.providers.value) : supplierId) || null,
    cliente_id: payload.importers ? Number(payload.importers.value) : null,
    contact: payload.contactImporter,
    responsible: payload.responsible,
    customer: payload.importer,
    des_negocio: payload.observation,
    cod_ordem_compra: payload.po,
    des_forma_pagamento: payload.paymentMethod || '',
    val_negocio_total: payload.totalValue || '',
    val_negocio_antecipado: payload.advenceTotalValue.replace(',', '.') || '',
    des_status_pagamento: payload.statusPayment || '',
    incoterm_id: payload.incoterm ? Number(payload.incoterm.value) : null,
    cod_booking: payload.booking,
    cidade_id_origem: payload.placeOfOrigin ? Number(payload.placeOfOrigin.value) : null,
    cidade_id_destino: payload.finalDestination ? Number(payload.finalDestination.value) : null,
    qtd_container: payload.numOfContainers ? Number(payload.numOfContainers) : null,
    cod_tp_container: payload.containerType ? payload.containerType.value : '',
    des_agente_carga: payload.transporter,
    des_navio: payload.vesselName,
    cod_nota_fiscal: payload.invoice,
    val_volume_m3_total: payload.m3,
    freight: payload?.freight ? payload?.freight?.value?.toLowerCase() : '',
    cost_freight: payload.costFreight ? payload.costFreight.replace(',', '.') : '',
    partial_shipments: payload.partialShipments,
    transhipment: payload.transhipment,
    last_date_shipment: payload.lastDateShipment,
    insurance: payload.insurance,
    desc_pro_forma: payload.descProForma,
    dynamic_title: payload.dynamicTitle,
    dynamic_description: payload.dynamicDescription,
  };
}

export function convertBusiness(business: IBusiness): Partial<IBusiness> {
  if (business?.freight) {
    const freightValue = business.freight.toLowerCase();

    if (freightValue === 'Prepaid abroad' || freightValue === 'prepaid abroad') {
      business.freight = 'prepaid_abroad';
    }
  }

  return {
    usuario_id_fornecedor: business.usuario_id_fornecedor,
    buyer_id: business.buyer_id,
    fornecedor_id: business.fornecedor_id,
    contact: business.contact,
    responsible: business.responsible,
    customer: business.customer,
    des_negocio: business.des_negocio,
    cod_ordem_compra: business.cod_ordem_compra,
    des_forma_pagamento: business.des_forma_pagamento,
    val_negocio_total: business.val_negocio_total,
    val_negocio_antecipado: business.val_negocio_antecipado,
    des_status_pagamento: business.des_status_pagamento,
    incoterm_id: business.incoterm_id,
    cod_booking: business.cod_booking,
    porto_id_destino: business.porto_id_destino,
    porto_id_carga: business.porto_id_carga,
    cidade_id_origem: business.cidade_id_origem,
    cidade_id_destino: business.cidade_id_destino,
    qtd_container: business.qtd_container,
    cod_tp_container: business.cod_tp_container,
    des_agente_carga: business.des_agente_carga,
    des_navio: business.des_navio,
    cod_nota_fiscal: business.cod_nota_fiscal,
    val_volume_m3_total: business.val_volume_m3_total,
    cost_freight: business.cost_freight,
    cliente_id: business.cliente_id,
    consignee_id: business?.consignee_id,
    notify_one_id: business?.notify_one_id,
    notify_two_id: business?.notify_two_id,
    documents_to_be_issued: business?.documents_to_be_issued,
    quantity_of_pieces: business?.quantity_of_pieces,
    tolerance_volume_m3: business?.tolerance_volume_m3,
    estimated_boarding_at: business?.estimated_boarding_at,
    beneficiary: business?.beneficiary,
    beneficiary_account: business?.beneficiary_account,
    beneficiary_bank_final: business?.beneficiary_bank_final,
    swift_bank_final: business?.swift_bank_final,
    intermediary_bank: business?.intermediary_bank,
    swift_intermediary_bank: business?.swift_intermediary_bank,
    iban_code: business?.iban_code,
    additional_info: business?.additional_info,
    freight: business?.freight ? business?.freight?.toLowerCase() : '',
    partial_shipments: business?.partial_shipments,
    transhipment: business?.transhipment,
    last_date_shipment: business?.last_date_shipment,
    insurance: business?.insurance,
    desc_pro_forma: business?.desc_pro_forma,
    dynamic_title: business?.dynamic_title,
    dynamic_description: business?.dynamic_description,
  };
}
