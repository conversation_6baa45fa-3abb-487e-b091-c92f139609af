import React, { useState } from 'react';
import { Row, Col } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';

import { HiOutlinePlusSm } from 'react-icons/hi';
import { useLoader } from '../../../../../contexts/LoaderContext';
import Text from '../../../../../components/Text';
import AuthsService from '../../../../../services/auth.service';
import { format } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';
import Modal from '../../../../../components/Modal';
import { IUserResponse, ResponseUser } from '../../../../../interfaces';

import Button from '../../../../../components/Button';

import './styles.scss';

import { TableComponent } from '../TabelUsers';
import { IColumnsProps } from '../utils';
import { useAnalytics } from '../../../../../contexts/AnalyticsContext';

import Actions from '../Action';

interface IPropsCard {
  idClient: string;
}

const CardUserClient = ({ idClient }: IPropsCard): React.ReactElement => {
  const { t } = useTranslation();
  const [users, setUsers] = useState<ResponseUser | undefined>();
  const [page, setPage] = useState<number>(1);
  const [field] = useState<string>('nom_usuario');
  const [order] = useState<string>('');
  //eslint-disable-next-line
  const [isMobile, setIsMobile] = React.useState<boolean>(window.innerWidth <= 768);
  const { showLoader, setShowLoader } = useLoader();
  const [tableLength, setTableLength] = React.useState(0);
  const { trackEvent } = useAnalytics();
  const [lastPage, setLastPage] = React.useState(1);
  const [limit, setLimit] = React.useState(10);
  //eslint-disable-next-line
  const [query, setQuery] = React.useState<string>('');

  const [showModalCreate, setShowModalCreate] = useState<any>({ show: false, id: null });

  const collums: IColumnsProps[] = [
    {
      field: 'name',
      headerName: t('placeholders.holderNewUser'),
      fixed: false,
      color: '#201E40',
      flexGrow: 6,
      resizable: !!isMobile,
    },
    {
      field: 'dt_criacao',
      headerName: t('table.dateOfCreate'),
      color: '#201E40',
      flexGrow: 3,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'email',
      headerName: t('placeholders.holderEmail'),
      color: '#201E40',
      flexGrow: 5,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'actions',
      headerName: t('modal.modalTitleActions'),
      color: '#201E40',
      flexGrow: 2,
      fixed: false,
      resizable: !!isMobile,
    },
  ];

  const handleChangeLimit = (dataKey: number): void => {
    setPage(1);
    setLimit(dataKey);
    trackEvent('Quotes per page', {
      action: `See ${dataKey} per page`,
      param: 'page',
    });
  };

  const getUsers = React.useCallback(async (): Promise<IUserResponse> => {
    try {
      setShowLoader(true);
      const res = await AuthsService.getUsers(page, limit, query, field, order, false, idClient);
      if (res) {
        setShowLoader(false);
        return res;
      }
      return {} as IUserResponse;
    } catch (error) {
      return {} as IUserResponse;
    }
  }, [limit, page, query, setShowLoader]);

  React.useEffect(() => {
    async function loadList(): Promise<void> {
      const res = await getUsers();
      if (res) {
        const rows: any = res.data.map((item: any, index: number) => ({
          name: item.name || t('labels.notInformed'),
          dt_criacao:
            format(utcToZonedTime(item.created_at, 'America/Sao_Paulo'), t('format.date')) || t('labels.notInformed'),
          email: item.email || t('labels.notInformed'),
          className: index % 2 === 0 ? 'custom-row' : '',
          uuid: item.uuid,
          id: item.id,
        }));

        setUsers(rows);
        setLastPage(res.meta.last_page);
        setTableLength(res.meta.total);
      }
    }
    loadList();
  }, [getUsers, t]);

  const handleDownPage = (): void => {
    const newPage = page - 1;
    if (newPage >= 1) {
      setPage(newPage);
    }

    trackEvent('Quotes Previus Page', {
      action: `Clicou previus page`,
      param: 'Page',
    });
  };

  const handleUpPage = (): void => {
    const newPage = page + 1;
    if (page <= tableLength / limit) {
      setPage(newPage);
    }

    trackEvent('Quotes Next Page', {
      action: `Clicou next page`,
      param: 'Page',
    });
  };

  return (
    <div className="dashboard-container-card">
      <Row className="d-flex">
        <Col md={8}>
          <Text as="span" size="1.2rem" weight={600} color="#201E40">
            {t('titles.usersClient')}
          </Text>
        </Col>
        <Col md={4} className="d-flex justify-content-end gap-2 mb-2 mt-4">
          <Button
            type="submit"
            variant="success"
            cy="test-create"
            onClick={() => setShowModalCreate({ show: true, idClient })}
          >
            <HiOutlinePlusSm /> {t('buttons.newRegister')}
          </Button>
        </Col>

        <Col md={12} className="mt-4">
          <Modal
            className="styleModalNewUser"
            show={showModalCreate.show}
            handleClose={() => setShowModalCreate({ ...showModalCreate, show: false })}
            title={t('titles.newUser')}
            size="lg"
          >
            <Row className="mt-4">
              <Col className="d-flex align-items-center justify-content-end gap-2">
                <Actions idClient={idClient} />
              </Col>
            </Row>
          </Modal>

          <TableComponent
            isMobile={isMobile}
            data={users}
            tableColumns={collums}
            page={page}
            lastPage={lastPage}
            limit={limit}
            handleChangeLimit={handleChangeLimit}
            handleDownPage={handleDownPage}
            handleUpPage={handleUpPage}
            showLoader={showLoader}
            idClient={idClient}
          />
        </Col>
      </Row>
    </div>
  );
};

export default CardUserClient;
