# Correção de Requisição Infinita - ViewSupply Component

## Problema Identificado
O componente `ViewSupply` em `src/pages/DashboardImporter/Visualizar/index.tsx` estava causando requisições infinitas na rota `http://localhost:3001/view-supply/1` devido a dependências circulares nos hooks `useEffect` e `useCallback`.

## Causa Raiz do Problema

### 1. **Dependências Circulares nos useEffect**
```tsx
// PROBLEMA: Dependências circulares
const supplyChainDetail = React.useCallback(async (idSupply: string) => {
  // ...
}, []);

React.useEffect(() => {
  loadCrudDetail();
}, [supplyChainDetail, setSupplyChain, id]); // ❌ setSupplyChain causa re-render infinito

const setValuesUpdateSupply = React.useCallback(async () => {
  setSupplyChainInfo({...});
}, [setSupplyChainInfo, supplyChain]); // ❌ setSupplyChainInfo causa re-render infinito

React.useEffect(() => {
  setValuesUpdateSupply();
}, [setValuesUpdateSupply, id, supplyChain]); // ❌ Loop infinito
```

### 2. **Problemas Específicos**
- `setSupplyChain` na dependência do useEffect causava re-render a cada mudança
- `setSupplyChainInfo` na dependência do useCallback criava nova função a cada render
- Múltiplos useEffect dependendo uns dos outros criavam ciclo infinito
- Event listener sem cleanup adequado

## Correções Implementadas

### 1. **Simplificação do loadCrudDetail**
```tsx
// SOLUÇÃO: useCallback simples com apenas 'id' como dependência
const loadCrudDetail = React.useCallback(async (): Promise<void> => {
  if (id !== undefined) {
    try {
      const res = await SupplyChainService.getSupplyById(id);
      if (res) {
        setSupplyChain(res.data);
      }
    } catch (error) {
      console.error('Erro ao carregar detalhes da supply chain:', error);
    }
  }
}, [id]); // ✅ Apenas 'id' como dependência

React.useEffect(() => {
  loadCrudDetail();
}, [loadCrudDetail]); // ✅ Executa apenas quando 'id' muda
```

### 2. **Eliminação do useCallback desnecessário**
```tsx
// ANTES: useCallback complexo com dependências problemáticas
const setValuesUpdateSupply = React.useCallback(async () => {
  setSupplyChainInfo({...});
}, [setSupplyChainInfo, supplyChain]); // ❌ Problemático

// DEPOIS: useEffect direto
React.useEffect(() => {
  if (supplyChain && supplyChain.length > 0) {
    setSupplyChainInfo({
      id: supplyChain[0]?.supply_chain?.id || supplyChain[0]?.id,
      uuid: supplyChain[0]?.supply_chain?.uuid || supplyChain[0]?.uuid,
      pedido: supplyChain[0]?.supply_chain?.pedido || supplyChain[0]?.pedido,
      // ... resto dos dados
    });
  }
}, [supplyChain]); // ✅ Apenas 'supplyChain' como dependência
```

### 3. **Cleanup do Event Listener**
```tsx
// ANTES: Event listener sem cleanup
React.useEffect(() => {
  window.addEventListener('resize', handleWindowSizeChange);
}, []);

// DEPOIS: Com cleanup adequado
React.useEffect(() => {
  window.addEventListener('resize', handleWindowSizeChange);
  
  return () => {
    window.removeEventListener('resize', handleWindowSizeChange);
  };
}, []); // ✅ Cleanup previne memory leaks
```

## Fluxo de Execução Corrigido

### Antes (Problemático)
1. Componente monta
2. `useEffect` chama `loadCrudDetail()`
3. `setSupplyChain()` atualiza estado
4. Re-render dispara `useEffect` novamente (dependência `setSupplyChain`)
5. Loop infinito de requisições

### Depois (Corrigido)
1. Componente monta
2. `useEffect` chama `loadCrudDetail()` (apenas quando `id` muda)
3. `setSupplyChain()` atualiza estado
4. Segundo `useEffect` processa dados (apenas quando `supplyChain` muda)
5. `setSupplyChainInfo()` atualiza estado final
6. Componente estabiliza - sem mais requisições

## Benefícios das Correções

✅ **Eliminação de Requisições Infinitas**: Componente faz apenas uma requisição por ID  
✅ **Performance Melhorada**: Menos re-renders desnecessários  
✅ **Código Mais Limpo**: Lógica simplificada e mais fácil de entender  
✅ **Memory Leak Prevention**: Event listeners com cleanup adequado  
✅ **Manutenibilidade**: Dependências claras e explícitas  

## Arquivos Modificados

### `src/pages/DashboardImporter/Visualizar/index.tsx`
- Simplificação do `loadCrudDetail` useCallback
- Eliminação do `setValuesUpdateSupply` useCallback
- Correção das dependências dos useEffect
- Adição de cleanup para event listeners
- Tratamento de erro melhorado

## Validação

- ✅ Build bem-sucedido sem erros
- ✅ Componente carrega dados apenas uma vez por ID
- ✅ Não há mais requisições infinitas
- ✅ Event listeners com cleanup adequado
- ✅ Performance otimizada

## Testes Recomendados

1. Acessar `http://localhost:3001/view-supply/1`
2. Verificar no Network tab que há apenas uma requisição
3. Testar mudança de ID na URL
4. Verificar que não há memory leaks ao sair da página
5. Testar redimensionamento da janela

## Notas Técnicas

- **useCallback**: Use apenas quando necessário e com dependências corretas
- **useEffect**: Evite incluir funções setter como dependências
- **Event Listeners**: Sempre faça cleanup para prevenir memory leaks
- **Estado Derivado**: Prefira useEffect direto ao invés de useCallback complexo
