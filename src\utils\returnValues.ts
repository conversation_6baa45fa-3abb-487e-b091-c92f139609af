import { convertNumber } from './convertNumber';

export function transformObject(obj: any): any {
  const result = [];
  if (obj.range) {
    const { min, max } = obj.range;
    result.push({
      type: 'range',
      min: convertNumber(min) ?? '',
      max: convertNumber(max) ?? '',
    });
  }

  if (obj.unique) {
    result.push({
      type: 'unique',
      value: convertNumber(obj.unique),
    });
  }

  if (obj.list) {
    const { list } = obj;
    result.push({
      type: 'list',
      value: list ? list.map((element: any) => convertNumber(element)) : [],
    });
  }

  return result;
}

export function filterNonEmptyValues(arr: any): any {
  return arr.filter((obj: any) => {
    const values = Object.values(obj);
    return values.some((val) => val !== null && val !== '');
  });
}
