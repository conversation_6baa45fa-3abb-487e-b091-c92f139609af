import React, { useState } from 'react';
import { Col, Container, Row } from 'react-bootstrap';
import Modal from '../../../components/Modal';
import { useTranslation } from 'react-i18next';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import { Box } from '@mui/material';
import CompaniesService from '../../../services/companies.service';
import CardInfoCompany from './DadosCadastrais';
import Button from '../../../components/Button';
import { useLoader } from '../../../contexts/LoaderContext';
import Text from '../../../components/Text';
import { IParam } from '../../../interfaces';
import FlorestaService, { IFlorestaDocumento } from '../../../services/floresta.service';
import { useParams } from 'react-router';
import { IColumnsProps } from '../utils';
import { TableFloresta } from './Floresta/TableFloresta';
import { useAnalytics } from '../../../contexts/AnalyticsContext';
import './styles.scss';
import CardInfoFloresta from './Floresta';
import CardInfoMateriaPrima from './MateriaPrima';
import { safeJsonParse } from '../../../utils/geoJsonHelpers';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const NewSupplier = (): React.ReactElement => {
  const { t } = useTranslation();
  const { id } = useParams<IParam>();
  const [company, setCompany] = useState({} as any);
  const [valueTab, setValueTab] = React.useState(0);
  const { showLoader } = useLoader();
  const [companyInfo, setCompanyInfo] = useState({
    id: 0,
    companyName: '',
    cnpj: '',
    address: '',
    razaoSocial: '',
    city: '',
    state: '',
  });
  const [florestas, setFlorestas] = React.useState([] as IFlorestaDocumento[]);
  const { trackEvent } = useAnalytics();
  const [isMobile, setIsMobile] = React.useState<boolean>(window.innerWidth <= 768);
  const [lastPage, setLastPage] = React.useState(1);
  const [limit, setLimit] = React.useState(10);
  const [tableLength, setTableLength] = React.useState(0);
  const [page, setPage] = React.useState(1);
  const [showModalDocument, setShowModalDocument] = useState<any>({ show: false, tipo: 1 });
  const [showHelpDocModal, setShowHelpDocModal] = useState(false);
  const [uploadError, setUploadError] = useState<any>(null);

  // Função para limpar erro de upload
  const clearUploadError = () => {
    setUploadError(null);
    localStorage.removeItem('uploadError');
  };

  // Verificar se há erro salvo no localStorage ao montar o componente
  React.useEffect(() => {
    const savedError = localStorage.getItem('uploadError');

    if (savedError) {
      try {
        const errorObj = JSON.parse(savedError);
        setUploadError(errorObj);
      } catch (e) {
        localStorage.removeItem('uploadError');
      }
    }
  }, [setUploadError]);

  const handleWindowSizeChange = (): void => {
    if (window.innerWidth <= 768) {
      setIsMobile(true);
    } else {
      setIsMobile(false);
    }
  };

  React.useEffect(() => {
    window.addEventListener('resize', handleWindowSizeChange);
  }, []);

  const collums: IColumnsProps[] = [
    {
      field: 'nome',
      headerName: t('table.name'),
      fixed: true,
      color: '#201E40',
      flexGrow: 6,
      resizable: !!isMobile,
    },
    {
      field: 'identificacao',
      headerName: t('labels.identificacao'),
      color: '#201E40',
      flexGrow: 4,
      fixed: false,
      resizable: !!isMobile,
    },

    {
      field: 'car',
      headerName: 'CAR',
      color: '#201E40',
      flexGrow: 4,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'actions',
      headerName: t('modal.modalTitleActions'),
      color: '#201E40',
      flexGrow: 2,
      fixed: false,
      resizable: !!isMobile,
    },
  ];

  const companyDetail = React.useCallback(async (): Promise<any> => {
    try {
      const res = await CompaniesService.getCompanyById(String(id));
      if (res) {
        return res;
      }
      return {};
    } catch (error) {
      return {};
    }
  }, [id]);

  React.useEffect(() => {
    let isCleaningUp = false;

    async function loadCrudDetail(): Promise<void> {
      const res = await companyDetail();
      if (res && !isCleaningUp) {
        setCompany(res.data);
      }
    }

    if (id) {
      loadCrudDetail();
    }

    return () => {
      isCleaningUp = true;
    };
  }, [companyDetail]);

  const florestaDetail = React.useCallback(async (): Promise<any> => {
    try {
      const res: any = await FlorestaService.getDocumentoFloresta(String(company?.id));

      if (typeof res === 'string') {
        const parsedData = safeJsonParse(res);
        if (!parsedData) {
          //eslint-disable-next-line
          console.log('Erro ao analisar JSON:', res);
        }
        // Continuar processamento com parsedData...
      }

      if (res) {
        return res;
      }

      return {};
    } catch (error) {
      return {};
    }
  }, [company]);

  const setValuesUpdate = React.useCallback(async () => {
    setCompanyInfo({
      id: company?.id,
      companyName: company?.name_sender,
      cnpj: company?.tax_identifier_number,
      address: company?.address,
      razaoSocial: company?.name,
      city: company?.city,
      state: company?.state,
    });
  }, [setCompanyInfo, company]);

  React.useEffect(() => {
    if (company != undefined) {
      setValuesUpdate();
    }
  }, [setValuesUpdate, company]);

  async function loadListFloresta(): Promise<void> {
    const res = await await florestaDetail();

    if (res) {
      const rows: any = res.data.map((item: any, index: number) => ({
        nome: item.floresta?.nome || item.nome,
        identificacao: item.floresta?.identificacao || item.identificacao,
        latitude: item.floresta?.latitude || item.latitude,
        longitude: item.floresta?.longitude || item.longitude,
        pais_origem: item.floresta?.pais_origem || item.pais_origem,
        car: item.documento?.documento_path || 'semdoc',
        documento: item.documento || undefined,
        tamanho_arquivo: item.documento?.tamanho_arquivo || '',
        geojson: item.geojson || undefined,
        className: index % 2 === 0 ? 'custom-row' : '',
        uuid: item.floresta?.uuid || item.uuid,
        id: item.floresta?.id || item.id,
      }));

      setFlorestas(rows);
      setLastPage(res.meta.last_page);
      setTableLength(res.meta.total);
    }
  }

  React.useEffect(() => {
    let isCleaningUp = false;

    if (companyInfo != undefined && !isCleaningUp) {
      loadListFloresta();
    }

    return () => {
      isCleaningUp = true;
    };
  }, [florestaDetail, setFlorestas, setLastPage, setTableLength]);

  const handleChangeLimit = (dataKey: number): void => {
    setPage(1);
    setLimit(dataKey);
    trackEvent('Quotes per page', {
      action: `See ${dataKey} per page`,
      param: 'page',
    });
  };

  const handleDownPage = (): void => {
    const newPage = page - 1;
    if (newPage >= 1) {
      setPage(newPage);
    }

    trackEvent('Quotes Previus Page', {
      action: `Clicou previus page`,
      param: 'Page',
    });
  };

  const handleUpPage = (): void => {
    const newPage = page + 1;
    if (page <= tableLength / limit) {
      setPage(newPage);
    }

    trackEvent('Quotes Next Page', {
      action: `Clicou next page`,
      param: 'Page',
    });
  };

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValueTab(newValue);
  };

  const a11yProps = (index: number) => {
    return {
      id: `scrollable-auto-tab-${index}`,
      'aria-controls': `scrollable-auto-tabpanel-${index}`,
    };
  };

  function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
      >
        {value === index && <Box sx={{ pt: 3, pb: 3, pl: 0, pr: 0 }}>{children}</Box>}
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      <Text className="pages-title" as="h1" size="2rem" weight={700}>
        {t('titles.novoFornecedor')}
      </Text>
      <Container className="card-style-default">
        <Modal
          show={showModalDocument.show}
          handleClose={() => setShowModalDocument({ ...showModalDocument, show: false })}
          title={showModalDocument.tipo === 1 ? t('titles.novaFloresta') : t('titles.novaMateriaPrima')}
          size="xl"
          className="styleModalConfirm"
          colorIcon
        >
          {showModalDocument.tipo === 1 ? (
            <CardInfoFloresta
              idFornecedor={company?.id}
              closeModal={setShowModalDocument}
              atualziarFlorestas={loadListFloresta}
              setUploadError={setUploadError}
            />
          ) : (
            <CardInfoMateriaPrima idFornecedor={company?.id} closeModal={setShowModalDocument} />
          )}
        </Modal>

        <Row className="d-flex flex-row justify-content-between">
          <Row className="mt-2" style={{ paddingLeft: '2rem' }}>
            <Col>
              <Tabs value={valueTab} onChange={handleChange} variant="fullWidth" centered>
                <Tab label={t('titles.dadosFornecedor')} {...a11yProps(0)} style={{ textTransform: 'none' }} />
                <Tab
                  disabled={id === undefined}
                  label={t('titles.floresta')}
                  {...a11yProps(1)}
                  style={{ textTransform: 'none' }}
                />
              </Tabs>

              <CustomTabPanel value={valueTab} index={0}>
                <Col md={12} className="mt-2">
                  <label className="titleCard">{t('labels.supplierIdentifer')}</label>
                  <CardInfoCompany fornecedorUiid={id || ''} companyInfo={companyInfo} />
                </Col>
              </CustomTabPanel>

              <CustomTabPanel value={valueTab} index={1}>
                <Container>
                  <Row>
                    <Col md={8} className="mt-2" style={{ paddingLeft: '15px' }}>
                      <label className="descricaoCard">{t('messages.addFlorest')}</label>
                    </Col>
                    <Col md={4} className="d-flex justify-content-end gap-2 mb-2 mt-4">
                      <Button
                        cy="btn-new"
                        variant="outline-secondary"
                        onClick={() => setShowModalDocument({ show: true, tipo: 1 })}
                      >
                        {t('buttons.addFlorest')}
                      </Button>
                    </Col>
                  </Row>

                  {/* Mensagem de erro do upload */}
                  {uploadError && (
                    <Col md={12} className="mt-2">
                      <div
                        className="alert alert-danger d-flex justify-content-between align-items-center mb-3"
                        role="alert"
                      >
                        <div className="d-flex align-items-center">
                          <i className="fas fa-exclamation-triangle me-2"></i>
                          <span>{uploadError.message}</span>
                        </div>
                        <div className="d-flex gap-2">
                          {uploadError.showHelpButton && (
                            <Button
                              cy="btn-help"
                              variant="outline-primary"
                              size="sm"
                              onClick={() => setShowHelpDocModal(true)}
                            >
                              Resolver
                            </Button>
                          )}
                          <Button cy="btn-close-error" variant="outline-secondary" size="sm" onClick={clearUploadError}>
                            ✕
                          </Button>
                        </div>
                      </div>
                    </Col>
                  )}
                  <TableFloresta
                    isMobile={isMobile}
                    data={florestas}
                    tableColumns={collums}
                    page={page}
                    lastPage={lastPage}
                    limit={limit}
                    handleChangeLimit={handleChangeLimit}
                    handleDownPage={handleDownPage}
                    handleUpPage={handleUpPage}
                    showLoader={showLoader}
                    idFornecedor={companyInfo?.id}
                    atualziarFlorestas={loadListFloresta}
                    setUploadError={setUploadError}
                  />
                  {/* Modal de ajuda para conversão de PDF */}
                  <Modal
                    show={showHelpDocModal}
                    handleClose={() => setShowHelpDocModal(false)}
                    title="Como resolver problemas com PDF"
                    size="lg"
                    className="styleModalConfirm"
                  >
                    <div className="p-4">
                      <div className="alert alert-info mb-4">
                        <strong>Problemas mais comuns:</strong>
                        <ul className="mb-0 mt-2">
                          <li>Arquivo muito grande (limite: 10MB)</li>
                          <li>PDF corrompido ou com erro</li>
                          <li>Versão do PDF incompatível</li>
                          <li>Arquivo protegido por senha</li>
                        </ul>
                      </div>

                      <h5>Soluções recomendadas:</h5>
                      <div className="mt-3">
                        <h6>1. Corrigir erros no PDF (Recomendado):</h6>
                        <div className="ms-3 mb-3">
                          <strong>Ferramentas online gratuitas:</strong>
                          <ul>
                            <li>
                              <strong>1°. Reduzir compressão do PDF: </strong>
                              <a href="https://www.pdfyeah.com/decompress-pdf/" target="_blank" rel="noreferrer">
                                https://www.pdfyeah.com/decompress-pdf/
                              </a>
                            </li>
                            <li>
                              <strong>2°. SmallPDF: </strong>
                              <a href="https://smallpdf.com/compress-pdf" target="_blank" rel="noreferrer">
                                smallpdf.com/compress-pdf
                              </a>
                            </li>
                            <li>
                              <strong>3°. ILovePDF: </strong>
                              <a href="https://ilovepdf.com/compress_pdf" target="_blank" rel="noreferrer">
                                ilovepdf.com/compress_pdf
                              </a>
                            </li>
                            <li>
                              <strong>4°. PDF24: </strong>
                              <a href="https://tools.pdf24.org/pt/comprimir-pdf" target="_blank" rel="noreferrer">
                                tools.pdf24.org/pt/comprimir-pdf
                              </a>
                            </li>
                          </ul>
                          <div className="alert alert-success mt-2">
                            <small>
                              <strong>Dica:</strong> Essas ferramentas otimizam o PDF, desconprimindo ou reduzindo o
                              tamanho e mantendo a qualidad e.
                            </small>
                          </div>
                        </div>

                        <h6>2. Usando Adobe Acrobat (Se disponível):</h6>
                        <div className="ms-3 mb-3">
                          <ol>
                            <li>Abra o PDF no Adobe Acrobat</li>
                            <li>
                              Vá em &quot;Arquivo&quot; → &quot;Salvar como outro&quot; → &quot;PDF otimizado&quot;
                            </li>
                            <li>Nas configurações, reduza a qualidade das imagens para 150 DPI</li>
                            <li>Desmarque opções desnecessárias</li>
                            <li>Salve o arquivo</li>
                          </ol>
                        </div>

                        <h6>3. Verificações antes do upload:</h6>
                        <div className="ms-3">
                          <ul>
                            <li>✅ Arquivo em formato PDF</li>
                            <li>✅ Tamanho menor que 10MB</li>
                            <li>✅ PDF não protegido por senha</li>
                            <li>✅ Arquivo não corrompido (abre normalmente)</li>
                            <li>✅ Conteúdo legível e completo</li>
                          </ul>
                        </div>
                      </div>

                      <div className="d-flex justify-content-end mt-4">
                        <Button
                          cy="btn-close-help"
                          variant="outline-secondary"
                          onClick={() => setShowHelpDocModal(false)}
                        >
                          Fechar
                        </Button>
                      </div>
                    </div>
                  </Modal>
                </Container>
              </CustomTabPanel>
            </Col>
            <Col md={12} className="d-flex justify-content-end gap-2 mb-2 mt-4">
              <Button
                cy="btn-cancel"
                type="button"
                variant="outline-green"
                onClick={(event) => handleChange(event, valueTab - 1)}
                disabled={valueTab === 0}
              >
                {t('pagination.previous')}
              </Button>
              <Button
                cy="btn-cancel"
                type="button"
                variant="outline-green"
                onClick={(event) => handleChange(event, valueTab + 1)}
                disabled={valueTab === 1 || id === undefined}
              >
                {t('pagination.next')}
              </Button>
            </Col>
          </Row>
        </Row>
      </Container>
    </div>
  );
};

export default NewSupplier;
