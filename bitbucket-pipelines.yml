# This is a sample build configuration for JavaScript.
# Check our guides at https://confluence.atlassian.com/x/14UWN for more examples.
# Only use spaces to indent your .yml configuration.
# -----
# You can specify a custom docker image from Docker Hub as your build environment.
image: node:14.17.1

pipelines:
  branches:
    main:
      - step:
          name: Build projeto
          size: 2x # Double resources available for this step.
          caches:
            - node
          script: # Modify the commands below to build your repository.
            - npx yarn
            - CI=false npx yarn build
          artifacts: # defining the artifacts to be passed to each future step.
            - build/**
      - step:
          name: Deploy to S3 Prod
          deployment: Production
          script:
            - pipe: atlassian/aws-s3-deploy:0.3.8
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID_DEPLOY
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY_DEPLOY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION_DEPLOY
                S3_BUCKET: $S3_BUCKET_DEPLOY
                LOCAL_PATH: 'build/'
            - pipe: atlassian/aws-cloudfront-invalidate:0.4.1
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID_DEPLOY
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY_DEPLOY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION_DEPLOY
                DISTRIBUTION_ID: 'E106XTS2DGXBRX' #app.woodflow.com.br
                PATHS: '/index.html'
    stable:
      - step:
          name: Build projeto
          size: 2x # Double resources available for this step.
          caches:
            - node
          script: # Modify the commands below to build your repository.
            - npx yarn
            - mv .env.staging .env.production
            - CI=false npx yarn build
          artifacts: # defining the artifacts to be passed to each future step.
            - build/**
      - step:
          name: Deploy to S3
          deployment: Staging
          script:
            - pipe: atlassian/aws-s3-deploy:0.3.8
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID_DEPLOY
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY_DEPLOY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION_DEPLOY
                S3_BUCKET: $S3_BUCKET_DEPLOY
                LOCAL_PATH: 'build/'
            - pipe: atlassian/aws-cloudfront-invalidate:0.4.1
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID_DEPLOY
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY_DEPLOY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION_DEPLOY
                DISTRIBUTION_ID: 'E1FANZR2DWDEFU' #hmg.woodflow.com.br
                PATHS: '/index.html'