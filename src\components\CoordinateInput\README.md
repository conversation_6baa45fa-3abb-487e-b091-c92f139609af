# CoordinateInput Component

Um componente React especializado para entrada de coordenadas geográficas no formato DMS (Graus, Minutos, Segundos) com formatação automática e validação.

**Formato:** `27°59'05,7" S` (DD°MM'SS,D" C)

## Características

- **Formatação DMS automática**: Converte entrada numérica para formato Graus°Minutos'Segundos,Decimal" Direção
- **Formatação progressiva**: Aplica símbolos DMS conforme o usuário digita
- **Suporte a colar (Paste)**: Aceita múltiplos formatos de coordenadas quando coladas:
  - Formato DMS completo: `27°59'05,7" S`
  - Formato decimal: `-23.550520` ou `23.550520 S`
  - Formato DMS sem formatação: `275905S` ou `27 59 05 S`
- **Conversão automática**: Converte formatos colados para DMS padrão
- **Validação de range inteligente**:
  - Permite digitação livre durante a entrada
  - Aplica limitação de range apenas quando o usuário sai do campo (onBlur)
  - Latitude: 00°00'00,0" N/S até 90°00'00,0" N/S
  - Longitude: 00°00'00,0" E/W até 180°00'00,0" E/W
- **Direções cardeais**: Adiciona automaticamente N/S para latitude, E/W para longitude
- **Prevenção de entrada inválida**: Bloqueia caracteres não permitidos (apenas números, °'", e NSEW)
- **Feedback visual**: Estados de erro com mensagens personalizadas
- **Acessibilidade**: Suporte completo a screen readers e navegação por teclado
- **Experiência fluida**: Formatação não intrusiva durante a digitação

## Uso

```tsx
import CoordinateInput from './components/CoordinateInput';

function MyComponent() {
  const [latitude, setLatitude] = useState('');
  const [longitude, setLongitude] = useState('');
  const [latError, setLatError] = useState(false);
  const [lngError, setLngError] = useState(false);

  return (
    <div>
      <CoordinateInput
        cy="test-latitude"
        id="latitude"
        label="Latitude"
        placeholder="Ex: -23.550520"
        value={latitude}
        onChange={setLatitude}
        onBlur={() => {
          // Validação opcional no blur
          if (latitude && !isValidLatitude(latitude)) {
            setLatError(true);
          }
        }}
        type="latitude"
        isInvalid={latError}
        msg={latError ? 'Latitude inválida' : ''}
      />

      <CoordinateInput
        cy="test-longitude"
        id="longitude"
        label="Longitude"
        placeholder="Ex: -46.633309"
        value={longitude}
        onChange={setLongitude}
        onBlur={() => {
          // Validação opcional no blur
          if (longitude && !isValidLongitude(longitude)) {
            setLngError(true);
          }
        }}
        type="longitude"
        isInvalid={lngError}
        msg={lngError ? 'Longitude inválida' : ''}
      />
    </div>
  );
}
```

## Props

| Prop          | Tipo                        | Obrigatório | Descrição                                  |
| ------------- | --------------------------- | ----------- | ------------------------------------------ |
| `value`       | `string`                    | ✅          | Valor atual do campo                       |
| `onChange`    | `(value: string) => void`   | ✅          | Callback chamado quando o valor muda       |
| `type`        | `'latitude' \| 'longitude'` | ✅          | Tipo de coordenada para validação          |
| `id`          | `string`                    | ✅          | ID único do elemento                       |
| `cy`          | `string`                    | ✅          | Atributo data-cy para testes               |
| `placeholder` | `string`                    | ✅          | Texto de placeholder                       |
| `label`       | `string`                    | ❌          | Label do campo                             |
| `onBlur`      | `() => void`                | ❌          | Callback chamado quando o campo perde foco |
| `isInvalid`   | `boolean`                   | ❌          | Se o campo está em estado de erro          |
| `msg`         | `string`                    | ❌          | Mensagem de erro a ser exibida             |
| `disabled`    | `boolean`                   | ❌          | Se o campo está desabilitado               |
| `readOnly`    | `boolean`                   | ❌          | Se o campo é somente leitura               |
| `required`    | `boolean`                   | ❌          | Se o campo é obrigatório                   |
| `maxLength`   | `number`                    | ❌          | Comprimento máximo do campo                |
| `desc`        | `string`                    | ❌          | Descrição para acessibilidade              |

## Exemplos de Formatação

### Entrada → Saída Formatada

**Durante a digitação:**

- `"23,550520"` → `"23.550520"` (vírgula convertida para ponto)
- `"23.5505201234"` → `"23.550520"` (limitado a 6 casas decimais)
- `"95"` → `"95"` (permite valores fora do range durante digitação)
- `"185"` → `"185"` (permite valores fora do range durante digitação)
- `"23.550520abc"` → `"23.550520"` (caracteres inválidos removidos)
- `"-23.550520"` → `"-23.550520"` (coordenadas negativas suportadas)

**Ao sair do campo (onBlur):**

- `"95"` → `"90"` (latitude limitada ao range válido)
- `"185"` → `"180"` (longitude limitada ao range válido)
- `"-95"` → `"-90"` (latitude negativa limitada)
- `"-185"` → `"-180"` (longitude negativa limitada)

## Validação

O componente inclui validação automática baseada no tipo:

- **Latitude**: Deve estar entre -90 e +90 graus
- **Longitude**: Deve estar entre -180 e +180 graus
- **Formato**: Aceita números decimais com até 6 casas decimais
- **Caracteres permitidos**: Números (0-9), ponto (.), vírgula (,), sinal de menos (-)

## Estilos

O componente usa classes CSS customizadas que podem ser sobrescritas:

- `.coordinate-input`: Container principal
- `.coordinate-input__error`: Estado de erro
- `.coordinate-input__label-error`: Label em estado de erro
- `.coordinate-input__label-disabled`: Label desabilitada
- `.coordinate-input__invalid-feedback`: Mensagem de erro

## Testes

O componente inclui testes abrangentes que cobrem:

- Renderização correta
- Formatação de entrada para latitude e longitude
- Limitação de range
- Limitação de casas decimais
- Remoção de caracteres inválidos
- Estados de erro, desabilitado e somente leitura
- Eventos de blur e mudança
