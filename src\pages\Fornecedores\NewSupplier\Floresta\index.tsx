import React, { useCallback, useState } from 'react';
import { Card, Container, Image, Row, Col, Button } from 'react-bootstrap';
import CustomInput from '../../../../components/Input/CustomInput';
import FlorestaService, {
  IDocumento,
  IFloresta,
  IGeoJson,
  IpropsMateriaPrima,
} from '../../../../services/floresta.service';
import Modal from '../../../../components/Modal';
import { useTranslation } from 'react-i18next';
import CustomButton from '../../../../components/Button';
import { useLoader } from '../../../../contexts/LoaderContext';
import toastMsg, { ToastType } from '../../../../utils/toastMsg';
import { useHistory } from 'react-router';
import { CardContent } from '@mui/material';
import DocumentosService, { IGrupoDocumentoFlorestaSave } from '../../../../services/documentos.service';
import { formatDateToString } from '../../../../utils/formatDateToString';
import { Upload, FileIcon } from 'lucide-react';
import DatePicker from '../../../../components/Datepicker';
import InactiveUser from '../../../../statics/delete.svg';
import InfoIcon from '../../../../statics/info-circle.svg';
import { isValidLatitude, isValidLongitude } from '../../../../utils/formatCoordinates';
import CoordinateInput from '../../../../components/CoordinateInput';
import './styles.scss';

interface IPropsFloresta {
  idFornecedor: string;
  closeModal: any;
  atualziarFlorestas?: any;
  setUploadError: (error: any) => void;
  car?: IDocumento;
  floresta?: IFloresta;
  geojson?: IGeoJson;
}

interface FileItem {
  file: File;
  preview?: string;
  path?: string;
  tamanho_arquivo?: string;
}

const CardInfoFloresta = ({
  idFornecedor,
  closeModal,
  atualziarFlorestas,
  setUploadError,
  car,
  floresta,
}: IPropsFloresta): React.ReactElement => {
  const [documento, setDocumento] = useState({} as IDocumento);
  const [show, setShow] = useState<any>({ show: false, client: null, idDoc: null });
  const [showHelpModal, setShowHelpModal] = useState(false);
  const [files, setFiles] = useState<FileItem[]>([] as FileItem[]);
  const { t } = useTranslation();
  const { setShowLoader } = useLoader();
  const history = useHistory();
  const [dataAtual, setDataAtual] = useState<Date | null>(new Date());
  const [florestaName, setFlorestaName] = useState('');
  const [identificacao, setIdentificacao] = useState('');
  const [latitude, setLatitude] = useState('');
  const [longitude, setLongitude] = useState('');
  const [paisOrigemFloresta, setPaisOrigemFloresta] = useState('');
  const [materiaPrima, setMateriaPrima] = useState<IpropsMateriaPrima[]>([]);
  const [nomeMp, setNomeMp] = useState('');
  const [cientificoMp, setCientificoMp] = useState('');
  const [latitudeError, setLatitudeError] = useState(false);
  const [longitudeError, setLongitudeError] = useState(false);

  const handleLatitudeChange = (value: string) => {
    setLatitude(value);
    setLatitudeError(false);
  };

  const handleLongitudeChange = (value: string) => {
    setLongitude(value);
    setLongitudeError(false);
  };

  const handleLatitudeBlur = () => {
    if (latitude && !isValidLatitude(latitude)) {
      setLatitudeError(true);
    }
  };

  const handleLongitudeBlur = () => {
    if (longitude && !isValidLongitude(longitude)) {
      setLongitudeError(true);
    }
  };

  const addMateriaPrimaria = () => {
    if (nomeMp !== '' || cientificoMp !== '') {
      setMateriaPrima([...materiaPrima, { nome: nomeMp, nome_cientifico: cientificoMp, status: 'A' }]);
      setNomeMp('');
      setCientificoMp('');
    }
  };

  const selectMateriasPrima = useCallback(async (): Promise<void> => {
    if (floresta?.id) {
      const resMT = await FlorestaService.getMateriaPrimaSelect(floresta.id.toString());
      setMateriaPrima(resMT.data);
    }
  }, [setMateriaPrima]);

  React.useEffect(() => {
    async function loadDocuments(): Promise<void> {
      setDocumento({
        ...documento,
        id: car?.id ?? 0,
        uuid: car?.uuid ?? '',
        nome: car?.nome ?? '',
        documento_path: car?.documento_path ?? '',
        extensao: car?.extensao ?? '',
        descricao: car?.descricao ?? '',
        vencimento: car?.vencimento ?? '',
        status: car?.status ?? '',
        tipo_documento_id: car?.tipo_documento_id ?? 0,
        tamanho_arquivo: car?.tamanho_arquivo ?? '',
      });

      setFlorestaName(floresta?.nome ?? '');
      setIdentificacao(floresta?.identificacao ?? '');
      setLatitude(floresta?.latitude ?? '');
      setLongitude(floresta?.longitude ?? '');
      setPaisOrigemFloresta(floresta?.pais_origem ?? '');
      selectMateriasPrima();
    }
    if (floresta) {
      loadDocuments();
    }

    //setMateriaPrima([...materiaPrima, { nome: 'nomeMp', cientifico: 'cientificoMp' }]);
  }, [
    setDocumento,
    setFlorestaName,
    setIdentificacao,
    setLatitude,
    setLongitude,
    setPaisOrigemFloresta,
    setMateriaPrima,
  ]);

  const removeMateriaPrima = (indice: number) => () => {
    const newMateriaPrima = [...materiaPrima];
    newMateriaPrima.splice(indice, 1);
    setMateriaPrima(newMateriaPrima);
  };

  const validTypes = ['application/zip', 'application/x-zip-compressed', 'application/x-zip'];

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      // Validar todos os arquivos
      const filesArray = Array.from(event.target.files);
      for (const file of filesArray) {
        if ((file.type !== 'application/pdf' && !validTypes.includes(file.type)) || file.size < 1024) {
          toastMsg(ToastType.Error, t('exceptions.pdfTypeZip'));
          return;
        }
      }

      const newFiles = filesArray.map((file) => ({
        file,
        preview: URL.createObjectURL(file),
      }));
      setFiles([...files, ...newFiles]);
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (event.dataTransfer.files) {
      // Validar todos os arquivos
      const filesArray = Array.from(event.dataTransfer.files);
      for (const file of filesArray) {
        if ((file.type !== 'application/pdf' && !validTypes.includes(file.type)) || file.size < 1024) {
          toastMsg(ToastType.Error, t('exceptions.pdfTypeZip'));
          return;
        }
      }

      const newFiles = filesArray.map((file) => ({
        file,
        preview: URL.createObjectURL(file),
      }));
      setFiles([...files, ...newFiles]);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const removeFile = (index: number) => {
    const newFiles = [...files];
    URL.revokeObjectURL(newFiles[index].preview || '');
    newFiles.splice(index, 1);
    setFiles(newFiles);
  };

  const formatFileSize = (bytes: number) => {
    // Format file size
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];

    if (bytes < k) {
      return bytes + ' ' + sizes[0];
    } else if (bytes < k * k) {
      return parseFloat((bytes / k).toFixed(2)) + ' ' + sizes[1];
    } else if (bytes < k * k * k) {
      return parseFloat((bytes / (k * k)).toFixed(2)) + ' ' + sizes[2];
    } else {
      return parseFloat((bytes / (k * k * k)).toFixed(2)) + ' ' + sizes[3];
    }
  };

  const getTipoDocumento = React.useCallback(async (idTipo: string): Promise<any> => {
    try {
      const res: any = await DocumentosService.getTiposDocumento(idTipo);

      if (res) {
        return res;
      }
    } catch (error) {
      //eslint-disable-next-line
      //console.log('error', 'Erro ao buscar Tipo do Documento');
    }
  }, []);

  const getDocumentoById = React.useCallback(async (id: string): Promise<any> => {
    try {
      const res: any = await DocumentosService.getDocumentoCar(id);

      if (res) {
        return res.data;
      }
    } catch (error) {
      //eslint-disable-next-line
      //console.log('error', 'Erro ao buscar documento');
    }
  }, []);

  React.useEffect(() => {
    async function loadDocSave(): Promise<void> {
      if (car?.id !== undefined) {
        const docAtual = await getDocumentoById(car?.id.toString());

        //setDataAtual(new Date());
        //setDataAtual(new Date(docAtual?.data?.documento.vencimento));

        setDataAtual(new Date(docAtual?.vencimento));
        const fileName = docAtual?.documento_path.split('/').pop() || 'documento.pdf';

        setFiles([
          {
            file: new File([docAtual?.documento_path], fileName),
            preview: URL.createObjectURL(new File([docAtual?.documento_path], docAtual?.documento_path)),
            path: docAtual?.documento_path,
          },
        ]);
      }
    }

    loadDocSave();
  }, [getDocumentoById, setDataAtual, setFiles]);

  const handleSubmit = React.useCallback(async () => {
    try {
      setShowLoader(true);

      if (!florestaName || !identificacao || !paisOrigemFloresta) {
        toastMsg(ToastType.Error, t('messages.errorRequiredFields'));
        //setShowLoader(false);
        return;
      }

      let materiaPrimaFinal = [...materiaPrima];

      // Verifica se há dados nos campos que não foram adicionados
      if (materiaPrimaFinal.length <= 0 && (nomeMp.trim() !== '' || cientificoMp.trim() !== '')) {
        const novaMateriaPrima = {
          nome: nomeMp,
          nome_cientifico: cientificoMp,
          status: 'A',
        };
        materiaPrimaFinal = [...materiaPrimaFinal, novaMateriaPrima];
      }

      // Verifica se há pelo menos uma matéria-prima
      if (materiaPrimaFinal.length <= 0) {
        toastMsg(ToastType.Error, t('messages.errorRequiredFieldsMT'));
        //setShowLoader(false);
        return;
      }

      // Valida coordenadas se preenchidas
      if (latitude && !isValidLatitude(latitude)) {
        toastMsg(ToastType.Error, t('messages.errorInvalidLatitude'));
        //setLatitudeError(true);
        setShowLoader(false);
        return;
      }

      if (longitude && !isValidLongitude(longitude)) {
        toastMsg(ToastType.Error, t('messages.errorInvalidLongitude'));
        //setLongitudeError(true);
        setShowLoader(false);
        return;
      }

      if (floresta) {
        const updateloresta: IFloresta = {
          id: floresta.id,
          uuid: floresta.uuid,
          nome: florestaName,
          identificacao: identificacao,
          latitude: latitude,
          longitude: longitude,
          pais_origem: paisOrigemFloresta,
          status: 'A',
          fornecedor_id: idFornecedor ? Number(idFornecedor) : 0,
          materia_prima: materiaPrimaFinal || null,
        };

        await FlorestaService.updateFloresta(updateloresta);

        if (files.length > 0) {
          const formData = new FormData();
          let arquivoSize = '';

          files.forEach((file) => {
            if (file.file.type === '') {
              arquivoSize = car?.tamanho_arquivo || '';
            } else if (file.file.type === 'application/pdf') {
              formData.append('file', file.file);
              arquivoSize = formatFileSize(file.file.size);
            } else {
              formData.append('zip_file', file.file);
            }
          });

          if (car !== undefined) {
            formData.append('id', String(car.id));
            const tipoDocumento = await getTipoDocumento('33');

            const documentoSave: IGrupoDocumentoFlorestaSave = {
              id: car.id,
              nome: tipoDocumento.data.titulo,
              descricao: tipoDocumento.data.descricao,
              status: 'A',
              documento: {
                nome: tipoDocumento.data.titulo,
                descricao: tipoDocumento.data.descricao,
                descricao_en: tipoDocumento.data.descricao_en,
                extensao: '',
                tamanho_arquivo: arquivoSize,
                vencimento: formatDateToString(dataAtual),
                tipo_documento_id: String(tipoDocumento.data.id),
                status: 'A',
              },
              floresta_id: String(floresta.id),
            };

            formData.append('documento', JSON.stringify(documentoSave));
          } else {
            formData.append('id', String(0));
            const tipoDocumento = await getTipoDocumento('33');

            const documentoSave: IGrupoDocumentoFlorestaSave = {
              nome: tipoDocumento.data.titulo,
              descricao: tipoDocumento.data.descricao,
              status: 'A',
              documento: {
                nome: tipoDocumento.data.titulo,
                descricao: tipoDocumento.data.descricao,
                descricao_en: tipoDocumento.data.descricao_en,
                extensao: '',
                tamanho_arquivo: arquivoSize,
                vencimento: formatDateToString(dataAtual),
                tipo_documento_id: String(tipoDocumento.data.id),
                status: 'A',
              },
              floresta_id: String(floresta.id),
            };

            formData.append('documento', JSON.stringify(documentoSave));
          }

          await FlorestaService.salvarDocumento(formData);
        } else {
          await FlorestaService.deleteDocumentoFloresta(String(floresta.id));
        }

        toastMsg(ToastType.Success, t('messages.updateSupplier'));
      } else {
        const newFloresta: IFloresta = {
          nome: florestaName,
          identificacao: identificacao,
          latitude: latitude,
          longitude: longitude,
          pais_origem: paisOrigemFloresta,
          status: 'A',
          fornecedor_id: idFornecedor ? Number(idFornecedor) : 0,
          materia_prima: materiaPrimaFinal || null,
        };

        const florestaSave: IFloresta = await FlorestaService.saveFloresta(newFloresta);

        if (files.length > 0) {
          const formData = new FormData();
          formData.append('id', 'N');
          let arquivoSize = '';

          files.forEach((file) => {
            if (file.file.type === 'application/pdf') {
              formData.append('file', file.file);
              arquivoSize = formatFileSize(file.file.size);
            } else {
              formData.append('zip_file', file.file);
            }
          });

          const tipoDocumento = await getTipoDocumento('33');

          const documentoSave: IGrupoDocumentoFlorestaSave = {
            nome: tipoDocumento.data.titulo,
            descricao: tipoDocumento.data.descricao,
            status: 'A',
            documento: {
              nome: tipoDocumento.data.titulo,
              descricao: tipoDocumento.data.descricao,
              descricao_en: tipoDocumento.data.descricao_en,
              extensao: '',
              tamanho_arquivo: arquivoSize,
              vencimento: formatDateToString(dataAtual),
              tipo_documento_id: String(tipoDocumento.data.id),
              status: 'A',
            },
            floresta_id: String(florestaSave.id),
          };

          formData.append('documento', JSON.stringify(documentoSave));

          await FlorestaService.salvarDocumento(formData);
        }

        toastMsg(ToastType.Success, t('messages.saveSupplier'));
      }

      atualziarFlorestas();
      closeModal(false);
      //setShowLoader(false);
    } catch (error: any) {
      // Verificar se é erro de upload de PDF
      const errorData = error?.response?.data;
      if (errorData && (errorData.isPdfError || errorData.showHelpButton)) {
        // Setar uploadError para exibir a mensagem de erro
        const uploadErrorObj = {
          message: errorData.message || error?.message || 'Erro no upload do PDF',
          showHelpButton: errorData.showHelpButton || false,
          isPdfError: errorData.isPdfError || false,
        };

        setUploadError(uploadErrorObj);

        // Salvar no localStorage para persistir entre recarregamentos
        localStorage.setItem('uploadError', JSON.stringify(uploadErrorObj));

        // NÃO fechar o modal para que o usuário veja a mensagem de erro
      } else {
        // Para outros tipos de erro, exibir toast normalmente
        toastMsg(ToastType.Error, error?.response?.data?.errors || error?.message || 'Erro desconhecido');
      }
    } finally {
      //atualziarFlorestas();
      //closeModal(false);
      setShowLoader(false);
    }
  }, [
    setShowLoader,
    getTipoDocumento,
    addMateriaPrimaria,
    setMateriaPrima,
    florestaName,
    identificacao,
    latitude,
    longitude,
    paisOrigemFloresta,
    files,
    dataAtual,
    history,
    documento,
    nomeMp,
    cientificoMp,
    setUploadError,
    materiaPrima,
    floresta,
    idFornecedor,
    car,
    t,
    atualziarFlorestas,
    closeModal,
  ]);

  const handleDeleteDocument = async (id: any): Promise<void> => {
    if (!id) {
      return;
    }

    try {
      setShowLoader(true);
      await FlorestaService.deleteFloresta(id);
      setShowLoader(false);
      toastMsg(ToastType.Success, t('response.deleteSuccess'));
      atualziarFlorestas();
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  };

  const handleChangeMateriaPrima = (index: number, event: React.ChangeEvent<HTMLInputElement>, tipo: string) => {
    const newMateriaPrima = [...materiaPrima];
    if (tipo === 'MP') {
      newMateriaPrima[index].nome = event.target.value;
      setMateriaPrima(newMateriaPrima);
    } else {
      newMateriaPrima[index].nome_cientifico = event.target.value;
      setMateriaPrima(newMateriaPrima);
    }
  };

  const downloadDoc = (url: string) => {
    window.open(url, '_blank');
  };

  const getDocumentNameLimited = (name: string): string => {
    if (name.length <= 45) {
      return name;
    }

    // Extract the file extension
    const lastDotIndex = name.lastIndexOf('.');
    const extension = lastDotIndex !== -1 ? name.substring(lastDotIndex) : '';

    // Calculate how much of the name we can keep
    const maxNameLength = 45 - extension.length;

    if (maxNameLength <= 0) {
      // If extension is too long, just truncate the whole name
      return name.substring(0, 45);
    }

    // Truncate name and append original extension
    return name.substring(0, maxNameLength) + extension;
  };

  const getDocumentName = (path: string): string => {
    const parts = path.split('/');
    if (parts.length > 0) {
      return getDocumentNameLimited(parts[parts.length - 1]);
    }
    return '';
  };

  return (
    <div id={floresta?.uuid}>
      {/* Modal de Ajuda em Tela Cheia */}
      {showHelpModal && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100vw',
            height: '100vh',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 9999,
          }}
          onClick={() => setShowHelpModal(false)}
        >
          <div
            style={{
              position: 'relative',
              backgroundColor: 'white',
              borderRadius: '15px',
              padding: '20px',
              boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
              maxWidth: '740px',
              maxHeight: '480px',
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Botão de fechar */}
            <button
              onClick={() => setShowHelpModal(false)}
              style={{
                position: 'absolute',
                top: '10px',
                right: '15px',
                background: 'none',
                border: 'none',
                fontSize: '24px',
                cursor: 'pointer',
                color: '#666',
                zIndex: 10000,
              }}
              title="Fechar"
            >
              ×
            </button>

            {/* Título */}
            <h4
              style={{
                textAlign: 'center',
                marginBottom: '20px',
                color: '#333',
                paddingRight: '30px',
              }}
            >
              {t('modal.importarCoordenadas')}
            </h4>

            {/* Iframe centralizado */}
            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <iframe
                width="700px"
                height="400px"
                src="https://www.youtube.com/embed/5qwbv5RQwD0?si=2sgJCunAWGt1a9TJ"
                title={t('modal.importarCoordenadas')}
                referrerPolicy="unsafe-url"
                allowFullScreen={true}
                allow="clipboard-write"
                sandbox="allow-popups allow-popups-to-escape-sandbox allow-scripts allow-forms allow-same-origin allow-presentation"
                style={{ borderRadius: '10px', border: 'none' }}
              />
            </div>
          </div>
        </div>
      )}

      <Row style={{ marginLeft: 'auto' }}>
        {/* Cabeçalho com botão de ajuda */}
        <Col md={12} className="mt-2 mb-3">
          <div className="d-flex align-items-center justify-content-between">
            <h5 className="mb-0">{floresta ? t('titles.editFloresta') : t('titles.novaFloresta')}</h5>
            <Button
              variant="link"
              onClick={() => setShowHelpModal(true)}
              className="p-0 d-flex align-items-center"
              title={t('modal.ajudaCoordenadas')}
            >
              <Image src={InfoIcon} height={20} className="me-1" />
              <span style={{ fontSize: '14px', color: '#87949A' }}>{t('modal.linkHelp')}</span>
            </Button>
          </div>
        </Col>

        <Col md={12} className="mt-2">
          <CustomInput
            cy={'test-name'}
            id={'name'}
            name={'name'}
            label={`${t('table.name')}*`}
            placeholder={t('labels.nomeFloresta')}
            value={florestaName}
            onChange={(e) => {
              setFlorestaName?.(e.target.value);
            }}
            type="text"
            maxLength={255}
          />
        </Col>
        <Col md={6} className="mt-2">
          <CustomInput
            cy={'test-identificacao'}
            id={'identificacao'}
            name={'identificacao'}
            label={`${t('labels.identificacao')}*`}
            placeholder={t('labels.identificacao')}
            value={identificacao}
            onChange={(e) => {
              setIdentificacao?.(e.target.value);
            }}
            type="text"
            maxLength={255}
          />
        </Col>
        <Col md={6} className="mt-2">
          <CustomInput
            cy={'test-pais-origem'}
            id={'paisOrigem'}
            name={'paisOrigem'}
            label={`${t('labels.paisOrigemFloresta')}*`}
            placeholder={t('labels.paisOrigemFloresta')}
            value={paisOrigemFloresta}
            onChange={(e) => {
              setPaisOrigemFloresta?.(e.target.value);
            }}
            type="text"
            maxLength={100}
          />
        </Col>

        <Col md={6} className="mt-2">
          <CoordinateInput
            cy={'test-latitude'}
            id={'latitude'}
            label={t('labels.latitude')}
            placeholder="Ex: 27°59'05,59&quot; S"
            value={latitude}
            onChange={handleLatitudeChange}
            onBlur={handleLatitudeBlur}
            type="latitude"
            maxLength={20}
            isInvalid={latitudeError}
            msg={latitudeError ? t('messages.errorInvalidLatitude') : ''}
          />
        </Col>
        <Col md={6} className="mt-2">
          <CoordinateInput
            cy={'test-longitude'}
            id={'longitude'}
            label={t('labels.longitude')}
            placeholder="Ex: 46°37'59,90&quot; O"
            value={longitude}
            onChange={handleLongitudeChange}
            onBlur={handleLongitudeBlur}
            type="longitude"
            maxLength={20}
            isInvalid={longitudeError}
            msg={longitudeError ? t('messages.errorInvalidLongitude') : ''}
          />
        </Col>

        <Col md={5} className="mt-2">
          <CustomInput
            cy={'test-materiaPrima'}
            id={'materiaPrima'}
            name={'materiaPrima'}
            label={`${t('titles.materiaPrima')}*`}
            placeholder={t('labels.nomeMateriaPrima')}
            value={nomeMp}
            onChange={(e) => {
              setNomeMp?.(e.target.value);
            }}
            type="text"
            maxLength={255}
          />
        </Col>
        <Col md={5} className="mt-2">
          <CustomInput
            cy={'test-cientifico'}
            id={'cientifico'}
            name={'cientifico'}
            label={`${t('labels.cientifico')}*`}
            placeholder={t('labels.cientifico')}
            value={cientificoMp}
            onChange={(e) => {
              setCientificoMp?.(e.target.value);
            }}
            type="text"
            maxLength={255}
          />
        </Col>
        <Col md={2} className="mt-2">
          <div style={{ marginTop: '27px', marginLeft: '10px' }}>
            <Button variant="outline-secondary" onClick={addMateriaPrimaria}>
              {t('buttons.add')}
            </Button>
          </div>
        </Col>

        {materiaPrima?.map((mp, index) => (
          <>
            <Col md={5} className="mt-2">
              <CustomInput
                cy={'test-materiaPrimaD' + mp.id}
                id={'materiaPrimaD' + mp.id}
                name={'materiaPrimaD' + mp.id}
                value={mp.nome}
                type="text"
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChangeMateriaPrima(index, e, 'MP')}
              />
            </Col>
            <Col md={5} className="mt-2">
              <CustomInput
                cy={'test-cientificoD' + mp.id}
                id={'cientificoD' + mp.id}
                name={'cientificoD' + mp.id}
                value={mp.nome_cientifico}
                type="text"
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChangeMateriaPrima(index, e, 'NC')}
              />
            </Col>
            <Col md={2} className="mt-2">
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-evenly',
                  flexWrap: 'wrap',
                  alignContent: 'flex-end',
                  width: '80%',
                  height: '100%',
                  paddingBottom: '10px',
                }}
              >
                <div
                  onClick={removeMateriaPrima(index)}
                  role="presentation"
                  title={'Remover Matéria Prima'}
                  style={{ cursor: 'pointer' }}
                >
                  <Image src={InactiveUser} height={20} />
                </div>
              </div>
            </Col>
          </>
        ))}

        <Col md={12}>
          <Card className="w-full max-w-4xl mx-auto">
            <Modal
              show={show.show}
              handleClose={() => setShow({ ...show, show: false })}
              title={t('modal.removeFlorestaTitle')}
              size="lg"
              className="styleModalConfirm"
            >
              <Container>
                <Row className="p-2">
                  <Col className="d-flex align-items-center ">
                    <h6>
                      <br />
                      {t('modal.removeFlorestaSubTitle') + show.titulo + '?'}
                      <br />
                      {t('modal.removeProductDescription')}
                    </h6>
                  </Col>
                </Row>
              </Container>

              <Row className="mt-4">
                <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
                  <CustomButton
                    cy="btn-cancel"
                    type="button"
                    variant="outline-green"
                    onClick={() => {
                      setShow(false);
                    }}
                  >
                    {t('buttons.cancel')}
                  </CustomButton>
                  <CustomButton
                    cy="btn-save"
                    type="button"
                    variant="danger"
                    onClick={() => handleDeleteDocument(show.idDoc)}
                  >
                    {t('buttons.delete')}
                  </CustomButton>
                </Col>
              </Row>
            </Modal>

            <CardContent className="space-y-6">
              {files.length <= 1 && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <label className="text-sm">
                      {t('labels.selectPdfAndZip')} <b>{'CAR - Cadastro Ambiental Rural'}</b>
                      <br />
                    </label>
                  </div>

                  <div
                    className="border-2 border-dashed rounded-lg p-8 text-center"
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                  >
                    <div className="flex flex-col items-center gap-2">
                      <Upload className="h-10 w-10 text-muted-foreground" />
                      <p>{t('labels.selecioneArquivoPdfZip')}</p>
                      <p className="text-sm text-muted-foreground">ZIP e PDF. {t('exceptions.maxSizeDocment')}</p>
                      <Button
                        onClick={() => document.getElementById(`file-upload${floresta?.uuid}`)?.click()}
                        variant="outline-success"
                        className="mt-2"
                      >
                        {t('buttons.selectFile')}
                      </Button>
                      <input
                        id={'file-upload' + floresta?.uuid}
                        type="file"
                        className="hidden"
                        onChange={handleFileSelect}
                        accept=".zip,.pdf"
                        multiple
                      />
                    </div>
                  </div>
                </div>
              )}

              {files.length > 0 && (
                <div className="space-y-4">
                  <h3 className="font-bold text-lg">{t('labels.labelArquivoAdicionado')}</h3>
                  <div className="border rounded-lg">
                    <div
                      className="grid grid-cols-12 border-b bg-muted/50"
                      style={{ paddingTop: '10px', paddingBottom: '10px' }}
                    >
                      <div className="col-span-1" style={{ textAlign: 'center' }}>
                        {t('table.type')}
                      </div>
                      <div className="col-span-6">{t('table.name')}</div>
                      <div className="col-span-2">{t('table.size')}</div>
                      <div className="col-span-2">{t('table.vencimento')}</div>
                      <div className="col-span-1">{t('modal.modalTitleActions')}</div>
                    </div>
                    {files.map((file, index) => (
                      <div key={index} className="grid grid-cols-12 items-center">
                        <div className="col-span-1">
                          <FileIcon className="h-6 w-6" />
                        </div>
                        <div className="col-span-6 flex items-center gap-2">
                          <Button variant="link" onClick={() => downloadDoc(file.path || '')}>
                            {file.path ? getDocumentName(file.path || '') : getDocumentNameLimited(file.file.name)}
                          </Button>
                        </div>
                        <div className="col-span-2">
                          {documento?.tamanho_arquivo ? documento.tamanho_arquivo : formatFileSize(file.file.size)}
                        </div>
                        <div className="col-span-2" style={{ paddingTop: '10px' }}>
                          <DatePicker
                            onChange={(start) => {
                              setDataAtual(start);
                            }}
                            selected={dataAtual}
                            className="dt-picker"
                          />
                        </div>
                        <div className="col-span-1">
                          <Button variant="ghost" title={t('titles.removeDocument')} onClick={() => removeFile(index)}>
                            <Image src={InactiveUser} height={20} />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="d-flex justify-content-end">
                <Button variant="outline-secondary" onClick={handleSubmit}>
                  {t('buttons.onlySave')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default CardInfoFloresta;
