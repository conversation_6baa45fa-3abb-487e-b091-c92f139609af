import { t } from 'i18next';
import { formattedValue } from './convertNumber';

interface DimensionsState {
  list: Array<number | string>;
  range: { min: string; max: string };
  unique: string;
}

export const updateDimensions = (
  dimensionsUpdate: Array<{ type: string; min: string; max: string; value: number | string }> | any,
  setDimensions: React.Dispatch<React.SetStateAction<DimensionsState>> | any,
  setType: React.Dispatch<React.SetStateAction<DimensionsState>> | any
): void => {
  const listLength = dimensionsUpdate.filter((typ: any) => typ.type === 'list').length;
  const rangeLength = dimensionsUpdate.filter((typ: any) => typ.type === 'range').length;
  const uniqueLength = dimensionsUpdate.filter((typ: any) => typ.type === 'unique').length;

  const updatedList = listLength ? (dimensionsUpdate.filter((typ: any) => typ.type === 'list') as any)[0].value : [];

  const updatedRange =
    rangeLength && (dimensionsUpdate.filter((typ: any) => typ.type === 'range') as any)[0]
      ? {
        min: formattedValue((dimensionsUpdate.filter((typ: any) => typ.type === 'range') as any)[0].min),
        max: formattedValue((dimensionsUpdate.filter((typ: any) => typ.type === 'range') as any)[0].max),
      }
      : { min: '', max: '' };
  const updatedUnique = uniqueLength
    ? formattedValue((dimensionsUpdate.filter((typ: any) => typ.type === 'unique') as any)[0].value)
    : '';

  let type = { value: '', label: '' };

  switch (dimensionsUpdate[0]?.type) {
    case 'list':
      type = {
        value: 'list',
        label: t('language') === 'USA' ? 'List' : 'Lista',
      };
      break;
    case 'range':
      type = {
        value: 'range',
        label: t('language') === 'USA' ? 'Range' : 'Intervalo',
      };
      break;
    case 'unique':
      type = {
        value: 'unique',
        label: t('language') === 'USA' ? 'Unique' : 'Único',
      };
      break;
    default:
      break;
  }

  if (dimensionsUpdate.length > 0) {
    setType(type);
  } else {
    setType(null);
  }

  const listCommma = updatedList?.map((element: any) => formattedValue(element)) || [];

  setDimensions({
    list: listCommma,
    range: updatedRange,
    unique: updatedUnique,
  });
};
