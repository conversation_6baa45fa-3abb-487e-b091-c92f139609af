.radioMain {
  p {
    font-weight: 400;
    font-size: 16px;
    line-height: 23px;
    display: flex;
    align-items: center;
    color: #87949a;
  }

  .text-radio {
    display: flex;
    gap: 0.438rem;
    justify-content: flex-start;
    margin-top: 0.4rem;
    font-family: 'Poppins';
    font-size: 14px;

    input[type='radio'] {
      margin-top: 0.2rem;
      position: relative;
      font-size: 10px;
      cursor: pointer;
      display: flex;
      align-items: flex-start;
      height: 16px;
      color: rgb(0, 0, 0);
      width: 1rem;
    }

    input[type='radio']::before {
      content: ' ';
      display: inline-block;
      vertical-align: middle;
      min-height: 16px;
      min-width: 16px;
      // background-color: #ffffff;
      border-width: 1px;
      border-style: solid;
      border-radius: 50%;
      box-shadow: none;
    }

    input[type='radio']:checked::before {
      content: ' ';
      display: inline-block;
      vertical-align: middle;
      min-height: 16px;
      min-width: 16px;
      background-image: url('../../statics/input-checkbox.svg');
      background-repeat: no-repeat;
      background-size: 100%;
      background-color: transparent;
      border-width: 1px;
      border-style: solid;
      border-color: #18b680;
      border-radius: 50%;
      box-shadow: none;
    }
  }
}
