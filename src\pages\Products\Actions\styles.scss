.genericMessage {
  font-family: '<PERSON>uni<PERSON>';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 23px;
  display: flex;
  align-items: center;
  color: #54666f;
}

.productsLimit {
  max-width: 96rem;
  padding-left: 8rem;
  padding-right: 8rem;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    padding-top: 1rem;
    padding-left: 0;
    padding-right: 0;
  }

  .btnCreate {
    min-height: 3rem;
    border-radius: 8px;
    margin-right: 1rem;
  }

  .btnRemove {
    min-height: 3rem;
    border-radius: 8px;
    margin-left: 1rem;
  }

  .btnReturn {
    min-height: 3rem;
    border-radius: 8px;
    margin-right: 0.7rem;
    border-color: #18b680;
    color: #18b680;
  }

  .inputs .form-control {
    line-height: 0;
  }
}

.style {
  margin-top: 2rem;
  padding-left: 2rem;

  .modal-header {
    background-color: var(--green-500);

    .modal-title {
      color: var(--white-100);
    }
  }
}

small {
  color: #54666f;
  font-size: 14px;
  line-height: 23px;
}

.rti--container {
  line-height: 1.2 !important;
}

.error-message {
  color: red;
}

.imageAlign {
  position: relative;
  width: 100%;
  max-width: 240px;

  img {
    max-width: 240px !important;
    max-height: 200px !important;
    min-width: 240px !important;
    min-height: 200px !important;
    border-radius: 8px;

    @media (max-width: 768px) {
      max-width: 200px !important;
    }
  }

  &__btn {
    position: absolute;
    top: 10%;
    right: 1%;
    transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    padding: 16px 18px;
    cursor: pointer;
    background-color: #e77a7a;
    border-radius: 24px;
    background-image: url('../../../statics/delete.svg');
    background-size: 18px;
    background-repeat: no-repeat;
    background-position: center;
  }

  &:hover {
    opacity: 0.7;
  }
}

.add__photo {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 240px !important;
  max-height: 200px !important;
  border: 1px dashed #bac5c5;
  border-radius: 0.75rem;
  color: #12263f;
  font-weight: 600;

  cursor: pointer;

  &__content {
    position: absolute;
    display: flex;
  }

  &::after {
    content: '';
    top: 0;
    left: 0;
    padding-bottom: 100%;
  }
}

.wrapper {
  background-color: #ffffff;
  border: 1px solid #bac5c5;
  border-radius: 8px;
  padding-top: 10px;
  padding-left: 10px;
  min-height: 220px !important;

  &__photos__wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;

    @media (max-width: 1440px) {
      grid-template-columns: 1fr 1fr 1fr;
    }

    @media (max-width: 1024px) {
      grid-template-columns: 1fr 1fr;
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
}