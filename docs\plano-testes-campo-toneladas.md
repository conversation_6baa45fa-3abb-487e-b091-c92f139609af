# Plano de Testes - Campo "Total em Toneladas"

## Visão Geral

Este documento define a estratégia completa de testes para a funcionalidade de campos de quantidade em toneladas nas telas de Cadeia de Fornecimento e Espécies de Madeira Utilizada do sistema WoodFlow.

## Objetivos dos Testes

- ✅ Garantir funcionamento correto dos novos campos de toneladas
- ✅ Validar integração com campos existentes de m³
- ✅ Verificar validações e regras de negócio
- ✅ Assegurar compatibilidade com funcionalidades existentes
- ✅ Confirmar experiência do usuário adequada

## Escopo dos Testes

### Funcionalidades Incluídas

- Campos de quantidade em toneladas para produtos
- Campos de quantidade em toneladas para matéria-prima
- Validações de formulário
- Conversões automáticas (se implementadas)
- Integração com APIs
- Persistência de dados

### Funcionalidades Excluídas

- Relatórios (será testado em fase posterior)
- Importação/exportação de dados
- Funcionalidades não relacionadas aos novos campos

## Estratégia de Testes

### Pirâmide de Testes

```
    /\
   /  \    E2E Tests (10%)
  /____\
 /      \   Integration Tests (20%)
/________\
           Unit Tests (70%)
```

## 1. Testes Unitários (70%)

### 1.1 Utilitários de Conversão

**Arquivo: `src/utils/__tests__/woodConversion.test.ts`**

#### Cenários de Teste:

| ID   | Cenário                                        | Entrada              | Saída Esperada | Prioridade |
| ---- | ---------------------------------------------- | -------------------- | -------------- | ---------- |
| UC01 | Conversão m³ para toneladas - densidade padrão | '1,000'              | '0,500'        | Alta       |
| UC02 | Conversão m³ para toneladas - eucalipto        | '2,000', 'eucalipto' | '1,200'        | Alta       |
| UC03 | Conversão toneladas para m³ - densidade padrão | '0,500'              | '1,000'        | Alta       |
| UC04 | Entrada inválida - string vazia                | ''                   | ''             | Média      |
| UC05 | Entrada inválida - texto                       | 'invalid'            | ''             | Média      |
| UC06 | Validação de consistência - valores corretos   | '1,000', '0,500'     | true           | Alta       |
| UC07 | Validação de consistência - valores incorretos | '1,000', '0,800'     | false          | Alta       |
| UC08 | Validação com campos vazios                    | '', '0,500'          | true           | Média      |

### 1.2 Hook de Quantidades

**Arquivo: `src/hooks/__tests__/useQuantityFields.test.ts`**

#### Cenários de Teste:

| ID   | Cenário                      | Ação                           | Resultado Esperado   | Prioridade |
| ---- | ---------------------------- | ------------------------------ | -------------------- | ---------- |
| UH01 | Inicialização com valores    | Passar valores iniciais        | Estados corretos     | Alta       |
| UH02 | Mudança em m³                | Alterar quantityM3             | Callback executado   | Alta       |
| UH03 | Mudança em toneladas         | Alterar quantityTons           | Callback executado   | Alta       |
| UH04 | Conversão automática m³→tons | autoConvert=true, alterar m³   | Toneladas calculadas | Média      |
| UH05 | Conversão automática tons→m³ | autoConvert=true, alterar tons | M³ calculados        | Média      |
| UH06 | Reset de valores             | Chamar reset()                 | Todos campos limpos  | Baixa      |

### 1.3 Componente QuantityInput

**Arquivo: `src/components/Input/__tests__/QuantityInput.test.tsx`**

#### Cenários de Teste:

| ID   | Cenário             | Ação                  | Resultado Esperado | Prioridade |
| ---- | ------------------- | --------------------- | ------------------ | ---------- |
| UC01 | Renderização básica | Renderizar componente | Elementos visíveis | Alta       |
| UC02 | Label exibida       | Passar prop label     | Label renderizada  | Alta       |
| UC03 | Valor inicial       | Passar prop value     | Input com valor    | Alta       |
| UC04 | Mudança de valor    | Digitar no input      | onChange chamado   | Alta       |
| UC05 | Máscara aplicada    | Digitar números       | Formato correto    | Alta       |
| UC06 | Campo desabilitado  | disabled=true         | Input desabilitado | Média      |
| UC07 | Placeholder         | Passar placeholder    | Texto exibido      | Baixa      |

## 2. Testes de Integração (20%)

### 2.1 Tela de Produtos - Supply Chain

**Arquivo: `src/pages/SupplyChain/New/Produto/__tests__/index.test.tsx`**

#### Cenários de Teste:

| ID   | Cenário                 | Pré-condição      | Ação                      | Resultado Esperado        | Prioridade |
| ---- | ----------------------- | ----------------- | ------------------------- | ------------------------- | ---------- |
| IP01 | Renderização com campos | Tela carregada    | -                         | Campos m³ e tons visíveis | Alta       |
| IP02 | Salvamento apenas m³    | Produto novo      | Preencher só m³, salvar   | Produto salvo com m³      | Alta       |
| IP03 | Salvamento apenas tons  | Produto novo      | Preencher só tons, salvar | Produto salvo com tons    | Alta       |
| IP04 | Salvamento ambos campos | Produto novo      | Preencher ambos, salvar   | Produto salvo com ambos   | Alta       |
| IP05 | Validação campos vazios | Produto novo      | Salvar sem preencher      | Erro de validação         | Alta       |
| IP06 | Edição de produto       | Produto existente | Alterar quantidades       | Produto atualizado        | Alta       |
| IP07 | Carregamento de dados   | Produto com dados | Abrir edição              | Campos preenchidos        | Média      |

### 2.2 Tela de Espécies de Madeira

**Arquivo: `src/pages/SupplyChain/New/Specie/__tests__/index.test.tsx`**

#### Cenários de Teste:

| ID   | Cenário                   | Pré-condição         | Ação               | Resultado Esperado     | Prioridade |
| ---- | ------------------------- | -------------------- | ------------------ | ---------------------- | ---------- |
| IS01 | Listagem matérias-prima   | Floresta selecionada | -                  | Lista com campos tons  | Alta       |
| IS02 | Preenchimento quantidades | MP listadas          | Preencher tons     | Valores aceitos        | Alta       |
| IS03 | Validação por MP          | MP sem quantidade    | Tentar salvar      | Erro específico por MP | Alta       |
| IS04 | Salvamento múltiplas MP   | Várias MP            | Preencher e salvar | Todas salvas           | Alta       |
| IS05 | Edição quantidades        | MP com dados         | Alterar valores    | Valores atualizados    | Média      |

### 2.3 Serviços de API

**Arquivo: `src/services/__tests__/supplyChain.service.test.ts`**

#### Cenários de Teste:

| ID   | Cenário                | Mock API | Entrada          | Resultado Esperado | Prioridade |
| ---- | ---------------------- | -------- | ---------------- | ------------------ | ---------- |
| SA01 | Criar produto com tons | POST 200 | Dados com tons   | Produto criado     | Alta       |
| SA02 | Atualizar produto      | PUT 200  | Novos dados tons | Produto atualizado | Alta       |
| SA03 | Erro de validação      | POST 422 | Dados inválidos  | Erro capturado     | Alta       |
| SA04 | Falha de rede          | POST 500 | Qualquer dado    | Erro tratado       | Média      |

## 3. Testes End-to-End (10%)

### 3.1 Fluxo Completo - Cadastro de Produto

**Cenário: Usuário cadastra produto com quantidade em toneladas**

#### Pré-condições:

- Usuário logado no sistema
- Acesso à tela de Nova Cadeia de Fornecimento
- Dados de importador configurados

#### Passos:

1. Navegar para "Nova Cadeia de Fornecimento"
2. Preencher dados básicos (importador, pedido, etc.)
3. Ir para aba "Produtos"
4. Preencher descrição do produto
5. Preencher apenas campo "Quantidade (toneladas)" com "2,500"
6. Clicar em "Salvar Produto"
7. Verificar produto salvo na lista

#### Resultado Esperado:

- Produto aparece na lista com quantidade em toneladas
- Dados persistidos corretamente no banco
- Nenhum erro exibido

### 3.2 Fluxo Completo - Espécies de Madeira

**Cenário: Usuário cadastra matéria-prima com ambas as unidades**

#### Pré-condições:

- Produto cadastrado na cadeia
- Fornecedor com floresta cadastrada
- Matérias-prima disponíveis na floresta

#### Passos:

1. Clicar em "Adicionar Espécie de Madeira"
2. Selecionar fornecedor e floresta
3. Para primeira matéria-prima:
   - Preencher "Quantidade (m³)" com "1,000"
   - Preencher "Quantidade (toneladas)" com "0,600"
4. Para segunda matéria-prima:
   - Preencher apenas "Quantidade (toneladas)" com "0,800"
5. Clicar em "Salvar"
6. Verificar dados salvos

#### Resultado Esperado:

- Ambas matérias-prima salvas com quantidades corretas
- Validação de consistência (se implementada) funcionando
- Dados exibidos corretamente na listagem

## 4. Testes de Validação

### 4.1 Validações de Formulário

| ID   | Campo           | Valor        | Resultado Esperado                            | Prioridade |
| ---- | --------------- | ------------ | --------------------------------------------- | ---------- |
| VF01 | Quantidade m³   | Vazio        | Aceito se tons preenchido                     | Alta       |
| VF02 | Quantidade tons | Vazio        | Aceito se m³ preenchido                       | Alta       |
| VF03 | Ambos campos    | Vazios       | Erro: "Pelo menos uma quantidade obrigatória" | Alta       |
| VF04 | Quantidade m³   | "abc"        | Erro: formato inválido                        | Média      |
| VF05 | Quantidade tons | "-1,000"     | Erro: valor negativo                          | Média      |
| VF06 | Quantidade m³   | "999999,999" | Aceito (dentro do limite)                     | Baixa      |

### 4.2 Validações de Consistência (Opcional)

| ID   | M³      | Toneladas | Espécie   | Resultado            | Prioridade |
| ---- | ------- | --------- | --------- | -------------------- | ---------- |
| VC01 | "1,000" | "0,500"   | default   | Válido               | Média      |
| VC02 | "1,000" | "0,600"   | eucalipto | Válido               | Média      |
| VC03 | "1,000" | "1,000"   | default   | Aviso inconsistência | Baixa      |
| VC04 | "2,000" | "0,500"   | default   | Aviso inconsistência | Baixa      |

## 5. Testes de Regressão

### 5.1 Funcionalidades Existentes

| ID   | Funcionalidade           | Teste            | Status Esperado         | Prioridade |
| ---- | ------------------------ | ---------------- | ----------------------- | ---------- |
| TR01 | Cadastro produto só m³   | Fluxo original   | Funcionando             | Alta       |
| TR02 | Listagem produtos        | Visualizar lista | Todos produtos visíveis | Alta       |
| TR03 | Edição produto existente | Alterar dados    | Salvamento correto      | Alta       |
| TR04 | Relatórios existentes    | Gerar relatório  | Dados corretos          | Média      |
| TR05 | Exportação dados         | Exportar CSV/PDF | Arquivo gerado          | Baixa      |

## 6. Testes de Performance

### 6.1 Métricas de Performance

| Métrica                  | Valor Atual | Meta    | Método de Medição |
| ------------------------ | ----------- | ------- | ----------------- |
| Tempo carregamento tela  | < 2s        | < 2.5s  | Chrome DevTools   |
| Tempo salvamento produto | < 1s        | < 1.5s  | Network tab       |
| Uso memória              | Baseline    | +5% máx | Performance tab   |
| Bundle size              | Baseline    | +2% máx | Webpack analyzer  |

## 7. Testes de Usabilidade

### 7.1 Cenários de Uso

| ID   | Cenário                      | Critério de Sucesso        | Método        |
| ---- | ---------------------------- | -------------------------- | ------------- |
| TU01 | Usuário entende novos campos | 90% completam tarefa       | Teste usuário |
| TU02 | Campos visualmente claros    | Sem confusão m³/tons       | Observação    |
| TU03 | Validações compreensíveis    | Mensagens claras           | Feedback      |
| TU04 | Fluxo intuitivo              | Tempo < 5min para cadastro | Cronometragem |

## 8. Ambiente de Testes

### 8.1 Configuração

**Frontend:**

- Node.js 14+
- React Testing Library
- Jest
- MSW (Mock Service Worker)

**Comandos:**

```bash
# Testes unitários
npm test

# Testes com coverage
npm test -- --coverage

# Testes específicos
npm test -- --testPathPattern=woodConversion

# Testes em modo watch
npm test -- --watch
```

### 8.2 Dados de Teste

**Produtos de Teste:**

```json
{
  "product_description": "Produto Teste Toneladas",
  "product_quantity": "1,500",
  "product_quantity_tons": "0,750",
  "status": "A",
  "supply_chain_id": "123"
}
```

**Matérias-Prima de Teste:**

```json
{
  "nome": "Eucalipto Teste",
  "nome_cientifico": "Eucalyptus grandis",
  "quantidade": "2,000",
  "quantidade_tons": "1,200",
  "status": "A"
}
```

## 9. Critérios de Aceitação

### 9.1 Critérios Funcionais

- ✅ Todos os testes unitários passando (100%)
- ✅ Todos os testes de integração passando (100%)
- ✅ Testes E2E principais passando (100%)
- ✅ Cobertura de código > 80%
- ✅ Nenhuma regressão identificada

### 9.2 Critérios Não-Funcionais

- ✅ Performance dentro das metas
- ✅ Acessibilidade mantida
- ✅ Responsividade em dispositivos móveis
- ✅ Compatibilidade com navegadores suportados

## 10. Cronograma de Execução

| Fase      | Atividade          | Duração     | Responsável  |
| --------- | ------------------ | ----------- | ------------ |
| 1         | Testes Unitários   | 3 dias      | Dev Frontend |
| 2         | Testes Integração  | 2 dias      | Dev Frontend |
| 3         | Testes E2E         | 2 dias      | QA           |
| 4         | Testes Regressão   | 1 dia       | QA           |
| 5         | Testes Performance | 1 dia       | QA           |
| 6         | Correções          | 2 dias      | Dev Team     |
| **Total** |                    | **11 dias** |              |

## 11. Relatórios e Métricas

### 11.1 Métricas de Qualidade

- **Cobertura de Código**: Meta > 80%
- **Bugs Encontrados**: Classificação por severidade
- **Tempo de Execução**: Todos os testes < 5 minutos
- **Taxa de Sucesso**: Meta > 95%

### 11.2 Relatório de Execução

**Template de Relatório:**

```
# Relatório de Testes - Campo Toneladas
Data: [DATA]
Versão: [VERSÃO]

## Resumo Executivo
- Testes Executados: X/Y
- Taxa de Sucesso: X%
- Bugs Críticos: X
- Bugs Menores: X

## Detalhamento por Categoria
[Detalhes por tipo de teste]

## Recomendações
[Ações necessárias]
```

## 12. Riscos e Mitigações

| Risco                     | Probabilidade | Impacto | Mitigação                    |
| ------------------------- | ------------- | ------- | ---------------------------- |
| Falha validação dados     | Média         | Alto    | Testes extensivos validação  |
| Regressão funcionalidades | Baixa         | Alto    | Suite completa regressão     |
| Performance degradada     | Baixa         | Médio   | Testes performance contínuos |
| Inconsistência dados      | Média         | Médio   | Validação cruzada m³/tons    |

## 13. Implementação dos Testes

### 13.1 Exemplo de Teste Unitário Completo

**Arquivo: `src/utils/__tests__/woodConversion.test.ts`**

```typescript
import { convertM3ToTons, convertTonsToM3, validateQuantityConsistency } from '../woodConversion';

describe('woodConversion', () => {
  describe('convertM3ToTons', () => {
    it('should convert m³ to tons with default density', () => {
      expect(convertM3ToTons('1,000')).toBe('0,500');
      expect(convertM3ToTons('2,000')).toBe('1,000');
    });

    it('should convert m³ to tons with eucalipto density', () => {
      expect(convertM3ToTons('1,000', 'eucalipto')).toBe('0,600');
      expect(convertM3ToTons('2,500', 'eucalipto')).toBe('1,500');
    });

    it('should handle empty input', () => {
      expect(convertM3ToTons('')).toBe('');
      expect(convertM3ToTons(null)).toBe('');
      expect(convertM3ToTons(undefined)).toBe('');
    });

    it('should handle invalid input', () => {
      expect(convertM3ToTons('invalid')).toBe('');
      expect(convertM3ToTons('abc123')).toBe('');
      expect(convertM3ToTons('12.34.56')).toBe('');
    });

    it('should handle zero values', () => {
      expect(convertM3ToTons('0,000')).toBe('0,000');
      expect(convertM3ToTons('0')).toBe('0,000');
    });
  });

  describe('convertTonsToM3', () => {
    it('should convert tons to m³ with default density', () => {
      expect(convertTonsToM3('0,500')).toBe('1,000');
      expect(convertTonsToM3('1,000')).toBe('2,000');
    });

    it('should convert tons to m³ with specific species', () => {
      expect(convertTonsToM3('0,600', 'eucalipto')).toBe('1,000');
      expect(convertTonsToM3('1,350', 'pinus')).toBe('3,000');
    });
  });

  describe('validateQuantityConsistency', () => {
    it('should validate consistent quantities', () => {
      expect(validateQuantityConsistency('1,000', '0,500')).toBe(true);
      expect(validateQuantityConsistency('2,000', '1,000', 'default')).toBe(true);
    });

    it('should detect inconsistent quantities', () => {
      expect(validateQuantityConsistency('1,000', '0,800')).toBe(false);
      expect(validateQuantityConsistency('1,000', '0,200')).toBe(false);
    });

    it('should return true for empty values', () => {
      expect(validateQuantityConsistency('', '0,500')).toBe(true);
      expect(validateQuantityConsistency('1,000', '')).toBe(true);
      expect(validateQuantityConsistency('', '')).toBe(true);
    });

    it('should handle tolerance parameter', () => {
      expect(validateQuantityConsistency('1,000', '0,550', 'default', 0.15)).toBe(true);
      expect(validateQuantityConsistency('1,000', '0,550', 'default', 0.05)).toBe(false);
    });
  });
});
```

### 13.2 Exemplo de Teste de Componente

**Arquivo: `src/components/Input/__tests__/QuantityInput.test.tsx`**

```typescript
import React from 'react';
import { render, fireEvent, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../translate/i18n';
import QuantityInput from '../QuantityInput';

const renderWithI18n = (component: React.ReactElement) => {
  return render(<I18nextProvider i18n={i18n}>{component}</I18nextProvider>);
};

describe('QuantityInput', () => {
  const defaultProps = {
    label: 'Test Quantity',
    value: '',
    onChange: jest.fn(),
    cy: 'test-quantity',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    renderWithI18n(<QuantityInput {...defaultProps} />);

    expect(screen.getByLabelText('Test Quantity')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('should display initial value', () => {
    renderWithI18n(<QuantityInput {...defaultProps} value="1,500" />);

    expect(screen.getByDisplayValue('1,500')).toBeInTheDocument();
  });

  it('should call onChange when value changes', async () => {
    const user = userEvent.setup();
    const mockOnChange = jest.fn();

    renderWithI18n(<QuantityInput {...defaultProps} onChange={mockOnChange} />);

    const input = screen.getByRole('textbox');
    await user.type(input, '1000');

    expect(mockOnChange).toHaveBeenCalled();
  });

  it('should apply mask correctly', async () => {
    const user = userEvent.setup();
    const mockOnChange = jest.fn();

    renderWithI18n(<QuantityInput {...defaultProps} onChange={mockOnChange} />);

    const input = screen.getByRole('textbox');
    await user.type(input, '1234567');

    // Verificar se a máscara foi aplicada (formato: 1.234,567)
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenLastCalledWith('1.234,567');
    });
  });

  it('should be disabled when disabled prop is true', () => {
    renderWithI18n(<QuantityInput {...defaultProps} disabled />);

    expect(screen.getByRole('textbox')).toBeDisabled();
  });

  it('should show placeholder', () => {
    renderWithI18n(<QuantityInput {...defaultProps} placeholder="Digite a quantidade" />);

    expect(screen.getByPlaceholderText('Digite a quantidade')).toBeInTheDocument();
  });

  it('should handle different column sizes', () => {
    const { container } = renderWithI18n(<QuantityInput {...defaultProps} colSize={6} />);

    expect(container.querySelector('.col-md-6')).toBeInTheDocument();
  });
});
```

### 13.3 Exemplo de Teste de Integração

**Arquivo: `src/pages/SupplyChain/New/Produto/__tests__/index.test.tsx`**

```typescript
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../../translate/i18n';
import ProdutoPage from '../index';
import * as SupplyChainService from '../../../../services/supplyChain.service';

// Mock do serviço
jest.mock('../../../../services/supplyChain.service');
const mockSupplyChainService = SupplyChainService as jest.Mocked<typeof SupplyChainService>;

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <I18nextProvider i18n={i18n}>{component}</I18nextProvider>
    </BrowserRouter>
  );
};

describe('ProdutoPage', () => {
  const mockSupplyChain = {
    id: 1,
    supply_chain_id: 'test-123',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockSupplyChainService.saveProductSupplyChain.mockResolvedValue({
      id: 1,
      product_description: 'Test Product',
      product_quantity: '1,500',
      product_quantity_tons: '0,750',
    });
  });

  it('should render quantity fields', () => {
    renderWithProviders(<ProdutoPage supplyChain={mockSupplyChain} />);

    expect(screen.getByLabelText(/quantidade.*m³/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/quantidade.*toneladas/i)).toBeInTheDocument();
  });

  it('should save product with only m³ quantity', async () => {
    const user = userEvent.setup();

    renderWithProviders(<ProdutoPage supplyChain={mockSupplyChain} />);

    // Preencher descrição
    await user.type(screen.getByLabelText(/descrição/i), 'Produto Teste');

    // Preencher apenas m³
    await user.type(screen.getByLabelText(/quantidade.*m³/i), '1,500');

    // Salvar
    await user.click(screen.getByRole('button', { name: /salvar/i }));

    await waitFor(() => {
      expect(mockSupplyChainService.saveProductSupplyChain).toHaveBeenCalledWith({
        product_description: 'Produto Teste',
        product_quantity: '1.500',
        product_quantity_tons: '',
        status: 'A',
        supply_chain_id: 'test-123',
      });
    });
  });

  it('should save product with only tons quantity', async () => {
    const user = userEvent.setup();

    renderWithProviders(<ProdutoPage supplyChain={mockSupplyChain} />);

    // Preencher descrição
    await user.type(screen.getByLabelText(/descrição/i), 'Produto Teste');

    // Preencher apenas toneladas
    await user.type(screen.getByLabelText(/quantidade.*toneladas/i), '0,750');

    // Salvar
    await user.click(screen.getByRole('button', { name: /salvar/i }));

    await waitFor(() => {
      expect(mockSupplyChainService.saveProductSupplyChain).toHaveBeenCalledWith({
        product_description: 'Produto Teste',
        product_quantity: '',
        product_quantity_tons: '0.750',
        status: 'A',
        supply_chain_id: 'test-123',
      });
    });
  });

  it('should show validation error when both quantities are empty', async () => {
    const user = userEvent.setup();

    renderWithProviders(<ProdutoPage supplyChain={mockSupplyChain} />);

    // Preencher apenas descrição
    await user.type(screen.getByLabelText(/descrição/i), 'Produto Teste');

    // Tentar salvar sem quantidades
    await user.click(screen.getByRole('button', { name: /salvar/i }));

    await waitFor(() => {
      expect(screen.getByText(/pelo menos uma quantidade/i)).toBeInTheDocument();
    });

    expect(mockSupplyChainService.saveProductSupplyChain).not.toHaveBeenCalled();
  });

  it('should handle API errors gracefully', async () => {
    const user = userEvent.setup();
    mockSupplyChainService.saveProductSupplyChain.mockRejectedValue(new Error('API Error'));

    renderWithProviders(<ProdutoPage supplyChain={mockSupplyChain} />);

    // Preencher dados válidos
    await user.type(screen.getByLabelText(/descrição/i), 'Produto Teste');
    await user.type(screen.getByLabelText(/quantidade.*m³/i), '1,500');

    // Tentar salvar
    await user.click(screen.getByRole('button', { name: /salvar/i }));

    await waitFor(() => {
      expect(screen.getByText(/erro/i)).toBeInTheDocument();
    });
  });
});
```

## Conclusão

Este plano de testes abrangente garante a qualidade e confiabilidade da implementação dos campos de toneladas no sistema WoodFlow. A execução sistemática destes testes, seguindo a estratégia definida, minimiza riscos e assegura uma funcionalidade robusta que atende aos requisitos de negócio e mantém a experiência do usuário.

### Benefícios do Plano:

- **Cobertura Completa**: Testes em todas as camadas (unitário, integração, E2E)
- **Qualidade Assegurada**: Critérios claros de aceitação e métricas de qualidade
- **Risco Minimizado**: Identificação e mitigação proativa de riscos
- **Manutenibilidade**: Estrutura organizada e exemplos práticos para a equipe
- **Experiência do Usuário**: Validação de usabilidade e performance

A implementação seguindo este plano resultará em uma funcionalidade confiável, bem testada e pronta para produção.

## Detalhamento por Categoria

[Detalhes por tipo de teste]

## Recomendações

[Ações necessárias]

```

## 12. Riscos e Mitigações

| Risco                     | Probabilidade | Impacto | Mitigação                    |
| ------------------------- | ------------- | ------- | ---------------------------- |
| Falha validação dados     | Média         | Alto    | Testes extensivos validação  |
| Regressão funcionalidades | Baixa         | Alto    | Suite completa regressão     |
| Performance degradada     | Baixa         | Médio   | Testes performance contínuos |
| Inconsistência dados      | Média         | Médio   | Validação cruzada m³/tons    |

## Conclusão

Este plano de testes garante cobertura completa da funcionalidade de campos de toneladas, mantendo a qualidade e confiabilidade do sistema WoodFlow. A execução seguindo este plano minimiza riscos e assegura uma implementação robusta.
```
