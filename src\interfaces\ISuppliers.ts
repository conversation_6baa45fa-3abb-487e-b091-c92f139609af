import { Link } from './ICustomers';

export interface ISuppliers {
  fornecedor_id: number;
  nom_fornecedor: string;
  num_telefone: string;
  json_endereco?: any;
  nom_contato_comercial: string;
  des_email_contato_comercial: string;
  nom_contato_producao: string;
  des_email_contato_producao: string;
  nom_contato_logistica: string;
  des_email_contato_logistica: string;
  nom_contato_despachante: string;
  des_email_contato_despachante: string;
  des_observacao?: any;
  dt_inativacao?: any;
  usuario_id_inativacao?: any;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}
export interface ISuppliersResponse {
  current_page: number;
  data: ISuppliers[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: Link[];
  next_page_url?: any;
  path: string;
  per_page: number;
  prev_page_url?: any;
  to: number;
  total: number;
}

export interface ISuppliersContact {
  usuario_id?: number;
  nom_usuario?: string;
  des_usuario_acesso?: string;
  perfil_id?: number;
  arq_imagem_usuario?: number | null;
  num_celular?: number | null;
  json_endereco?: number | null;
  cod_lingua?: string;
  data: ISuppliersContact[];
}
