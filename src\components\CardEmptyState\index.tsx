import React from 'react';
import { useHistory } from 'react-router-dom';
import { Panel } from 'rsuite';
import './styles.scss';

interface IProp {
  title: string;
  action: string;
  subtitle: string;
}

export const CardEmptyState = ({ title, action, subtitle }: IProp): React.ReactElement => {
  const history = useHistory();

  return (
    <>
      <Panel
        shaded
        bordered
        bodyFill
        style={{
          display: 'inline-block',
          width: 300,
          background: 'rgba(255, 255, 255, 0.4);',
          backdropFilter: 'blur(2px)',
        }}
        className="cardEmptyState"
      >
        <div className="alignItems">
          <h5>{title}</h5>
          <div className="alignClick">
            <span role="presentation" onClick={() => history.push('/products')}>
              {action}
            </span>{' '}
            <small>{subtitle}</small>
          </div>
        </div>
      </Panel>
    </>
  );
};
