.landing-page {
  padding: 10px;
  margin: 10px;
  overflow-x: hidden;

  // Hero Section
  .hero-section {
    background: linear-gradient(135deg, #18b680 0%, #02b5ba 100%);
    color: white;
    position: relative;
    min-height: 70vh; // Reduzido de 100vh para 70vh
    display: flex;
    align-items: center;
    width: 100%;
    margin: 0;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 50%;
      height: 100%;
      background: url('../../statics/home/<USER>') center/cover;
      opacity: 0.3;
      z-index: 1;
    }

    .container {
      padding-left: 4rem;
      padding-right: 4rem;

      @media (max-width: 768px) {
        padding-left: 1rem;
        padding-right: 1rem;
      }
    }

    .hero-content {
      position: relative;
      z-index: 2;
      padding: 4rem 0; // Removido padding lateral, agora controlado pelo container

      @media (max-width: 768px) {
        padding: 2rem 0; // Padding menor em mobile
      }
    }

    .hero-title {
      font-size: 3.5rem;
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: 1.5rem;

      @media (max-width: 768px) {
        font-size: 2.5rem;
      }
    }

    .hero-subtitle {
      font-size: 1.2rem;
      margin-bottom: 3rem;
      opacity: 0.9;
    }

    .highlight {
      color: #ffbf00;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .highlight-green {
      color: #d1e7dd;
      font-weight: 700;
    }

    .hero-cta-section {
      .cta-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 2rem;

        @media (max-width: 768px) {
          font-size: 1.5rem;
        }
      }

      .cta-button {
        background: #3c755f;
        border: none;
        padding: 2rem 3rem;
        font-size: 1.3rem;
        font-weight: 600;
        border-radius: 50px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(24, 32, 26, 0.2);

        &:hover {
          background: #1f3b33;
          transform: translateY(-2px);
          box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        }
      }
    }

    .hero-image {
      position: relative;
      z-index: 2;

      .wood-visual {
        width: 100%;
        height: 400px;
        background: url('../../statics/home/<USER>') center/cover;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        transform: perspective(1000px) rotateY(-15deg);

        @media (max-width: 768px) {
          transform: none;
          height: 250px;
        }
      }
    }
  }

  // Features Section
  .features-section {
    padding: 5rem 0;
    background: #f8f9fa;
    width: 100%;
    margin: 0;

    @media (max-width: 768px) {
      padding: 3rem 0;
    }

    .container {
      padding-left: 4rem;
      padding-right: 4rem;

      @media (max-width: 768px) {
        padding-left: 1rem;
        padding-right: 1rem;
      }
    }

    .section-title {
      font-size: 2.5rem;
      font-weight: 600;
      color: #2c0b0e;
      margin-bottom: 1.5rem;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .section-subtitle {
      font-size: 1.2rem;
      color: #666;
      line-height: 1.6;
    }

    .highlight-green {
      color: #18b680;
      font-weight: 700;
    }
  }

  // EUDR Section
  .eudr-section {
    padding: 5rem 0;
    background: white;
    width: 100%;
    margin: 0;

    @media (max-width: 768px) {
      padding: 3rem 0;
    }

    .container {
      padding-left: 4rem;
      padding-right: 4rem;

      @media (max-width: 768px) {
        padding-left: 1rem;
        padding-right: 1rem;
      }
    }

    .eudr-title {
      font-size: 2.5rem;
      font-weight: 600;
      color: #2c0b0e;
      margin-bottom: 2rem;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .eudr-description {
      font-size: 1.1rem;
      color: #666;
      line-height: 1.7;
      margin-bottom: 2rem;
    }

    .eudr-benefits {
      .benefit-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        font-size: 1.1rem;

        .benefit-icon {
          background: #18b680;
          color: white;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 1rem;
          font-weight: bold;
          font-size: 0.9rem;
        }
      }
    }

    .eudr-visual {
      text-align: center;
      position: relative;

      img {
        width: 100%;
        height: 450px;
        object-fit: cover;
        border-radius: 20px;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        transition: transform 0.3s ease, box-shadow 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
          height: 300px;
        }
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(24, 182, 128, 0.08), rgba(139, 69, 19, 0.05));
        border-radius: 20px;
        pointer-events: none;
        z-index: 1;
      }

      &::after {
        content: '✓ EUDR Compliant';
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(24, 182, 128, 0.9);
        color: white;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 12px;
        font-weight: 600;
        z-index: 2;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 15px rgba(24, 182, 128, 0.3);
      }
    }
  }

  // Documents Section
  .documents-section {
    padding: 5rem 0;
    background: #f8f9fa;
    width: 100%;
    margin: 0;

    @media (max-width: 768px) {
      padding: 3rem 0;
    }

    .container {
      padding-left: 4rem;
      padding-right: 4rem;

      @media (max-width: 768px) {
        padding-left: 1rem;
        padding-right: 1rem;
      }
    }

    .document-card {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      text-align: center;
      height: 100%;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border: 1px solid #e9ecef;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      }

      .document-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
      }

      h4 {
        color: #2c0b0e;
        font-weight: 600;
        margin-bottom: 1rem;
        font-size: 1.2rem;
      }

      p {
        color: #666;
        line-height: 1.6;
        margin-bottom: 0;
        font-size: 0.95rem;
      }
    }
  }

  // Importância dos Documentos Section
  .importance-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    width: 100%;
    margin: 0;

    @media (max-width: 768px) {
      padding: 3rem 0;
    }

    .container {
      padding-left: 4rem;
      padding-right: 4rem;

      @media (max-width: 768px) {
        padding-left: 1rem;
        padding-right: 1rem;
      }
    }

    .importance-content {
      .importance-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 30px;
        padding: 20px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;

        &:hover {
          transform: translateX(10px);
          box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
        }

        @media (max-width: 768px) {
          flex-direction: column;
          text-align: center;

          &:hover {
            transform: translateY(-5px);
          }
        }

        .importance-number {
          background: linear-gradient(135deg, #18b680, #45a049);
          color: white;
          width: 50px;
          height: 50px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          font-size: 1.2rem;
          margin-right: 20px;
          flex-shrink: 0;

          @media (max-width: 768px) {
            margin-right: 0;
            margin-bottom: 15px;
          }
        }

        .importance-text {
          h4 {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 1.1rem;
          }

          p {
            color: #6c757d;
            margin: 0;
            line-height: 1.6;
          }
        }
      }
    }

    .importance-visual {
      .stats-container {
        background: white;
        border-radius: 20px;
        padding: 60px;
        padding-top: 70px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        margin-top: -30px;

        @media (max-width: 768px) {
          padding: 30px 20px;
        }

        .stat-item {
          text-align: center;
          margin-bottom: 30px;

          &:last-child {
            margin-bottom: 0;
          }

          .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #18b680;
            margin-bottom: 8px;

            @media (max-width: 768px) {
              font-size: 2rem;
            }
          }

          .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            line-height: 1.4;
          }
        }
      }

      @media (max-width: 991px) {
        margin-top: 2rem;
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .hero-section {
      .hero-content {
        text-align: center;
        padding: 1rem 0;
      }

      .hero-image {
        margin-top: 2rem;
      }
    }

    .eudr-section {
      .eudr-content {
        text-align: center;
        margin-bottom: 3rem;
      }
    }
  }
}
