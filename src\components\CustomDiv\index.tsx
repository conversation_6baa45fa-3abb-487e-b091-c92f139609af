import React from 'react';
import { useHistory } from 'react-router-dom';
import './styles.scss';

type IProps = {
  text: string;
  button?: any;
  colorBackground?: string;
  colorText?: string;
  spanText?: string;
  endText?: string;
};

const CustomDivComponent: React.FC<IProps> = ({ text, button, colorBackground, colorText, spanText, endText }) => {
  const history = useHistory();

  return (
    <div className="custom-component" style={{ background: colorBackground }}>
      <div className="text" style={{ color: colorText }}>
        {text}
        <span role="presentation" onClick={() => history.push(`/new-quote`)}>
          {spanText ? String(spanText).trimEnd() : ''}
        </span>
        {endText ? ` ${endText}` : ''}
      </div>
      <div className="buttonInformate">{button}</div>
    </div>
  );
};

export default CustomDivComponent;
