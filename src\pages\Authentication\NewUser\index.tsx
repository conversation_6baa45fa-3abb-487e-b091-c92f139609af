import React from 'react';
import { Row, Col, Image, Form } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom';
import { isValidEmail } from '@brazilian-utils/brazilian-utils';

import Section from '../../../components/Section';
import Select from '../../../components/Select';
import Button from '../../../components/Button';
import CustomInput from '../../../components/Input/CustomInput';
import Text from '../../../components/Text';
import ImgWood from '../../../statics/img-wood.png';
import { ISelectOption } from '../../../interfaces';
import { useLoader } from '../../../contexts/LoaderContext';

import renderSelectValues from '../../../utils/renderSelectValues';

import checkObjectIsEmpty from '../../../utils/checkObjectIsEmpty';
import toastMsg, { ToastType } from '../../../utils/toastMsg';
import { useAnalytics } from '../../../contexts/AnalyticsContext';
import './styles.scss';
import CountriesService from '../../../services/countries.service';

const NewUser = (): React.ReactElement => {
  const { t } = useTranslation();
  const { setShowLoader } = useLoader();
  const history = useHistory();
  const { trackEvent } = useAnalytics();

  const [firstName, setFirstName] = React.useState<string>('');
  const [lastName, setLastName] = React.useState<string>('');
  const [email, setEmail] = React.useState<string>('');
  const [emailError, setEmailError] = React.useState<boolean>(false);
  const [company_name, setCompanyName] = React.useState<string>('');
  const [terms_of_use, setTermsOfUse] = React.useState<boolean>(false);
  const [country, setCountry] = React.useState<ISelectOption | null>(null);

  const MSG = t('exceptions.invalidEmail');

  const selectCountries = async (value: string): Promise<ISelectOption[]> => {
    const res = await CountriesService.list();

    const options = res.map((item: any) => ({
      value: item.cod_pais,
      label: item.nom_pais,
    }));

    return renderSelectValues(value, options);
  };

  const handleSubmit = async (): Promise<void> => {
    if (!isValidEmail(email)) {
      setEmailError(true);
      return;
    }
    try {
      const values: any = {
        first_name: firstName,
        last_name: lastName,
        email,
        company_name,
        terms_of_use,
        country_id: country ? country.value : '',
      };

      setShowLoader(true);

      toastMsg(ToastType.Success, t('response.postSuccess'));
      history.push('/confirm-account', { email });
    } catch (error) {
      toastMsg(ToastType.Error, t('exceptions.emailAlready'));
    } finally {
      setShowLoader(false);
    }
  };
  return (
    <Section title="New User" description="New user">
      <Row className="new-user d-flex">
        <Col className="divInfo" md={4}>
          <div className="displayImg">
            <Image src={ImgWood} />
          </div>
          <div>
            <p className="labelInfoImport">
              {t('labels.labelImportTheLumber')} <br />
            </p>
            <li>{t('titles.item1')}.</li>
            <li>{t('titles.item2')}.</li>
            <li>{t('titles.item3')}.</li>
            <li>{t('titles.item4')}.</li>
          </div>
        </Col>
        <Col className="divCrud" md={8}>
          <p className="textFree">{t('language') === 'BR' ? 'INICIE SEM CUSTOS.' : 'Start for free.'}</p>
          <Text className="pages-title" as="b" size="2rem" weight={600} color="#201e40">
            {t('headers.joinWoodflow')}
          </Text>
          <br />
          <Text className="pages-title" as="b" size="2rem" weight={600} color="#201e40">
            {t('headers.newCreateAccount')}
          </Text>
          <p className="textSign mt-4 mb-4">
            {t('labels.labelAreYouProducer')}{' '}
            <a
              href=" https://woodflow.com.br/woodflow-para-o-setor-madeireiro/"
              target="_blank"
              rel="noopener noreferrer"
              className="linkLoginHere"
            >
              {t('labels.labelEnterHere')}
            </a>
          </p>

          <Row>
            <Col md={6} className="gapItensCrud">
              <CustomInput
                maxLength={255}
                type="text"
                value={firstName}
                onChange={(e) => {
                  setFirstName(e.target.value);
                }}
                id="firstName"
                label={t('labels.labelFirstName')}
                desc="First name"
                required
                cy="test-inputFirstName"
              />
            </Col>

            <Col md={6}>
              <CustomInput
                maxLength={255}
                type="text"
                value={lastName}
                onChange={(e) => {
                  setLastName(e.target.value);
                }}
                id="lastName"
                label={t('labels.labelLastName')}
                desc="Last name"
                required
                cy="test-inputLastName"
              />
            </Col>
          </Row>
          <Row>
            <Col md={12} className="mt-4">
              <CustomInput
                maxLength={255}
                type="text"
                value={email}
                onChange={(e) => {
                  setEmailError(false);
                  setEmail(e.target.value);
                }}
                onBlur={() => {
                  if (!isValidEmail(email)) {
                    setEmailError(true);
                  }
                }}
                id="email"
                label="E-mail*"
                desc="E-mail"
                required
                cy="test-inputemail"
                isInvalid={emailError}
                msg={MSG}
              />
            </Col>
          </Row>
          <Row>
            <Col md={12} className="mt-4">
              <CustomInput
                maxLength={65}
                type="text"
                value={company_name}
                onChange={(e) => {
                  setCompanyName(e.target.value);
                }}
                id="companyName"
                label={t('labels.labelCompanyName')}
                desc="Company name"
                required
                cy="test-inputCompanyName"
              />
            </Col>
          </Row>
          <Row>
            <Col md={12} className="mt-4">
              <Select
                loadOptions={selectCountries}
                cacheOptions
                defaultOptions
                title={t('labels.labelContryName')}
                placeholder={t('labels.labelSelectCountry')}
                onChange={(e: ISelectOption) => {
                  setCountry(e);
                }}
                cy="test-selectCountry"
                value={!checkObjectIsEmpty(country) ? country : null}
              />
            </Col>
          </Row>

          <Col className="d-flex mt-4 gap-2">
            <input
              type="checkbox"
              onChange={() => {
                setTermsOfUse(!terms_of_use);
              }}
              checked={terms_of_use}
            />
            {t('labels.labelAcceptTerms')}{' '}
            <a href="https://www.woodflow.com.br/privacy-policy/" target="_blank" rel="noopener noreferrer">
              {t('labels.labelPrivacy')}
            </a>
          </Col>
          <Row className="align-items-center justify-content-end mt-4">
            <Button
              className="btnCreate w-10 d-flex align-items-center"
              type="button"
              cy="test-create"
              disabled={!terms_of_use || !firstName || !lastName || !email || !country?.value || !company_name}
              onClick={() => {
                handleSubmit();
                trackEvent('Create Account', {
                  action: 'Submit create account',
                });
              }}
            >
              <Text className="labelLogin" as="b">{`${t('headers.createAccount')}`}</Text>
            </Button>
          </Row>
        </Col>
      </Row>
    </Section>
  );
};

export default NewUser;
