import React, { useState } from 'react';
import { Download } from 'lucide-react';
import { Table } from 'rsuite';
import { Col, Container, Row } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import EmptyState from '../../../components/EmptyState';
import EmptyStateImage from '../../../statics/emptyState.png';
import Button from '../../../components/Button';
import Modal from '../../../components/Modal';

import '../styles.scss';

export const DownloadArquivo = ({
  sortColumn,
  sortType,
  handleSortColumn,
  isMobile,
  data,
  tableColumns,
  showLoader,
}: any): React.ReactElement => {
  const { Column, HeaderCell, Cell } = Table;
  const { t, i18n } = useTranslation();
  const [showMsg, setShowMsg] = useState<any>({
    show: false,
    msg: null,
    idDoc: null,
    titulo: null,
    site: null,
    orgao: null,
    local: null,
  });

  const downloadDoc = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <Container className="containerBackgroundCustomers">
      <Modal
        show={showMsg.show}
        handleClose={() => setShowMsg({ ...showMsg, show: false })}
        title={showMsg.titulo}
        size="lg"
        className="styleModalConfirm"
      >
        <Container>
          <Row className="mt-4 p-2">
            <Col className="d-flex align-items-center ">
              <h6>
                {showMsg.msg}
                <br />
                <br />
                <b>{i18n.language === 'pt-BR' ? 'Órgão Responsável' : 'Responsible Agency'}:</b> {showMsg.orgao}
                <br />
                <b>{i18n.language === 'pt-BR' ? 'Local de Acesso' : 'Access Location'}:</b> {showMsg.local}
                <br />
                {showMsg?.site?.startsWith('https:') || showMsg?.site?.startsWith('http:') ? (
                  <Button
                    cy="link"
                    variant="link"
                    onClick={() => window.open(showMsg.site, '_blank')}
                    style={{ color: 'blue' }}
                  >
                    {showMsg.site}
                  </Button>
                ) : (
                  <></>
                )}
              </h6>
            </Col>
          </Row>
        </Container>

        <Row className="mt-4">
          <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
            <Button
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={() => {
                setShowMsg(false);
              }}
            >
              {t('buttons.fechar')}
            </Button>
          </Col>
        </Row>
      </Modal>
      {data.length > 0 ? (
        <>
          <Table
            bordered
            cellBordered
            sortColumn={sortColumn}
            sortType={sortType}
            onSortColumn={handleSortColumn}
            autoHeight={isMobile}
            height={300}
            affixHorizontalScrollbar={!isMobile}
            data={data}
            className="table"
            rowClassName={(rowData: any) => rowData?.className || ''}
          >
            {tableColumns.map((column: any) => {
              const { field, headerName, color, cursor, textDecoration, fixed, ...rest } = column;

              if (field === 'actions') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            {rowData.idDocumento ? (
                              <div
                                role="presentation"
                                title="Download"
                                style={{ cursor: 'pointer' }}
                                onClick={() => downloadDoc(rowData.filepath)}
                              >
                                <Download size={20} />
                              </div>
                            ) : (
                              <></>
                            )}
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              if (field === 'enviarDocumento') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      <div className="d-flex justify-content-center gap-2">
                        <div>
                          <label style={{ color: '#046b04' }}>{t('labels.documentoEnviado')}</label>
                        </div>
                      </div>
                    </Cell>
                  </Column>
                );
              }

              if (field === 'ondeEmitir') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            <div>
                              <Button
                                cy="btn_oe"
                                variant="link"
                                style={{
                                  height: 30,
                                  fontSize: 12,
                                  marginTop: -4,
                                  color: '#201e40',
                                }}
                                onClick={() =>
                                  setShowMsg({
                                    show: true,
                                    msg: rowData.descricao,
                                    idDoc: rowData.idDocumento,
                                    titulo: rowData.documento,
                                    site: rowData.site,
                                    orgao: rowData.orgao,
                                    local: rowData.local,
                                  })
                                }
                              >
                                Info
                              </Button>
                            </div>
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              return (
                <>
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap />
                  </Column>
                </>
              );
            })}
          </Table>
        </>
      ) : (
        <>
          {!showLoader && (
            <EmptyState
              text={t('labels.labelDocumnetNotFound')}
              secondaryText={t('labels.labelDescDocumentNotFound')}
              img={EmptyStateImage}
            />
          )}
        </>
      )}
    </Container>
  );
};
