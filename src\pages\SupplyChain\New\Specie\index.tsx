import React, { useCallback, useState } from 'react';
import { Card, Container, Image, Row, Col, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import CustomInput from '../../../../components/Input/CustomInput';
import CustomCard from '../../../../components/Card';
import Select from '../../../../components/Select';
import './styles.scss';
import { ISelectOption } from '../../../../interfaces';
import Modal from '../../../../components/Modal';
import CustomButton from '../../../../components/Button';
import toastMsg, { ToastType } from '../../../../utils/toastMsg';
import { useLoader } from '../../../../contexts/LoaderContext';
import { CardContent } from '@mui/material';
import { Upload, FileIcon } from 'lucide-react';
import DatePicker from '../../../../components/Datepicker';
import InactiveUser from '../../../../statics/delete.svg';
import DocumentosService from '../../../../services/documentos.service';
import { formatDateToString } from '../../../../utils/formatDateToString';
import SupplyChainService from '../../../../services/supplyChain.service';
import FlorestaService, { IpropsMateriaPrima } from '../../../../services/floresta.service';
import CompaniesService from '../../../../services/companies.service';
import renderSelectValues from '../../../../utils/renderSelectValues';
import MaskedInput from 'react-text-mask';

interface IPropsCard {
  closeModal?: any;
  atualizarSpecie?: any;
  supplyChainId?: string;
  calcularSaldo?: any;
  supplyChainProductSaveId?: any;
  supplyChainSpecie?: any;
}

interface FileItem {
  uuid?: string;
  file: File;
  preview?: string;
  vencimento?: Date;
  path?: string;
  isExisting?: boolean; // Indica se o arquivo já existe no servidor
  id?: string; // ID do documento no servidor (se existir)
  tamanho_arquivo?: string;
}

const CardSupplySpecie = ({
  closeModal,
  atualizarSpecie,
  supplyChainId,
  supplyChainProductSaveId,
  supplyChainSpecie,
}: IPropsCard): React.ReactElement => {
  const { t } = useTranslation();
  const [show, setShow] = useState<any>({ show: false, client: null, idDoc: null });
  const { setShowLoader } = useLoader();
  const [files, setFiles] = useState<FileItem[]>([] as FileItem[]);
  const [dataAtual, setDataAtual] = useState<Date | null>(new Date());
  const [quantidadem3, setQuantidadem3] = useState<string>('');
  const [florestaSelect, setFlorestaSelect] = useState<ISelectOption>({} as ISelectOption);
  const [fornecedorSelect, setFornecedorSelect] = useState<ISelectOption>({} as ISelectOption);
  const [filePath, setFilePath] = useState<string>('');
  const [linkDoc, setLinkDoc] = useState<string>('');
  //eslint-disable-next-line
  const [query, setQuery] = React.useState<string>('');

  const [materiasPrima, setMateriasPrima] = useState<IpropsMateriaPrima[]>([]);
  const [materiasPrimaTons, setMateriasPrimaTons] = useState<IpropsMateriaPrima[]>([]); // NOVO: Estado para toneladas
  const [arquivoValido, setArquivoValido] = useState<string>('T');
  const selectFlorestas = useCallback(
    async (value: string): Promise<ISelectOption[]> => {
      const resFL = await FlorestaService.getFlorestaSelect(
        fornecedorSelect?.value ? String(fornecedorSelect?.value) : ''
      );

      const optionsFL = resFL.data.map((item: any) => ({
        value: item.id,
        label: item.nome,
      }));

      return renderSelectValues(value, optionsFL);
    },
    [fornecedorSelect]
  );
  //eslint-disable-next-line
  const [uploadError, setUploadError] = useState<any>(null);

  const clearUploadError = () => {
    setUploadError(null);
    localStorage.removeItem('uploadError');
  };

  // Verificar se há erro salvo no localStorage ao montar o componente
  React.useEffect(() => {
    const savedError = localStorage.getItem('uploadError');
    if (savedError) {
      try {
        const errorObj = JSON.parse(savedError);
        setUploadError(errorObj);
      } catch (e) {
        localStorage.removeItem('uploadError');
      }
    }
  }, []);

  // Função para fechar modal de forma controlada
  const handleCloseModal = React.useCallback(() => {
    if (closeModal) {
      closeModal({ show: false });
    }
  }, [closeModal]);

  const limparCampos = () => {
    setFlorestaSelect({} as ISelectOption);
  };

  const handleChangeFornecedor = (selectedOption: any): void => {
    setFornecedorSelect(selectedOption);
    limparCampos();
  };

  const recuperarMateriasPrima = useCallback(async (idFloresta: string): Promise<any> => {
    const resMT = await FlorestaService.getMateriaPrimaSelect(idFloresta);
    if (resMT) {
      return resMT.data;
    }
    return [];
  }, []);

  const selectMateriasPrima = useCallback(async (idFloresta: string): Promise<void> => {
    const resMT = await FlorestaService.getMateriaPrimaSelect(idFloresta);
    // Inicializar com campos de toneladas vazios
    const materiaPrimaWithTons = resMT.data.map((mp: IpropsMateriaPrima) => ({
      ...mp,
      quantidade_tons: mp.quantidade_tons || '',
    }));
    setMateriasPrima(materiaPrimaWithTons);
    setMateriasPrimaTons(materiaPrimaWithTons);
  }, []);

  const getDocumentoFloresta = useCallback(
    async (idF: number): Promise<void> => {
      const florestaRes = await FlorestaService.documentoFlorestaById(String(idF) || '');
      if (florestaRes && florestaRes.documento) {
        setLinkDoc(florestaRes.documento.documento_path);
      } else {
        setLinkDoc('');
      }
    },
    [linkDoc]
  );

  const handleChangeFloresta = (selectedOption: any): void => {
    setFlorestaSelect(selectedOption);
    getDocumentoFloresta(selectedOption.value);
    selectMateriasPrima(selectedOption.value);
  };

  const selectFornecedores = useCallback(
    async (value: string): Promise<ISelectOption[]> => {
      const resFornecedor = await CompaniesService.getCompaniesByExporter(query, 1, 100, 'supplier');

      const optFornecedr = resFornecedor.data.map((item: any) => ({
        value: item.id,
        label: item.name,
      }));

      return renderSelectValues(value, optFornecedr);
    },
    [query, setFornecedorSelect]
  );

  const getSupplyChainSpecieDoc = React.useCallback(async (idsp: string): Promise<any> => {
    try {
      const res = await SupplyChainService.getDocumentoBySupplyChainId(idsp);

      if (res) {
        return res;
      }
      return {};
    } catch (error) {
      return {};
    }
  }, []);

  const handleDeleteDocument = async (id: any): Promise<void> => {
    if (!id) {
      return;
    }

    try {
      setShowLoader(true);
      //await FlorestaService.deleteFloresta(id);
      setShowLoader(false);
      //history.push('/new-fornecedor/' + uuidFornecedor, { reload: true });
      toastMsg(ToastType.Success, t('response.deleteSuccess'));
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  };

  const validateFiles = (filesList: FileItem[]) => {
    if (filesList.length === 0) {
      setArquivoValido('T');
      return;
    }

    let isValid = true;
    filesList.forEach((fileItem) => {
      if (fileItem.file.type === 'application/pdf') {
        if (fileItem.file.size < 1024) {
          isValid = false;
        }
      }
    });
    setArquivoValido(isValid ? 'T' : 'F');
  };

  const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (event.dataTransfer.files) {
      // Limpar erro de upload anterior quando novos arquivos são selecionados
      clearUploadError();

      // Validar todos os arquivos
      const filesArray = Array.from(event.dataTransfer.files);
      for (const file of filesArray) {
        if (file.type !== 'application/pdf' || file.size < 1024) {
          toastMsg(ToastType.Error, t('exceptions.pdfType'));
          return;
        }
      }

      const newFiles = filesArray.map((file) => ({
        file,
        preview: URL.createObjectURL(file),
        vencimento: new Date(),
      }));
      const updatedFiles = [...files, ...newFiles];
      setFiles(updatedFiles);
      validateFiles(updatedFiles);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      // Limpar erro de upload anterior quando novos arquivos são selecionados
      clearUploadError();

      const filesArray = Array.from(event.target.files);

      // Validar todos os arquivos
      for (const file of filesArray) {
        const buffer = await file.slice(0, 5).arrayBuffer();
        const header = new Uint8Array(buffer);
        const pdfHeader = String.fromCharCode.apply(null, Array.from(header));

        if (!pdfHeader.startsWith('%PDF-') || file.type !== 'application/pdf' || file.size < 1024) {
          toastMsg(ToastType.Error, t('exceptions.pdfType'));
          return;
        }
      }

      const newFiles = filesArray.map((file) => ({
        file,
        preview: URL.createObjectURL(file),
        vencimento: new Date(),
      }));
      const updatedFiles = [...files, ...newFiles];
      setFiles(updatedFiles);
      validateFiles(updatedFiles);
    }
  };

  const formatFileSize = (bytes: number) => {
    // Format file size
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];

    if (bytes < k) {
      return bytes + ' ' + sizes[0];
    } else if (bytes < k * k) {
      return parseFloat((bytes / k).toFixed(2)) + ' ' + sizes[1];
    } else if (bytes < k * k * k) {
      return parseFloat((bytes / (k * k)).toFixed(2)) + ' ' + sizes[2];
    } else {
      return parseFloat((bytes / (k * k * k)).toFixed(2)) + ' ' + sizes[3];
    }
  };

  const removeFile = async (index: number) => {
    const newFiles = [...files];
    const fileToRemove = newFiles[index];

    // Se for um arquivo existente no servidor, pode ser necessário fazer uma chamada à API
    // Por enquanto, apenas removemos da lista local
    if (fileToRemove.isExisting && fileToRemove.id) {
      // TODO: Implementar chamada à API para remover arquivo do servidor se necessário
      // await DocumentosService.delete(fileToRemove.id);
      //eslint-disable-next-line
      console.log('Removendo arquivo existente:', fileToRemove.id);
    }

    URL.revokeObjectURL(newFiles[index].preview || '');
    newFiles.splice(index, 1);
    setFiles(newFiles);
    validateFiles(newFiles);
  };

  const getTipoDocumento = React.useCallback(async (idTipo: string): Promise<any> => {
    try {
      const res: any = await DocumentosService.getTiposDocumento(idTipo);

      if (res) {
        return res;
      }
      return null;
    } catch (error) {
      console.error('Erro ao obter tipo de documento:', error);
      return null;
    }
  }, []);

  const getSuppliChainSpecieMP = React.useCallback(async (supplyChainEspecieId: string): Promise<any> => {
    try {
      const res = await SupplyChainService.getSupplyChainSpecieMP(supplyChainEspecieId);

      if (res) {
        return res;
      }
    } catch (error) {
      return {};
    }
  }, []);

  React.useEffect(() => {
    let controle = true;
    async function loadCrudDetail(): Promise<void> {
      setFornecedorSelect({
        value: supplyChainSpecie?.fornecedor_id,
        label: supplyChainSpecie?.fornecedor_nome,
      });

      //setCientifico(supplyChainSpecie?.materia_prima?.nome_cientifico || '');
      setFlorestaSelect({ value: supplyChainSpecie?.floresta?.id, label: supplyChainSpecie?.floresta?.nome });
      //setQuantidadem3(supplyChainSpecie?.mp_qtde.replace('.', ',') || '');

      if (supplyChainSpecie?.floresta?.id !== undefined) {
        await getDocumentoFloresta(supplyChainSpecie?.floresta?.id);
      }

      const resdoc = await getSupplyChainSpecieDoc(supplyChainSpecie?.id);

      // A resposta pode ser diretamente o array ou ter uma propriedade data
      const documentos = resdoc.data || resdoc;

      if (documentos) {
        let loadedFiles: FileItem[] = [];

        // Verifica se a resposta é um array de documentos (nova estrutura)
        if (Array.isArray(documentos)) {
          // Nova estrutura: array de documentos diretamente
          loadedFiles = documentos
            .filter((doc: any) => {
              const hasDoc = doc.documento && doc.documento.documento_path;
              return hasDoc;
            })
            .map((doc: any) => {
              const fileName = doc.documento.documento_path.split('/').pop() || 'documento.pdf';
              return {
                uuid: doc.uuid,
                file: new File([doc.documento.documento_path], fileName),
                preview: URL.createObjectURL(new File([doc.documento.documento_path], doc.documento.documento_path)),
                vencimento: new Date(doc.documento.vencimento || new Date()),
                path: doc.documento.documento_path,
                isExisting: true,
                id: doc.id?.toString(),
                tamanho_arquivo: doc.documento.tamanho_arquivo,
              };
            });

          // Define o primeiro arquivo como filePath para compatibilidade
          if (loadedFiles.length > 0) {
            setFilePath(loadedFiles[0].path || '');
          }
        } else if (resdoc.data.documento) {
          // Caso de documento único (compatibilidade com versão anterior)
          const fileName = resdoc.data.documento.documento_path.split('/').pop() || 'documento.pdf';
          setFilePath(resdoc.data.documento.documento_path);
          loadedFiles = [
            {
              uuid: resdoc.data.uuid,
              file: new File([resdoc.data.documento.documento_path], fileName),
              preview: URL.createObjectURL(
                new File([resdoc.data.documento.documento_path], resdoc.data.documento.documento_path)
              ),
              vencimento: new Date(resdoc.data.documento.vencimento),
              path: resdoc.data.documento.documento_path,
              isExisting: true,
              id: resdoc.data.documento.id?.toString(),
            },
          ];
        } else if (resdoc.data.documentos && Array.isArray(resdoc.data.documentos)) {
          // Caso de múltiplos documentos em objeto
          loadedFiles = resdoc.data.documentos.map((doc: any) => {
            const fileName = doc.documento_path.split('/').pop() || 'documento.pdf';
            return {
              uuid: doc.uuid,
              file: new File([doc.documento_path], fileName),
              preview: URL.createObjectURL(new File([doc.documento_path], doc.documento_path)),
              vencimento: new Date(doc.vencimento),
              path: doc.documento_path,
              isExisting: true,
              id: doc.id?.toString(),
            };
          });

          // Define o primeiro arquivo como filePath para compatibilidade
          if (loadedFiles.length > 0) {
            setFilePath(loadedFiles[0].path || '');
          }
        }

        if (loadedFiles.length > 0) {
          setFiles(loadedFiles);
          validateFiles(loadedFiles);
        } else {
          //eslint-disable-next-line
          console.log('Nenhum arquivo carregado');
        }
      } else {
        //eslint-disable-next-line
        console.log('documentos é undefined ou null');
      }
    }

    if (controle && supplyChainSpecie?.id) {
      loadCrudDetail();
    } else {
      //eslint-disable-next-line
      console.log('loadCrudDetail não executado. controle:', controle, 'supplyChainSpecie?.id:', supplyChainSpecie?.id);
    }

    return () => {
      controle = false;
    };
  }, [
    setFornecedorSelect,
    setFlorestaSelect,
    setQuantidadem3,
    setFilePath,
    setFiles,
    selectMateriasPrima,
    supplyChainSpecie,
  ]);

  React.useEffect(() => {
    let controle = true;

    async function loadQuantidadeMP(): Promise<void> {
      const resMT = await recuperarMateriasPrima(supplyChainSpecie?.floresta?.id);

      const res = await getSuppliChainSpecieMP(supplyChainSpecie?.id);

      if (res) {
        const newMateriaPrimas: IpropsMateriaPrima[] = resMT.reduce((acc: IpropsMateriaPrima[], mp: any) => {
          const foundMateria = res.find((item: any) => item.materia_prima_id === mp.id);
          if (foundMateria) {
            acc.push({
              id: mp.id,
              nome: mp.nome,
              nome_cientifico: mp.nome_cientifico,
              quantidade: foundMateria.mp_qtde,
              quantidade_tons: foundMateria.mp_qtde_tons || '', // NOVO
              status: 'A',
            });
          } else {
            acc.push({
              id: mp.id,
              nome: mp.nome,
              nome_cientifico: mp.nome_cientifico,
              quantidade: '',
              quantidade_tons: '', // NOVO
              status: 'A',
            });
          }
          return acc;
        }, []);

        setMateriasPrima(newMateriaPrimas);
        setMateriasPrimaTons(newMateriaPrimas); // NOVO
      } else {
        setMateriasPrima(resMT);
        setMateriasPrimaTons(resMT.map((mp: IpropsMateriaPrima) => ({ ...mp, quantidade_tons: '' }))); // NOVO
      }
    }

    if (controle && supplyChainSpecie?.floresta) {
      loadQuantidadeMP();
    }

    return () => {
      controle = false;
    };
  }, [getSuppliChainSpecieMP, recuperarMateriasPrima, selectMateriasPrima, setMateriasPrima, supplyChainSpecie]);

  const handleSubmitFornecedor = React.useCallback(async () => {
    try {
      if (files.length > 0 && arquivoValido === 'F') {
        toastMsg(ToastType.Error, t('exceptions.fileInvalid'));
        return;
      }
      setShowLoader(true);
      const formData = new FormData();
      //const saldoFormatado = parseFloat((saldo || '0').replace(',', '.'));
      //const quantidadem3Formatado = parseFloat(quantidadem3.replace(',', '.'));

      if (!fornecedorSelect?.value) {
        toastMsg(ToastType.Error, 'Selecione um Fornecedor');
        return;
      }

      if (!florestaSelect?.value) {
        toastMsg(ToastType.Error, 'Selecione uma Floresta');
        return;
      }

      // NOVA: Validar se pelo menos uma quantidade foi preenchida para cada matéria-prima
      const hasValidQuantities = materiasPrima.every((mp) => {
        const qtdM3 = mp.quantidade?.trim();
        const qtdTons = mp.quantidade_tons?.trim();
        return qtdM3 || qtdTons;
      });

      if (!hasValidQuantities) {
        toastMsg(ToastType.Error, t('labels.quantityRequired'));
        setShowLoader(false);
        return;
      }

      let newSupplyEspecieDoc: any;

      if (supplyChainSpecie) {
        newSupplyEspecieDoc = {
          id: supplyChainSpecie.id,
          uuid: supplyChainSpecie.uuid,
          floresta_nome: florestaSelect.label,
          fornecedor_nome: fornecedorSelect.label,
          supply_chain_product_id: supplyChainProductSaveId,
          floresta_id: florestaSelect.value,
          fornecedor_id: fornecedorSelect.value,
        };

        formData.append('id', JSON.stringify(supplyChainSpecie.id));
      } else {
        newSupplyEspecieDoc = {
          floresta_nome: florestaSelect.label,
          fornecedor_nome: fornecedorSelect.label,
          supply_chain_product_id: supplyChainProductSaveId,
          floresta_id: florestaSelect.value,
          fornecedor_id: fornecedorSelect.value,
        };
      }

      formData.append('supplyEspecieDoc', JSON.stringify(newSupplyEspecieDoc));

      // Filtra apenas os arquivos novos (não existentes) para upload
      //const newFiles = files.filter((file) => !file.isExisting);

      if (files.length > 0) {
        //formData.append('files', files);
        files.forEach((file, index) => {
          formData.append('files[' + index + ']', file.file);
        });
      }

      const tipoDocumento = await getTipoDocumento('32');

      if (!tipoDocumento || !tipoDocumento.data) {
        toastMsg(ToastType.Error, 'Erro ao obter tipo de documento');
        return;
      }

      setDataAtual(new Date());

      const documentos = Array.from(files).map((file: any) => ({
        nome: tipoDocumento.data.titulo,
        descricao: tipoDocumento.data.descricao,
        status: 'A',
        documento: {
          uuid: file.uuid,
          nome: tipoDocumento.data.titulo,
          descricao: tipoDocumento.data.descricao,
          extensao: '',
          tamanho_arquivo: formatFileSize(file.file.size),
          vencimento: file.vencimento ? formatDateToString(new Date(file.vencimento)) : '',
          tipo_documento_id: String(tipoDocumento.data.id),
          status: 'A',
        },
        supply_chain_especie_id: '',
      }));

      formData.append('documento', JSON.stringify(documentos));

      // NOVO: Enviar dados de matéria-prima com quantidades em m³ e toneladas
      formData.append('materia_prima', JSON.stringify(materiasPrima));

      // Fazer a chamada da API e aguardar o resultado
      await SupplyChainService.salvarSpecie(formData);

      // Se chegou até aqui sem erro, o salvamento foi bem-sucedido
      toastMsg(ToastType.Success, t('response.saveSuccess'));
      atualizarSpecie();

      // Fechar o modal apenas em caso de sucesso
      handleCloseModal();
    } catch (error: any) {
      console.error('Erro ao salvar specie:', error);

      // Verificar se é erro de upload de PDF
      const errorData = error?.response?.data;
      if (errorData && (errorData.isPdfError || errorData.showHelpButton)) {
        // Setar uploadError para exibir a mensagem de erro
        const uploadErrorObj = {
          message: errorData.message || error?.message || 'Erro no upload do PDF',
          showHelpButton: errorData.showHelpButton || false,
          isPdfError: errorData.isPdfError || false,
        };

        setUploadError(uploadErrorObj);

        // Salvar no localStorage para persistir entre recarregamentos
        localStorage.setItem('uploadError', JSON.stringify(uploadErrorObj));

        // NÃO fechar o modal para que o usuário veja a mensagem de erro
      } else {
        // Para outros tipos de erro, exibir toast normalmente
        toastMsg(ToastType.Error, error?.response?.data?.errors || error?.message || 'Erro desconhecido');

        // Para erros não relacionados a PDF, também manter o modal aberto
        // para que o usuário possa corrigir e tentar novamente
      }
    } finally {
      setShowLoader(false);
    }
  }, [
    files,
    arquivoValido,
    t,
    quantidadem3,
    florestaSelect,
    fornecedorSelect,
    dataAtual,
    materiasPrima,
    materiasPrimaTons, // NOVO
    supplyChainSpecie,
    atualizarSpecie,
    supplyChainProductSaveId,
    setShowLoader,
    setDataAtual,
    getTipoDocumento,
    setUploadError,
    handleCloseModal,
  ]);

  const downloadCar = () => {
    window.open(linkDoc, '_blank');
  };

  const downloadDoc = (path?: string) => {
    const docPath = path || filePath;
    if (docPath) {
      window.open(docPath, '_blank');
    }
  };

  const getDocumentNameLimited = (name: string): string => {
    if (name.length <= 45) {
      return name;
    }

    // Extract the file extension
    const lastDotIndex = name.lastIndexOf('.');
    const extension = lastDotIndex !== -1 ? name.substring(lastDotIndex) : '';

    // Calculate how much of the name we can keep
    const maxNameLength = 45 - extension.length;

    if (maxNameLength <= 0) {
      // If extension is too long, just truncate the whole name
      return name.substring(0, 45);
    }

    // Truncate name and append original extension
    return name.substring(0, maxNameLength) + extension;
  };

  const getDocumentName = (path: string): string => {
    const parts = path.split('/');
    if (parts.length > 0) {
      return getDocumentNameLimited(parts[parts.length - 1]);
    }
    return '';
  };

  async function atualizrDataVencimento(): Promise<void> {
    const newFiles = files.map((file) => {
      return file;
    });
    setFiles([...newFiles]);
    validateFiles([...newFiles]);
  }

  const handleChangeMateriaPrima = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    const newMateriaPrima = [...materiasPrima];
    const newMateriaPrimaTons = [...materiasPrimaTons];

    let value = event.target.value.replace('.', ',');
    // Não permitir valores como ",0" - limpar se for apenas vírgula seguida de zeros
    if (value === ',0' || value === ',00' || value === ',000') {
      value = '';
    }

    // Atualizar ambos os arrays para manter sincronização
    newMateriaPrima[index].quantidade = value;
    setMateriasPrima(newMateriaPrima);

    if (newMateriaPrimaTons[index]) {
      newMateriaPrimaTons[index].quantidade = value;
      setMateriasPrimaTons(newMateriaPrimaTons);
    }
  };

  // NOVA: Função para lidar com mudanças nas quantidades em toneladas
  const handleChangeMateriaPrimaTons = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    const newMateriaPrima = [...materiasPrima];
    const newMateriaPrimaTons = [...materiasPrimaTons];

    let value = event.target.value.replace('.', ',');
    // Não permitir valores como ",0" - limpar se for apenas vírgula seguida de zeros
    if (value === ',0' || value === ',00' || value === ',000') {
      value = '';
    }

    // Atualizar ambos os arrays para manter sincronização
    if (newMateriaPrima[index]) {
      newMateriaPrima[index].quantidade_tons = value;
      setMateriasPrima(newMateriaPrima);
    }

    if (newMateriaPrimaTons[index]) {
      newMateriaPrimaTons[index].quantidade_tons = value;
      setMateriasPrimaTons(newMateriaPrimaTons);
    }
  };

  return (
    <CustomCard
      cy="card-test-product"
      renderBody={
        <Row>
          <label className="titleCard">{t('titles.specieUsed')}</label>

          <Col md={12} className="mt-2">
            <Select
              id={`fornecedorSelect${t('language') || supplyChainId}`}
              renderKey
              loadOptions={selectFornecedores}
              cacheOptions
              defaultOptions
              title={`${t('titles.suppliers')}*`}
              placeholder={t('labels.selectSupplier')}
              noOptionsMessage={() => t('labels.noOptions')}
              loadingMessage={() => 'Carregando...'}
              onChange={handleChangeFornecedor}
              value={
                Object.values(fornecedorSelect || {}).length
                  ? { value: fornecedorSelect?.value, label: fornecedorSelect?.label }
                  : null
              }
              cy="test-selectTypeFornecedor"
            />
          </Col>
          <Row style={{ marginLeft: '0px' }}>
            <Col md={6} className="mt-2">
              <Select
                id={fornecedorSelect?.value}
                renderKey
                loadOptions={selectFlorestas}
                cacheOptions
                defaultOptions
                isDisabled={!fornecedorSelect?.value}
                title={t('labels.wild') + '*'}
                placeholder={t('labels.selectWild')}
                noOptionsMessage={() => t('labels.noOptions')}
                loadingMessage={() => 'Carregando...'}
                onChange={handleChangeFloresta}
                value={
                  Object.values(florestaSelect || {}).length
                    ? { value: florestaSelect?.value, label: florestaSelect?.label }
                    : null
                }
                cy="test-selectTypeFornecedor"
              />
            </Col>
            {linkDoc && (
              <Col md={6} className="mt-2" style={{ paddingTop: '34px' }}>
                <Button variant="link" onClick={downloadCar}>
                  {t('labels.downloadDocCAR')}
                </Button>
              </Col>
            )}
          </Row>
          {florestaSelect?.value && (
            <Row style={{ marginLeft: '0px' }}>
              <Col md={3} className="mt-2">
                {t('labels.selectMatPrima')}
              </Col>
              <Col md={3} className="mt-2">
                {t('labels.scientificName')}
              </Col>
              <Col md={2} className="mt-2">
                {t('labels.productQuantityM3')}
              </Col>
              <Col md={2} className="mt-2">
                {t('labels.quantityMatPrimaTons')}
              </Col>
              <Col md={2} className="mt-2">
                {/* Espaço para ações */}
              </Col>
            </Row>
          )}
          {materiasPrima?.map((mp, index) => (
            <Row key={mp.id} style={{ marginLeft: '0px' }}>
              <Col md={3} className="mt-2">
                <CustomInput
                  cy={'test-materiaPrima' + mp.id}
                  id={'materiaPrima' + mp.id}
                  name={'materiaPrima' + mp.id}
                  label=""
                  value={mp.nome}
                  type="text"
                  disabled
                  maxLength={255}
                />
              </Col>

              <Col md={3} className="mt-2">
                <CustomInput
                  cy={'test-cientifico' + mp.id}
                  id={'cientifico' + mp.id}
                  name={'cientifico' + mp.id}
                  value={mp.nome_cientifico}
                  type="text"
                  disabled
                  maxLength={255}
                />
              </Col>
              <Col md={2} className="mt-2">
                <MaskedInput
                  value={mp.quantidade}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChangeMateriaPrima(index, e)}
                  mask={(rawValue) => {
                    const digits = rawValue.replace(/[^\d]/g, '');

                    // Se não há dígitos, não aplicar máscara
                    if (digits.length === 0) {
                      return false;
                    }

                    const decimalPart = [/\d/, /\d/, /\d/]; // Sempre 3 casas decimais

                    // Ajusta a parte inteira com base na quantidade de dígitos
                    const integerPart = [];
                    for (let i = 0; i < digits.length - 3; i++) {
                      integerPart.push(/\d/);
                      // Adiciona ponto como separador de milhar após cada 3 dígitos
                      if ((digits.length - 3 - i) % 3 === 1 && i < digits.length - 4) {
                        integerPart.push('.');
                      }
                    }

                    return [...integerPart, ',', ...decimalPart];
                  }}
                  guide={false}
                  placeholderChar={'\u2000'}
                  placeholder={t('labels.productQuantityM3')}
                  className="form-control"
                />
              </Col>

              {/* NOVO CAMPO TONELADAS */}
              <Col md={2} className="mt-2">
                <MaskedInput
                  value={materiasPrima[index]?.quantidade_tons || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChangeMateriaPrimaTons(index, e)}
                  mask={(rawValue) => {
                    const digits = rawValue.replace(/[^\d]/g, '');

                    // Se não há dígitos, não aplicar máscara
                    if (digits.length === 0) {
                      return false;
                    }

                    const decimalPart = [/\d/, /\d/, /\d/]; // Sempre 3 casas decimais

                    // Ajusta a parte inteira com base na quantidade de dígitos
                    const integerPart = [];
                    for (let i = 0; i < digits.length - 3; i++) {
                      integerPart.push(/\d/);
                      // Adiciona ponto como separador de milhar após cada 3 dígitos
                      if ((digits.length - 3 - i) % 3 === 1 && i < digits.length - 4) {
                        integerPart.push('.');
                      }
                    }

                    return [...integerPart, ',', ...decimalPart];
                  }}
                  guide={false}
                  placeholderChar={'\u2000'}
                  placeholder={t('labels.quantityMatPrimaTons')}
                  className="form-control"
                />
              </Col>

              <Col md={2} className="mt-2">
                {/* Espaço para botões de ação futuros */}
              </Col>
            </Row>
          ))}
          <Col md={12} className="mt-2">
            <Card>
              <Modal
                show={show.show}
                handleClose={() => setShow({ ...show, show: false })}
                title={t('modal.removeFlorestaTitle')}
                size="lg"
                className="styleModalConfirm"
              >
                <Container>
                  <Row className="mt-4 p-2">
                    <Col className="d-flex align-items-center ">
                      <h6>
                        <br />
                        {t('modal.removeFlorestaSubTitle') + show.titulo + '?'}
                        <br />
                        {t('modal.removeProductDescription')}
                      </h6>
                    </Col>
                  </Row>
                </Container>

                <Row className="mt-4">
                  <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
                    <CustomButton
                      cy="btn-cancel"
                      type="button"
                      variant="outline-green"
                      onClick={() => {
                        setShow(false);
                      }}
                    >
                      {t('buttons.cancel')}
                    </CustomButton>
                    <CustomButton
                      cy="btn-save"
                      type="button"
                      variant="danger"
                      onClick={() => handleDeleteDocument(show.idDoc)}
                    >
                      {t('buttons.delete')}
                    </CustomButton>
                  </Col>
                </Row>
              </Modal>

              <CardContent>
                <div>
                  <div className="flex items-center gap-2">
                    <label className="text-sm">
                      {t('modal.anexarNota')}
                      <br />
                    </label>
                  </div>

                  <div
                    className="border-2 border-dashed rounded-lg p-6 text-center"
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                  >
                    <div
                      className="flex flex-col items-center gap-2"
                      style={{
                        display: 'flex',
                        alignContent: 'flex-start',
                        flexDirection: 'row',
                        alignItems: 'flex-start',
                        flexWrap: 'nowrap',
                        justifyContent: 'center',
                      }}
                    >
                      <div>
                        <Upload className="h-10 w-10 text-muted-foreground" />
                      </div>
                      <div style={{ marginLeft: '40px' }}>
                        <p>{t('labels.selecioneArquivo')}</p>
                        <p className="text-sm text-muted-foreground">PDF File {t('exceptions.maxSizeDocment')}</p>
                      </div>
                      <div style={{ marginLeft: '50px' }}>
                        <Button
                          onClick={() => document.getElementById(`file-upload${supplyChainId}`)?.click()}
                          variant="outline-success"
                          className="mt-2"
                        >
                          {t('buttons.selectFile')}
                        </Button>
                      </div>
                    </div>
                    <div>
                      <input
                        id={'file-upload' + supplyChainId}
                        type="file"
                        className="hidden"
                        onChange={handleFileSelect}
                        accept=".pdf"
                        multiple
                      />
                    </div>
                  </div>
                </div>

                {files.length > 0 && (
                  <div className="space-y-4" style={{ marginTop: '20px' }}>
                    <h3 className="font-bold text-sm">{t('labels.labelArquivoAdicionado')}</h3>
                    <div className="border rounded-lg" style={{ fontSize: '12px' }}>
                      <div
                        className="grid grid-cols-12 border-b bg-muted/50"
                        style={{ paddingTop: '10px', paddingBottom: '10px' }}
                      >
                        <div className="col-span-1" style={{ display: 'flex', justifyContent: 'center' }}>
                          {t('table.type')}
                        </div>
                        <div className="col-span-6">{t('table.name')}</div>
                        <div className="col-span-2">{t('table.size')}</div>
                        <div className="col-span-2">{t('table.dtEmissao')}</div>
                        <div className="col-span-1" style={{ display: 'flex', justifyContent: 'center' }}>
                          {t('modal.modalTitleActions')}
                        </div>
                      </div>
                      {files.map((file, index) => (
                        <div key={index} className="grid grid-cols-12 items-center">
                          <div className="col-span-1" style={{ display: 'flex', justifyContent: 'center' }}>
                            <FileIcon className="h-6 w-6" />
                          </div>
                          <div className="col-span-6 flex items-center gap-2">
                            <Button variant="link" onClick={() => downloadDoc(file.path)}>
                              {file.path ? getDocumentName(file.path || '') : getDocumentNameLimited(file.file.name)}
                            </Button>
                          </div>
                          <div className="col-span-2">
                            {file.tamanho_arquivo ? file.tamanho_arquivo : formatFileSize(file.file.size)}
                          </div>
                          <div className="col-span-2" style={{ paddingTop: '10px', fontSize: '12px' }}>
                            <DatePicker
                              onChange={(start) => {
                                if (start) {
                                  file.vencimento = new Date(start.toString());
                                }
                                atualizrDataVencimento();
                              }}
                              selected={file.vencimento || new Date()}
                              className="dt-picker"
                            />
                          </div>
                          <div className="col-span-1" style={{ display: 'flex', justifyContent: 'center' }}>
                            <Button
                              variant="ghost"
                              title={t('titles.removeDocument')}
                              onClick={async () => await removeFile(index)}
                            >
                              <Image src={InactiveUser} height={20} />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                <div className="d-flex justify-content-end" style={{ marginTop: '20px' }}>
                  <Button variant="outline-secondary" onClick={handleSubmitFornecedor}>
                    {t('buttons.onlySave')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </Col>
        </Row>
      }
    />
  );
};

export default CardSupplySpecie;
