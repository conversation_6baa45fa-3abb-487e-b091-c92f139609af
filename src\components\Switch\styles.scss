.input-switch {
  input {
    background-image: url('../../statics/icons/switch.svg');
    border-radius: 2.8rem;
    &:focus {
      box-shadow: 0.5rem var(--tail-500);
      border-color: var(--tail-500);
      outline: var(--tail-200) 0.125rem solid;
    }
    &:disabled,
    &.disabled {
      color: var(--white-100);
      pointer-events: none;
      border: 0.12rem solid var(--gray-200);
      background-image: url('../../statics/icons/switch-disabled.svg');
    }
    &:checked {
      background-color: var(--tail-500);
      border-color: var(--tail-500);
      background-image: url('../../statics/icons/switch-active.svg');
      &[disabled] {
        background-color: var(--gray-200);
        border-color: var(--gray-200);
      }
    }
  }
  label {
    color: var(--gray-800);
    line-height: 1.25rem;
    margin-left: 0;
    font-size: 0.81rem;
    font-style: normal;
    font-weight: var(--is-400);
  }
}

.switch-error {
  display: block;
  color: var(--red-500);
  font-size: 0.8rem;
  margin-top: 0;
  border-color: transparent;
}
