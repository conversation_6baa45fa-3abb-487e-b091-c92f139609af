.containerModal {
  .colButtons {
    display: flex;
    justify-content: end;
    padding: 0 1.4rem;
  }

  .textsModal {
    padding: 0 1rem;
    margin-bottom: 1.8rem;
    h6 {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 600;
      font-size: 16px;
      line-height: 20px;
      text-transform: uppercase;
      color: #54666f;
    }
    h3 {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 600;
      font-size: 24px;
      line-height: 140%;
      color: #201e40;
    }
  }
  a {
    color: #18b680;
    margin-top: 3px;
  }

  span {
    margin-top: 3px;
  }

  .textsModalApprove {
    h3 {
      color: var(--basics-secondary, #201e40);
      text-align: center;
      font-size: 1.5rem;
      font-family: Poppins;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }
    p {
      color: var(--basics-secondary, #201e40);
      text-align: center;
      font-size: 1rem;
      font-family: Poppins;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    button {
      min-height: 3rem;
      padding: 0.9375rem 4.25rem;
    }
  }

  .alignChecks {
    columns: 3;
    column-gap: 20px;
    margin-bottom: 1rem;
    .text-checked {
      display: flex;
      gap: 0.438rem;
      align-items: center;
      padding: 0 1rem;
      font-family: 'Poppins';
      font-size: 14px;

      input[type='checkbox'] {
        margin-top: 0.2rem;
        position: relative;
        font-size: 10px;
        cursor: pointer;
        display: flex;
        align-items: flex-start;
        height: 16px;
        color: rgb(0, 0, 0);
        width: 1rem;
      }

      input[type='checkbox']::before {
        content: ' ';
        display: inline-block;
        vertical-align: middle;
        min-height: 16px;
        min-width: 16px;
        background-color: #ffffff;
        border-color: #bac5c5;
        border-width: 1px;
        border-style: solid;
        border-radius: 2px;
        box-shadow: none;
      }

      input[type='checkbox']:checked::before {
        content: ' ';
        display: inline-block;
        vertical-align: middle;
        min-height: 16px;
        min-width: 16px;
        border-color: #bac5c5;
        background-color: #18b680;
        border-width: 1px;
        border-style: solid;
        border-radius: 2px;
        box-shadow: none;
      }

      input[type='checkbox']:checked::after {
        content: ' ';
        background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MTIgNTEyIj48cGF0aCBmaWxsPSIjZmZmZmZmIiBkPSJNMTczLjg5OCA0MzkuNDA0bC0xNjYuNC0xNjYuNGMtOS45OTctOS45OTctOS45OTctMjYuMjA2IDAtMzYuMjA0bDM2LjIwMy0zNi4yMDRjOS45OTctOS45OTggMjYuMjA3LTkuOTk4IDM2LjIwNCAwTDE5MiAzMTIuNjkgNDMyLjA5NSA3Mi41OTZjOS45OTctOS45OTcgMjYuMjA3LTkuOTk3IDM2LjIwNCAwbDM2LjIwMyAzNi4yMDRjOS45OTcgOS45OTcgOS45OTcgMjYuMjA2IDAgMzYuMjA0bC0yOTQuNCAyOTQuNDAxYy05Ljk5OCA5Ljk5Ny0yNi4yMDcgOS45OTctMzYuMjA0LS4wMDF6Ii8+PC9zdmc+');
        background-repeat: no-repeat;
        background-size: 10px 10px;
        background-position: center center;
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 0px;
        left: 0px;
        top: 0px;
        text-align: center;
        background-color: transparent;
        font-size: 10px;
        height: 16px;
        width: 16px;
      }
    }
  }

  .privacy-policy-content {
    h1,
    h2,
    h3 {
      color: #201e40;
      margin-bottom: 10px;
      font-weight: 600;
    }

    h1 {
      font-size: 16px;
    }

    h2 {
      font-size: 14px;
    }

    h3 {
      font-size: 13px;
    }

    p {
      margin-bottom: 8px;
      color: #54666f;
    }

    ul,
    ol {
      margin-left: 15px;
      margin-bottom: 10px;

      li {
        margin-bottom: 5px;
        color: #54666f;
      }
    }

    strong {
      color: #201e40;
    }

    hr {
      margin: 15px 0;
      border-color: #ddd;
    }
  }
}

.modal.show .modal-dialog {
  transform: none;
  display: flex;
  height: 90%;
  align-items: center;
}
