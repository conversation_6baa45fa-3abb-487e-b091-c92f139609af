import React, { useState } from 'react';
import { Table } from 'rsuite';
import { Col, Container, Dropdown, Image, Row } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, ArrowRight } from '@mui/icons-material';
import EmptyState from '../../../../../components/EmptyState';
import '../styles.scss';
import InactiveUser from '../../../../../statics/BsXCircle.svg';
import SendEmail from '../../../../../statics/forward_to_inbox.png';
import AuthsService from '../../../../../services/auth.service';
import toastMsg, { ToastType } from '../../../../../utils/toastMsg';
import Modal from '../../../../../components/Modal';
import Button from '../../../../../components/Button';
import UsersService from '../../../../../services/users.service';

export const TableComponent = ({
  sortColumn,
  sortType,
  handleSortColumn,
  isMobile,
  data,
  tableColumns,
  page,
  lastPage,
  limit,
  handleChangeLimit,
  handleDownPage,
  handleUpPage,
  showLoader,
  idClient,
}: any): React.ReactElement => {
  const { Column, HeaderCell, Cell } = Table;
  const { t } = useTranslation();
  const history = useHistory();
  const [showModal, setShowModal] = useState<any>({ show: false, id: null, customerId: null });

  const renderNameCompanies = (companies: any, isTitle = false): string => {
    if (!companies) return '';

    const values = companies.map((element: any) =>
      element.cliente?.name ? `${element.cliente?.name} ` : `${element.fornecedor?.name}`
    );

    if (values.join(', ').length > 60 && isTitle) {
      return values.join(', ').substring(0, 60).concat('...');
    }

    return values.join(', ');
  };

  const resetPassword = async (email: string): Promise<void> => {
    try {
      await AuthsService.sendEmailForCustomer(email);
      toastMsg(ToastType.Success, t('response.emailSendSucess'));
    } catch (e) {
      toastMsg(ToastType.Error, t('response.emailNotExists'));
    }
  };

  const handleDelete = async (id: string, customerId: string): Promise<void> => {
    try {
      await UsersService.delete(id, customerId);
      toastMsg(ToastType.Success, t('response.deleteSuccess'));
      setShowModal({ show: false, id: null, customerId: null });
      history.go(0);
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    }
  };

  return (
    <Container className="containerBackgroundCustomers">
      <Modal
        className="styleModalConfirm"
        show={showModal.show}
        handleClose={() => setShowModal({ ...showModal, show: false })}
        title={t('titles.desvincular')}
        size="lg"
        isCrud
      >
        <Row>
          <Col className="d-flex align-items-center ">
            <p>{t('messages.desvincular')}</p>
          </Col>
        </Row>
        <Row className="mt-4">
          <Col className="d-flex align-items-center justify-content-end gap-2">
            <Button
              cy="btn-cancel"
              type="button"
              variant="no-color"
              onClick={() => {
                setShowModal({ ...showModal, show: false });
              }}
            >
              {t('buttons.cancel')}
            </Button>
            <Button
              cy="btn-save"
              type="button"
              onClick={() => {
                handleDelete(showModal.id, showModal.customerId);
              }}
              variant="success"
            >
              {t('buttons.desvincular')}
            </Button>
          </Col>
        </Row>
      </Modal>

      {data && data.length > 0 ? (
        <>
          <Table
            bordered
            cellBordered
            sortColumn={sortColumn}
            sortType={sortType}
            onSortColumn={handleSortColumn}
            autoHeight={isMobile}
            height={300}
            affixHorizontalScrollbar={!isMobile}
            data={data}
            className="table"
            rowClassName={(rowData: any) => rowData?.className || ''}
          >
            {tableColumns.map((column: any) => {
              const { field, headerName, color, cursor, textDecoration, fixed, ...rest } = column;

              if (field === 'company') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div key="company" role="presentation" title="company">
                            {renderNameCompanies(rowData.company, true)}
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              if (field === 'actions') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            <div
                              className="invisible"
                              onClick={() => resetPassword(rowData.email)}
                              role="presentation"
                              title="send"
                              style={{ cursor: 'pointer' }}
                            >
                              <Image src={SendEmail} height={22} />
                            </div>
                            <div
                              onClick={() => setShowModal({ show: true, id: rowData.id, customerId: idClient })}
                              role="presentation"
                              title={t('language') === 'BR' ? 'Inativar usuário' : 'Deactivate'}
                              style={{ cursor: 'pointer' }}
                            >
                              <Image src={InactiveUser} height={20} />
                            </div>
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              return (
                <>
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap />
                  </Column>
                </>
              );
            })}
          </Table>
          <div className="pagination">
            <div className="pageGroup">
              {!isMobile && <span>{t('pagination.clientsPerPage')}</span>}
              <Dropdown className="selectPerPage">
                <Dropdown.Toggle id="dropdown-limit">{limit}</Dropdown.Toggle>
                <Dropdown.Menu>
                  <Dropdown.Item onClick={() => handleChangeLimit(10)}>10</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(30)}>30</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(50)}>50</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(70)}>70</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(100)}>100</Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            </div>
            <div className="paginator">
              <span>
                {t('pagination.page')} {page} {t('pagination.of')} {lastPage}
              </span>
              <ArrowLeft className="navigator" onClick={handleDownPage} />
              <ArrowRight className="navigator" onClick={handleUpPage} />
            </div>
          </div>
        </>
      ) : (
        <>{!showLoader && <EmptyState text={t('labels.labelUserNotLinked')} secondaryText="" img={null} />}</>
      )}
    </Container>
  );
};
