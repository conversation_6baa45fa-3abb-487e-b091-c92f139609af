import { Links, Meta } from './IMeta';

export interface Type {
  id: number;
  description: string;
  description_ptbr: string;
}

export interface NewQuote {
  company_uuid: string;
  product_type_id: number;
  description_product: string;
  unit_measurement: string;
  thickness: number;
  length: number;
  width: number;
  volume: number;
  description: string;
}

export interface Status {
  id: number;
  description: string;
  description_ptbr: string;
}

export interface Company {
  id: number;
  uuid: string;
  name: string;
}

export interface User {
  usuario_id: number;
  nom_usuario: string;
}

export interface IQuoteComplete {
  data: IQuoteComplete | PromiseLike<IQuoteComplete>;
  id: number;
  created_at: string;
  uuid: string;
  company_id: number;
  product_type_id: number;
  status_id: number;
  thickness?: any;
  length?: any;
  width?: any;
  volume?: number;
  description: string;
  description_product: string;
  refused_by?: any;
  unit_measurement: string;
  type: Type;
  status: Status;
  company: Company;
  user: User;
  documents: any;
  specie?: string;
  species?: any;
  qualities?: any;
}

export interface IQuote {
  data: IQuoteComplete[];
  links: Links;
  meta: Meta;
}
