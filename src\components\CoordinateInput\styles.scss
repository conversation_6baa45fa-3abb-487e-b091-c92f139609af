.coordinate-input {
  font-weight: var(--is-400);
  
  &__error {
    border-color: var(--red-500) !important;
    color: var(--gray-800);
    font-weight: var(--is-400);
    background-image: url('../../statics/icons/input-error.svg');
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    padding-right: calc(1.5em + 0.75rem);
    
    &:focus {
      box-shadow: 0.5rem var(--teal-200);
      border-color: var(--red-500) !important;
      outline: var(--teal-200) 0.125rem solid;
    }
  }
  
  .form-control {
    &:focus {
      box-shadow: 0.5rem var(--teal-200);
      border-color: var(--gray-200);
      outline: var(--teal-200) 0.125rem solid;
    }
  }
  
  &__label-error {
    color: var(--red-500);
    font-weight: var(--is-400);
  }
  
  &__label-disabled {
    color: var(--gray-400);
    font-weight: var(--is-400);
  }
  
  &__invalid-feedback {
    color: var(--red-500);
    font-weight: var(--is-400);
    font-size: 0.875rem;
    margin-top: 0.25rem;
  }
}
