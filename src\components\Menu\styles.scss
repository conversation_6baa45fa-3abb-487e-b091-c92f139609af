.menu {
  transition: 0.2s ease-in;
  padding-left: 1rem;

  &__brand {
    padding-top: 0.2rem;
    padding-bottom: 2rem;

    svg {
      min-width: 10rem;
      max-width: 10rem;
    }
  }

  &__routes {
    &--ul {
      line-height: 2.8rem;
      list-style-type: none;
      padding-left: 0;
    }

    &--link {
      color: var(--white-100);
      font-weight: var(--is-400);
      padding-left: 1rem;
      text-decoration: none;

      &:hover,
      &:focus {
        color: var(--white-100);
      }
    }

    &--title {
      color: var(--white-100);
      display: block;
      font-size: 1.2rem;
      margin-left: 0.5rem;
      border-bottom: 1px solid #18b680;
      text-decoration: none;
    }

    // &--active {
    //   background-color: var(--teal-500);
    //   color: var(--white-100) !important;
    //   font-weight: var(--is-500) !important;
    // }
  }
}

.min-width {
  transition: 0.1s ease-out;
  min-width: 0;
  max-width: 0;

  .sidebar__brand,
  .sidebar__routes {
    display: none;
  }
}
