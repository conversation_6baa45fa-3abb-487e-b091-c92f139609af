/*!
 * Mixins de estilos.
*/

@mixin btnDisabled {
  &:disabled,
  &.disabled {
    color: var(--white-100);
    pointer-events: none;
    background-color: var(--gray-200);
    border: 0.12rem solid var(--gray-200);
  }
}

@mixin btnDisabledOutline {
  &:disabled,
  &.disabled {
    color: var(--gray-300);
    pointer-events: none;
    background-color: var(--white-100);
    border: 0.12rem solid var(--gray-300);
  }
}

/*!
 * Classe btn do bootstrap.
*/

.btn {
  font-size: 1rem;
  font-weight: 700;
  font-style: normal;
  font-family: 'Poppins';
  padding: 0.8rem 1rem 0.8rem 1rem;
  display: flex;
  align-items: center;
  height: 2.12rem;
  border-radius: 0.25rem;
  border: 0.12rem solid transparent;
  width: auto;

  svg {
    color: var(--white-100);
  }
}

/*!
 * Configurações adicionais.
*/

.btn-border {
  border-radius: 2.81rem;
}

.btn-icon-left {
  svg {
    margin-right: 0.4rem;
  }
}

.btn-icon-right {
  svg {
    margin-left: 0.4rem;
  }
}

/*!
 * Lista de botões padrões.
*/

.btn-primary {
  color: var(--white-100);
  background-color: var(--green-400);

  &:hover {
    background-color: var(--green-800);
    border-color: var(--green-500);
  }

  &:focus {
    background-color: var(--green-600);
    border-color: var(--green-500);
  }

  &:active {
    background-color: var(--green-500);
    border-color: transparent;
  }

  @include btnDisabled;
}

.btn-secondary {
  color: var(--white-100);
  background-color: #201e40;

  &:hover {
    background-color: var(--gray-700);
    border-color: var(--gray-700);
  }

  &:focus {
    background-color: var(--gray-600);
    border: 0.12rem solid var(--gray-900);
  }

  &:active {
    background-color: var(--gray-700);
  }

  @include btnDisabled;
}

.btn-download {
  background-color: #201e40;
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  text-align: center;
  color: var(--white-100);
  border-radius: 8px;
  &:hover {
    background-color: var(--gray-700);
    border-color: var(--gray-700);
  }

  &:focus {
    background-color: var(--gray-600);
    border: 0.12rem solid var(--gray-900);
  }

  &:active {
    background-color: var(--gray-700);
  }

  @include btnDisabled;
}

.btn-add-email {
  background-color: #201e40;
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 600;
  font-size: 13px;
  white-space: nowrap;
  line-height: 21px;
  text-align: center;
  color: var(--white-100);
  margin-top: 0.1rem;
  &:hover {
    background-color: var(--gray-700);
    border-color: var(--gray-700);
    color: var(--white-100);
  }

  &:focus {
    outline: none;
    box-shadow: none;
  }
  &:active {
    outline: none;
    box-shadow: none;
  }

  svg {
    color: var(--white-100);
    margin-right: 0.5rem;
  }

  @include btnDisabled;
}

.btn-success {
  color: var(--white-100);
  background-color: var(--green-400);

  &:hover {
    background-color: var(--green-700);
    border-color: var(--green-700);
  }

  &:focus {
    background-color: var(--green-500);
    border: 0.12rem solid var(--green-900);
  }

  &:active {
    background-color: var(--green-700);
  }

  @include btnDisabled;
}

.btn-danger {
  color: var(--white-100);
  background-color: var(--red-500);

  &:hover {
    background-color: var(--red-700);
    border-color: var(--red-700);
  }

  &:focus {
    background-color: var(--red-500);
    border: 0.12rem solid var(--red-900);
  }

  &:active {
    background-color: var(--red-700);
  }

  @include btnDisabled;
}

/*!
 * Lista de botões outline.
*/

.btn-outline-primary {
  background-color: var(--white-100);
  border-color: var(--teal-500);
  color: var(--teal-500);

  &:hover {
    color: var(--white-100);
    background-color: var(--teal-500);
    border-color: var(--teal-500);

    svg {
      color: var(--white-100);
    }
  }

  &:focus {
    color: var(--teal-500);
    background-color: var(--teal-100);
    border-color: var(--teal-600);

    svg {
      color: var(--teal-500);
    }
  }

  &:active {
    color: var(--white-100);
    background-color: var(--teal-500);
    border-color: var(--teal-500);

    svg {
      color: var(--white-100);
    }
  }

  @include btnDisabledOutline;

  svg {
    color: var(--teal-500);
  }
}

.btn-outline-warning {
  background-color: var(--white-100);
  border-color: var(--yellow-500);
  color: var(--yellow-500);

  &:hover {
    color: var(--white-100);
    background-color: var(--yellow-500);
    border-color: var(--yellow-500);

    svg {
      color: var(--white-100);
    }
  }

  &:focus {
    color: var(--yellow-500);
    background-color: var(--yellow-100);
    border-color: var(--yellow-600);

    svg {
      color: var(--yellow-500);
    }
  }

  &:active {
    color: var(--white-100);
    background-color: var(--yellow-500);
    border-color: var(--yellow-500);

    svg {
      color: var(--white-100);
    }
  }

  @include btnDisabledOutline;

  svg {
    color: var(--yellow-500);
  }
}

.btn-outline-secondary {
  background-color: transparent;
  border-radius: 0.25rem;
  border: 2px solid var(--basics-secondary, #201e40);
  color: var(--gray-700);

  @include btnDisabledOutline;

  svg {
    color: var(--gray-500);
  }
}

.btn-outline-green {
  background-color: transparent;
  border: 2px solid var(--basics-primary, #18b680);
  color: #18b680;

  &:hover {
    color: var(--white-100);
    border-color: #18b680;
    background-color: #18b680;

    svg {
      color: var(--white-100);
    }
  }
  &:focus {
    outline: none;
    box-shadow: none;
  }
  &:active {
    outline: none;
    box-shadow: none;
  }

  @include btnDisabledOutline;
}

.btn-upload {
  border: dashed 1px var(--gray-300);
  height: 100;
  font-size: 17px;
  color: var(--gray-400);
  background-color: var(--white-100);
  white-space: nowrap;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  &:hover {
    color: var(--white-100);
    background-color: var(--green-500);
    border-color: var(--green-500);
  }
}

.btn-outline-success {
  background-color: var(--white-100);
  border-color: var(--green-500);
  color: var(--green-500);

  &:hover {
    color: var(--white-100);
    background-color: var(--green-500);
    border-color: var(--green-500);

    svg {
      color: var(--white-100);
    }
  }

  &:focus {
    color: var(--green-500);
    background-color: var(--green-100);
    border-color: var(--green-600);

    svg {
      color: var(--green-500);
    }
  }

  &:active {
    color: var(--white-100);
    background-color: var(--green-500);
    border-color: var(--green-500);

    svg {
      color: var(--white-100);
    }
  }

  @include btnDisabledOutline;

  svg {
    color: var(--green-500);
  }
}

.btn-outline-danger {
  background-color: transparent;
  border-color: var(--red-500);
  color: var(--red-500);

  &:hover {
    color: var(--white-100);
    background-color: var(--red-500);
    border-color: var(--red-500);

    svg {
      color: var(--white-100);
    }
  }

  &:focus {
    color: var(--red-500);
    background-color: var(--red-100);
    border-color: var(--red-600);

    svg {
      color: var(--red-500);
    }
  }

  &:active {
    color: var(--white-100);
    background-color: var(--red-500);
    border-color: var(--red-500);

    svg {
      color: var(--white-100);
    }
  }

  @include btnDisabledOutline;

  svg {
    color: var(--red-500);
  }
}

.btn-transparent {
  background: transparent;
  color: var(--gray-900);
  border: none;
  font-weight: var(--is-400);
  padding-left: 0;

  svg {
    fill: var(--gray-900);
  }

  &:hover,
  &:active,
  &:focus {
    background-color: transparent;
    color: var(--gray-900);
  }
}

.btn-tabs {
  color: var(--teal-500);
  border: none;
  border-bottom: 0.063rem solid var(--teal-600);
  background-color: var(--white-500);
  margin-top: 1rem;
  border-radius: 0;

  &:hover {
    background-color: var(--teal-400);
    border-color: var(--teal-600);
  }
}

.btn-active {
  color: var(--white-100);
  border: none;
  border-bottom: 0.063rem solid var(--white-600);
  background-color: var(--teal-500);
  margin-top: 1rem;
  border-radius: 0;
}

.btn-transparent-green {
  background: transparent;
  color: var(--green-400);
  border: none;
  font-weight: var(--is-700);
  padding-left: 0;

  svg {
    fill: var(--gray-900);
  }

  &:hover,
  &:active,
  &:focus {
    background-color: transparent;
    color: var(--gray-900);
  }
}

.btn-transparent-gray {
  display: flex;
  align-items: center;
  background: transparent;
  color: var(--gray-900);
  border: none;
  font-weight: var(--is-700);
  padding-left: 0;

  svg {
    fill: var(--gray-900);
  }

  &:hover,
  &:active,
  &:focus {
    background-color: transparent;
    color: var(--gray-900);
  }
}
