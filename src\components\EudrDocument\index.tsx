import React, { useState } from 'react';
import { Table } from 'rsuite';
import { Col, Container, Image, Row } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import EmptyState from '../../components/EmptyState';
import EmptyStateImage from '../../statics/emptyState.png';
import { FiEdit2 } from 'react-icons/fi';
import InactiveUser from '../../statics/BsXCircle.svg';
import Modal from '../../components/Modal';
import Button from '../../components/Button';
import { useLoader } from '../../contexts/LoaderContext';
import toastMsg, { ToastType } from '../../utils/toastMsg';
import CompaniesService from '../../services/companies.service';
import './styles.scss';

export const EudrDocument = ({
  sortColumn,
  sortType,
  handleSortColumn,
  isMobile,
  data,
  tableColumns,
  showLoader,
}: any): React.ReactElement => {
  const { Column, HeaderCell, Cell } = Table;
  const { t } = useTranslation();
  const history = useHistory();
  const [show, setShow] = useState<any>({ show: false, client: null });
  const { setShowLoader } = useLoader();

  const handleDeleteProduct = async (client: any): Promise<void> => {
    if (!client?.uuid) {
      return;
    }

    try {
      setShowLoader(true);

      await CompaniesService.delete(client?.uuid);
      toastMsg(ToastType.Success, t('response.deleteSuccess'));
      history.push('/clients');
      setShowLoader(false);
      setShow(false);

      history.push('/clients', { reload: true });
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  };

  return (
    <Container className="containerBackgroundCustomers">
      <Modal
        show={show.show}
        handleClose={() => setShow({ ...show, show: false })}
        title={t('modal.removeClientTitle')}
        size="lg"
        className="styleModalConfirm"
        colorIcon
      >
        <Container>
          <Row className="mt-4 p-2">
            <Col className="d-flex align-items-center ">
              <h6>
                {t('modal.removeClientSubTitle')}
                <br />
                {t('modal.removeProductDescription')}
              </h6>
            </Col>
          </Row>
        </Container>

        <Row className="mt-4">
          <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
            <Button
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={() => {
                setShow(false);
              }}
            >
              {t('buttons.cancel')}
            </Button>
            <Button cy="btn-save" type="button" variant="danger" onClick={() => handleDeleteProduct(show.client)}>
              {t('buttons.delete')}
            </Button>
          </Col>
        </Row>
      </Modal>

      {data.length > 0 ? (
        <>
          <Table
            bordered
            cellBordered
            sortColumn={sortColumn}
            sortType={sortType}
            onSortColumn={handleSortColumn}
            autoHeight={isMobile}
            height={500}
            affixHorizontalScrollbar={!isMobile}
            data={data}
            className="table"
            rowClassName={(rowData: any) => rowData?.className || ''}
          >
            {tableColumns.map((column: any) => {
              const { field, headerName, color, cursor, textDecoration, fixed, ...rest } = column;

              if (field === 'actions') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            <div
                              onClick={() => {
                                history.push(`/new-client/${rowData.id}`);
                              }}
                              role="presentation"
                              title={t('titles.editClient')}
                              style={{ cursor: 'pointer', color: '#494747' }}
                            >
                              <FiEdit2 size={20} color="green" />
                            </div>
                            <div
                              onClick={() => setShow({ show: true, client: rowData })}
                              role="presentation"
                              title={t('business.removeClient')}
                              style={{ cursor: 'pointer' }}
                            >
                              <Image src={InactiveUser} height={20} />
                            </div>
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              return (
                <>
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap />
                  </Column>
                </>
              );
            })}
          </Table>
        </>
      ) : (
        <>
          {!showLoader && (
            <EmptyState
              text={t('labels.labelCustomerNotFound')}
              secondaryText={t('labels.labelDescCustomerNotFound')}
              img={EmptyStateImage}
            />
          )}
        </>
      )}
    </Container>
  );
};
