import React, { useState } from 'react';
import { Row, Col, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import CustomInput from '../../../../components/Input/CustomInput';
import CompaniesService from '../../../../services/companies.service';
import { useLoader } from '../../../../contexts/LoaderContext';
import toastMsg, { ToastType } from '../../../../utils/toastMsg';
import InputMask from '../../../../components/InputMask';

interface IPropsCard {
  company: any;
  loadList: () => void;
  setValuesUpdate: () => void;
}

const CardInfoCompany = ({ company, loadList, setValuesUpdate }: IPropsCard): React.ReactElement => {
  const { t } = useTranslation();
  const { setShowLoader } = useLoader();
  const [razaoSocial, setRazaoSocial] = useState<string>('');
  const [fantasia, setFantasia] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [cnpj, setCnpj] = useState<string>('');
  const [address, setAddress] = useState<string>('');
  const [numero, setNumero] = useState<string>('');
  const [city, setCity] = useState<string>('');
  const [state, setState] = useState<string>('');
  const [inscricaoEstadual, setInscricaoEstadual] = useState<string>('');
  const [telefone, setTelefone] = useState<string>('');
  const [cep, setCep] = useState<string>('');

  const setUpdateValues = React.useCallback(async () => {
    setRazaoSocial(company.razao);
    setFantasia(company.fantasia);
    setEmail(company.email);
    setCnpj(company.cnpj);
    setAddress(company.address);
    setNumero(company.numero);
    setCity(company.city);
    setState(company.state);
    setInscricaoEstadual(company.inscricaoEstadual);
    setTelefone(company.telefone);
    setCep(company.cep);
  }, [company]);

  React.useEffect(() => {
    if (company !== undefined) {
      setUpdateValues();
    }
  }, [setUpdateValues, company]);

  const handleSubmit = React.useCallback(async () => {
    try {
      setShowLoader(true);

      const companyData: any = {
        name: razaoSocial,
        name_sender: fantasia,
        email: email,
        tax_identifier_number: cnpj,
        street: address,
        address_number: numero,
        phone: telefone,
        address_complement: inscricaoEstadual,
        zip_code: cep,
        city: city,
        state: state,
        rules: {
          customer: false,
          seller: true,
          trader: false,
        },
      };

      await CompaniesService.updateCompany(companyData, company.uuid);

      loadList();
      setValuesUpdate();
      //window.location.reload();
    } catch (error: any) {
      toastMsg(ToastType.Error, error?.response?.data?.errors);
    } finally {
      setShowLoader(false);
    }
  }, [
    razaoSocial,
    fantasia,
    email,
    cnpj,
    address,
    numero,
    city,
    state,
    inscricaoEstadual,
    telefone,
    cep,
    company.uuid,
    setShowLoader,
  ]);

  return (
    <>
      <Row style={{ marginLeft: 'auto' }}>
        <Col md={12} className="mt-2">
          <CustomInput
            cy="test-address"
            id="razaoSocial"
            name="address"
            label={`${t('labels.razaoSocial')}*`}
            placeholder={t('labels.sendRazaoSocial')}
            value={razaoSocial}
            onChange={(e) => {
              setRazaoSocial(e.target.value);
            }}
            type="text"
            maxLength={255}
          />
        </Col>
        <Col md={12} className="mt-2">
          <CustomInput
            cy="test-fantasia"
            id="fantasia"
            name="fantasia"
            label={`${t('labels.fantasia')}`}
            placeholder={t('labels.sendFantasia')}
            value={fantasia}
            onChange={(e) => {
              setFantasia(e.target.value);
            }}
            type="text"
            maxLength={255}
          />
        </Col>

        <Col md={8} className="mt-2">
          <CustomInput
            cy="test-email"
            id="email"
            name="email"
            label={`${t('placeholders.holderEmail')}*`}
            placeholder={t('placeholders.holderEmail')}
            value={email}
            onChange={(e) => {
              setEmail(e.target.value);
            }}
            type="text"
            maxLength={255}
          />
        </Col>
        <Col md={4} className="mt-2">
          <InputMask
            cy="test-fone"
            id="fome"
            label={`${t('table.phone')}*`}
            placeholder={t('labels.phoneContact')}
            mask="(99) 99999-9999"
            desc="(99) 99999-9999"
            value={telefone}
            onChange={(e) => {
              setTelefone(e.target.value);
            }}
          />
        </Col>

        <Col md={6} className="mt-2">
          <InputMask
            cy="test-cnpj"
            id="cnpj"
            label={`${t('labels.cnpj')}*`}
            placeholder={t('labels.sendCnpj')}
            value={cnpj}
            onChange={(e) => {
              setCnpj(e.target.value);
            }}
            mask="99.999.999/9999-99"
            desc="99.999.999/9999-99"
          />
        </Col>
        <Col md={6} className="mt-2">
          <CustomInput
            cy="test-ie"
            id="incricaoEstadual"
            name="incricaoEstadual"
            label={`${t('table.incricaoEstadual')}`}
            placeholder={t('labels.sendIncricaoEstadual')}
            value={inscricaoEstadual}
            onChange={(e) => {
              setInscricaoEstadual(e.target.value);
            }}
            type="text"
            maxLength={20}
          />
        </Col>

        <Col md={8} className="mt-2">
          <CustomInput
            cy="test-address"
            id="address"
            name="address"
            label={`${t('labels.fullAddress')}`}
            placeholder={t('labels.sendFullAddress')}
            value={address}
            onChange={(e) => {
              setAddress(e.target.value);
            }}
            type="text"
            maxLength={255}
          />
        </Col>
        <Col md={4} className="mt-2">
          <CustomInput
            cy="test-numero"
            id="numero"
            name="numero"
            label={t('labels.labelNumero')}
            placeholder={t('labels.digiteNumero')}
            value={numero}
            onChange={(e) => {
              setNumero(e.target.value);
            }}
            type="text"
            maxLength={20}
            msg={t('exceptions.numberAlreadyExists')}
          />
        </Col>

        <Col md={4} className="mt-2">
          <InputMask
            cy="test-cep"
            id="cep"
            label={`${t('labels.cep')}`}
            placeholder={t('labels.sendCep')}
            value={cep}
            onChange={(e) => {
              setCep(e.target.value);
            }}
            mask="99999-999"
            desc="99999-999"
          />
        </Col>
        <Col md={4} className="mt-2">
          <CustomInput
            cy="test-city"
            id="city"
            name="city"
            label={`${t('labels.city')}*`}
            placeholder={t('labels.sendCity')}
            value={city}
            onChange={(e) => {
              setCity(e.target.value);
            }}
            type="text"
            maxLength={20}
            msg={t('exceptions.numberAlreadyExists')}
          />
        </Col>
        <Col md={4} className="mt-2">
          <CustomInput
            cy="test-state"
            id="state"
            name="state"
            label={`${t('labels.state')}*`}
            placeholder={t('labels.sendState')}
            value={state}
            onChange={(e) => {
              setState(e.target.value);
            }}
            type="text"
            maxLength={20}
            msg={t('exceptions.numberAlreadyExists')}
          />
        </Col>
      </Row>

      <div className="d-flex justify-content-end" style={{ marginTop: '20px' }}>
        <Button variant="outline-secondary" onClick={handleSubmit}>
          {t('buttons.alterData')}
        </Button>
      </div>
    </>
  );
};

export default CardInfoCompany;
