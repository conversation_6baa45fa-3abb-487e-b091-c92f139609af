import React, { useEffect, useContext } from 'react';
import mixpanel, { Dict } from 'mixpanel-browser';

interface IContext {
  trackEvent: (eventName: string, properties: Dict) => void;
  identify: (identifier: string) => void;
  setPeople: (field: string, value: string) => void;
}

interface IProvider {
  children: React.ReactElement;
}

const AnalyticsContext = React.createContext<IContext>({} as IContext);

export function useAnalytics(): IContext {
  return useContext(AnalyticsContext);
}

export function AnalyticsProvider({ children }: IProvider): React.ReactElement {
  function trackEvent(eventName: string, properties: Dict): void {
    mixpanel.track(eventName, properties);
  }
  function identify(identifier: string): void {
    mixpanel.identify(identifier);
  }
  function setPeople(field: string, value: string): void {
    mixpanel.people.set(field, value);
  }

  useEffect(() => {
    if (process.env.REACT_APP_MIXPANEL_TOKEN) {
      mixpanel.init(process.env.REACT_APP_MIXPANEL_TOKEN);
    }
  }, []);

  const value = {
    identify,
    setPeople,
    trackEvent,
  };

  return <AnalyticsContext.Provider value={value}>{children}</AnalyticsContext.Provider>;
}
