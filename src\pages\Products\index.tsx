import React from 'react';
import { Row, Col, Image, Container } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import debounce from 'lodash.debounce';
import { format } from 'date-fns';
import { HiOutlinePlusSm } from 'react-icons/hi';
import { utcToZonedTime } from 'date-fns-tz';
import Section from '../../components/Section';
import Button from '../../components/Button';
import Text from '../../components/Text';
import Modal from '../../components/Modal';
import ProductService, { ResponseProduct } from '../../services/products.service';
import { useAnalytics } from '../../contexts/AnalyticsContext';
import { useAuth } from '../../contexts/AuthContext';
import { useLoader } from '../../contexts/LoaderContext';
import SearchIC from '../../statics/search.svg';
import FilterIC from '../../statics/filter_alt.svg';
import { ISelectOption } from '../../interfaces';
import renderSelectValues from '../../utils/renderSelectValues';
import { TableComponent } from './TableProducts';
import Select from '../../components/Select';
import { renderDefaultObjectsTable, renderPropertiesValues } from '../../utils/renderDefaultObjects';
import './styles.scss';
import toastMsg, { ToastType } from '../../utils/toastMsg';
import { IColumnsProps } from './utils';

const MyProducts: React.FunctionComponent = () => {
  const { t } = useTranslation();
  const { setShowLoader, showLoader } = useLoader();
  const [visibility, setVisibility] = React.useState<ISelectOption | null>(null);
  const { user } = useAuth();
  const [query, setQuery] = React.useState<string>('');
  const history = useHistory();

  const [isMobile, setIsMobile] = React.useState<boolean>(window.innerWidth <= 768);

  const columns: IColumnsProps[] = [
    {
      field: 'productType',
      headerName: t('labels.labelProduct'),
      fixed: true,
      color: '#201E40',
      flexGrow: 4,
      resizable: true,
    },
    {
      field: 'specie',
      headerName: t('labels.specie'),
      color: '#201E40',
      flexGrow: 6,
      fixed: false,
      resizable: true,
    },
    {
      field: 'qualities',
      headerName: t('table.grade'),
      color: '#201E40',
      flexGrow: 6,
      fixed: false,
      resizable: true,
    },
    {
      field: 'certificates',
      headerName: t('table.certificates'),
      color: '#201E40',
      flexGrow: 6,
      fixed: false,
      resizable: true,
    },
    {
      field: 'actions',
      headerName: t('modal.modalTitleActions'),
      color: '#201E40',
      flexGrow: 2,
      fixed: false,
      resizable: !!isMobile,
    },
  ];

  const [columnKeys] = React.useState(columns.map((column: any) => column.field));
  const [page, setPage] = React.useState(1);
  const [limit, setLimit] = React.useState(10);
  const [lastPage, setLastPage] = React.useState(1);
  const [tableLength, setTableLength] = React.useState(0);
  const [data, setData] = React.useState<any>([]);
  const [productsToDelete, setProductsToDelete] = React.useState<string[]>([]);
  const [isShowModal, setIsShowModal] = React.useState(false);
  const { trackEvent } = useAnalytics();

  const handleWindowSizeChange = (): void => {
    if (window.innerWidth <= 768) {
      setIsMobile(true);
    } else {
      setIsMobile(false);
    }
  };
  React.useEffect(() => {
    window.addEventListener('resize', handleWindowSizeChange);
  }, []);

  const tableColumns: any = columns.filter((column) => columnKeys.some((key) => key === column.field));

  const changeHandler = (event: any): any => {
    setQuery(event.target.value);
  };

  const debouncedChangeHandler = React.useMemo(() => debounce(changeHandler, 300), []);

  const getProducts = React.useCallback(async (): Promise<ResponseProduct> => {
    try {
      setShowLoader(true);

      const res = await ProductService.getProducts({
        page,
        companyId: String(user.default_company?.id) || '',
        query,
        perPage: limit,
        visibility: visibility ? visibility.value : '',
      });

      if (res) {
        return res;
      }

      return {} as ResponseProduct;
    } catch (error) {
      return {} as ResponseProduct;
    } finally {
      setShowLoader(false);
    }
  }, [setShowLoader, page, user.default_company?.id, query, limit, visibility]);

  const selectVisibility = React.useCallback(
    async (value: string): Promise<ISelectOption[]> => {
      const res = [
        {
          value: true,
          label: t('language') === 'USA' ? 'Visible' : 'Visível',
        },
        { value: false, label: t('language') === 'USA' ? 'Occult' : 'Oculto' },
      ];

      const options = res.map((item) => ({
        value: item.value,
        label: item.label,
      }));

      const newStatusContext = options.find((val) => val.value === visibility?.value);
      setVisibility(newStatusContext as ISelectOption);

      return renderSelectValues(value, options);
    },
    [t, visibility]
  );

  const loadList = React.useCallback(
    async (isCleaningUp = false): Promise<void> => {
      const res = await getProducts();

      if (res && !isCleaningUp) {
        setLastPage(res.meta?.last_page);
        setTableLength(res.meta?.total);
        const rows = res.data.map((element: any, index: number) => ({
          productType: t('language') === 'USA' ? element?.type?.description : element?.type?.description_ptbr,
          specie: renderDefaultObjectsTable(element.specie, t('language')) || t('labels.labelNotApply'),
          thickness: renderPropertiesValues(element.thickness) || t('labels.labelNotApply'),
          width: renderPropertiesValues(element.width) || t('labels.labelNotApply'),
          length: renderPropertiesValues(element.length) || t('labels.labelNotApply'),
          layers: Array.isArray(element?.layers)
            ? element?.layers?.map((x: any) => x).join(' | ')
            : t('labels.labelNotApply'),
          finishes: renderDefaultObjectsTable(element?.finishes, t('language')) || t('labels.labelNotApply'),
          qualities: renderDefaultObjectsTable(element?.quality, t('language')) || t('labels.labelNotApply'),
          certificates: renderDefaultObjectsTable(element?.certificates, t('language')) || t('labels.labelNotApply'),
          glue: renderDefaultObjectsTable(element?.glue, t('language')) || t('labels.labelNotApply'),
          created_at: format(utcToZonedTime(element.created_at, 'America/Sao_Paulo'), t('format.date')),
          status:
            element.visibility === 0 || !element.visibility
              ? t('language') === 'USA'
                ? 'Occult'
                : 'Oculto'
              : t('language') === 'USA'
              ? 'Visible'
              : 'Visível',
          className: index % 2 === 0 ? 'custom-row' : '',
          id: element.id,
          uuid: element.uuid,
        }));
        setData(rows);
      }
    },
    [getProducts, t]
  );

  React.useEffect(() => {
    let isCleaningUp = false;

    loadList(isCleaningUp);

    return () => {
      isCleaningUp = true;
    };
  }, [loadList]);

  const handleChangeLimit = (dataKey: number): void => {
    setPage(1);
    setLimit(dataKey);
    trackEvent('Products per page', {
      action: `See ${dataKey} per page`,
      param: 'page',
    });
  };

  const handleDownPage = (): void => {
    const newPage = page - 1;
    if (newPage >= 1) {
      setPage(newPage);
    }

    trackEvent('Products Previus Page', {
      action: `Clicou previus page`,
      param: 'Page',
    });
  };

  const handleUpPage = (): void => {
    const newPage = page + 1;
    if (page <= tableLength / limit) {
      setPage(newPage);
    }

    trackEvent('Order Next Page', {
      action: `Clicou next page`,
      param: 'Page',
    });
  };

  const handleSelectProducts = (uuid: string): void => {
    setProductsToDelete((prev) => {
      if (prev.find((item) => item === uuid)) {
        return prev.filter((item) => item !== uuid);
      }

      return [...prev, uuid];
    });
  };

  const handleDeleteProduct = async (): Promise<void> => {
    if (productsToDelete.length === 0) {
      return;
    }

    try {
      setIsShowModal(false);
      setShowLoader(true);

      const deletePromises = productsToDelete.map(async (product) => {
        await ProductService.deleteProductById(product);
      });

      await Promise.all(deletePromises);

      setProductsToDelete([]);
      loadList();

      toastMsg(ToastType.Success, t('response.deleteSuccess'));
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  };

  return (
    <Section title="Shipment Schedule" className="productsLimit mb-4">
      <Row>
        {isMobile ? (
          <Row className="alignMobileSS">
            <Col md={12}>
              <Text className="pages-title" as="b" size="2rem" weight={600} color="black">
                {t('titles.products')}
              </Text>
            </Col>
            <div className="d-flex justify-content-between gap-4 mb-4">
              <Button
                cy="btn-save"
                type="button"
                variant="success"
                onClick={() => {
                  history.push(`/add-product`);
                }}
              >
                <HiOutlinePlusSm /> {t('buttons.newProduct')}
              </Button>

              {productsToDelete.length > 0 && (
                <Button cy="btn-save" type="button" variant="danger" onClick={() => setIsShowModal(true)}>
                  {t('buttons.delete')}
                </Button>
              )}
            </div>
          </Row>
        ) : (
          <>
            <Col md={12} className="d-flex justify-content-between mb-4">
              <Text className="pages-title" as="b" size="2rem" weight={600} color="black">
                {t('titles.products')}
              </Text>
              <div className="d-flex gap-4">
                <Button
                  cy="btn-save"
                  type="button"
                  variant="success"
                  onClick={() => {
                    history.push(`/add-product`);
                  }}
                >
                  <HiOutlinePlusSm /> {t('buttons.newProduct')}
                </Button>
              </div>
            </Col>
            <Col className="mb-4">
              {!isMobile && (
                <Col className="d-flex align-items-center">
                  <div className="imageFilter">
                    <Image src={FilterIC} className="mt-2" />
                  </div>
                  <Col md={2} className="selectFilter">
                    <Select
                      id={`selectStatus${t('language') || visibility?.value}`}
                      renderKey
                      loadOptions={selectVisibility}
                      cacheOptions
                      defaultOptions
                      placeholder={t('labels.labelCatalogoDeProdutos')}
                      loadingMessage={() => 'Loading...'}
                      cy="test-selectVisibility"
                      onChange={(e: ISelectOption) => {
                        setVisibility(e);
                      }}
                      value={
                        Object.values(visibility || {}).length
                          ? { value: visibility?.value, label: visibility?.label }
                          : null
                      }
                    />
                  </Col>

                  <Col className="d-flex">
                    <div className="colorImage">
                      <Image src={SearchIC} />
                    </div>
                    <input
                      type="text"
                      className="customInput"
                      onChange={debouncedChangeHandler}
                      placeholder={t('placeholders.holderFilterProducts')}
                      required
                      id="query"
                    />
                  </Col>
                  {!isMobile && productsToDelete.length > 0 && (
                    <Col className="d-flex mt-2" md={1}>
                      <Button cy="btn-save" type="button" variant="danger" onClick={() => setIsShowModal(true)}>
                        {t('buttons.delete')}
                      </Button>
                    </Col>
                  )}
                </Col>
              )}
            </Col>
          </>
        )}
        <TableComponent
          isMobile={isMobile}
          data={data}
          tableColumns={tableColumns}
          page={page}
          lastPage={lastPage}
          limit={limit}
          handleChangeLimit={handleChangeLimit}
          handleDownPage={handleDownPage}
          handleUpPage={handleUpPage}
          productsToDelete={productsToDelete}
          handleSelectProducts={handleSelectProducts}
          showLoader={showLoader}
        />
      </Row>

      <Modal
        show={isShowModal}
        handleClose={() => setIsShowModal(false)}
        title={t('modal.removeMultiProductTitle')}
        size="lg"
        className="styleModalConfirm"
        colorIcon
      >
        <Container>
          <Row className="mt-4 p-2">
            <Col className="d-flex align-items-center ">
              <h6>
                {t('modal.removeMultiProductSubTitle')}
                <br />
                {t('modal.removeMultiProductDescription')}
              </h6>
            </Col>
          </Row>
        </Container>

        <Row className="mt-4">
          <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
            <Button
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={() => {
                setIsShowModal(false);
              }}
            >
              {t('buttons.cancel')}
            </Button>
            <Button cy="btn-save" type="button" variant="danger" onClick={handleDeleteProduct}>
              {t('buttons.delete')}
            </Button>
          </Col>
        </Row>
      </Modal>
    </Section>
  );
};

export default MyProducts;
