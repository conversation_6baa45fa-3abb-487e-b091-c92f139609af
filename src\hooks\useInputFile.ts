import { useState, useCallback, useEffect } from 'react';

interface IInputFile {
  fileUrl: string;
  fileName: string;
  size?: any;
}

export default function useInputFile(callback?: (file: IInputFile) => void): () => void {
  const [file, setFile] = useState<IInputFile>({ fileName: '', fileUrl: '', size: '' });

  useEffect(() => {
    if (file.fileName && file.fileUrl) {
      // eslint-disable-next-line no-unused-expressions
      callback && callback(file);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [file]);

  const input = document.createElement('input');
  input.setAttribute('type', 'file');
  input.accept = 'image/jpg, image/jpeg, image/png';

  const handleClick = useCallback((): void => {
    input.click();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  input.onchange = () => {
    if (input.files?.length) {
      const urlFile = URL.createObjectURL(input.files[0]);

      setFile({ fileUrl: urlFile, fileName: input.files[0].name, size: input.files[0].size });
    }
    input.value = '';
  };

  return handleClick;
}
