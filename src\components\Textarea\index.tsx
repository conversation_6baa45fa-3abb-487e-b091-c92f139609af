import React from 'react';
import { Form } from 'react-bootstrap';
import classNames from 'classnames';
import Text from '../Text';
import './styles.scss';

interface IProp {
  value?: string;
  placeholder?: string;
  maxLength?: number;
  minLength?: number;
  id?: string;
  label?: string;
  labelAux?: string;
  readOnly?: boolean | false;
  required?: boolean | false;
  disabled?: boolean | false;
  isInvalid?: boolean | false;
  desc?: string;
  msg?: string;
  className?: string;
  tabIndex?: number;
  rows?: number;
  defaultValue?: string;
  ref?: React.Ref<HTMLInputElement | HTMLElement | string | any>;
  onChange?: React.ChangeEventHandler<HTMLInputElement> | undefined;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onKeyPress?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onKeyUp?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  cy?: string;
  name?: string;
}

const Textarea = ({
  value,
  placeholder,
  maxLength,
  minLength,
  id,
  label,
  labelAux,
  readOnly,
  required,
  disabled,
  rows = 5,
  onChange,
  onBlur,
  onKeyDown,
  onKeyPress,
  onKeyUp,
  isInvalid,
  desc,
  msg,
  ref,
  className,
  tabIndex,
  cy,
  defaultValue,
  name,
}: IProp): React.ReactElement => (
  <Form.Group className={classNames('textarea', className)} controlId={id}>
    <Form.Label
      className={classNames((isInvalid && 'textarea__label-error') || (disabled && 'textarea__label-disabled'))}
    >
      {label}
      {labelAux && (
        <Text as="span" className="textarea__label-aux">
          {labelAux}
        </Text>
      )}
    </Form.Label>
    <Form.Control
      as="textarea"
      name={name}
      rows={rows}
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      onKeyDown={onKeyDown}
      onKeyPress={onKeyPress}
      onKeyUp={onKeyUp}
      placeholder={placeholder}
      maxLength={maxLength}
      minLength={minLength}
      readOnly={readOnly}
      required={required}
      disabled={disabled}
      isInvalid={isInvalid}
      aria-describedby={desc}
      ref={ref}
      autoComplete="off"
      defaultValue={defaultValue}
      className={classNames(className, 'ps-3')}
      tabIndex={tabIndex}
      data-cy={cy}
    />
    <Form.Control.Feedback type="invalid">{msg}</Form.Control.Feedback>
  </Form.Group>
);

Textarea.defaultProps = { tabIndex: 0 };

export default Textarea;
