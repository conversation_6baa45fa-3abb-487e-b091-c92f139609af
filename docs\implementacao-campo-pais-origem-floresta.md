# Documentação: Implementação do Campo "País de Origem" no Cadastro de Floresta

## Visão Geral

Esta documentação detalha a implementação do novo campo "País de Origem" no formulário de cadastro de floresta do sistema WoodFlow. O campo será adicionado como um campo de texto obrigatório para atender aos requisitos de rastreabilidade e conformidade com regulamentações internacionais como EUDR.

## Estrutura Atual do Sistema

### Componente Principal
- **Arquivo**: `src/pages/Fornecedores/NewSupplier/Floresta/index.tsx`
- **Componente**: `CardInfoFloresta`
- **Função**: Formulário de cadastro e edição de florestas

### Campos Existentes
1. **Nome da Floresta** (`florestaName`) - Obrigatório
2. **Identificação** (`identificacao`) - Obrigatório  
3. **Latitude** (`latitude`) - Opcional
4. **Longitude** (`longitude`) - Opcional
5. **Matéria Prima** (`materiaPrima`) - Obrigatório (array)

## Implementação Detalhada

### Fase 1: Backend - Modelo de Dados

#### 1.1 Atualização da Interface IFloresta

**Arquivo**: `src/services/floresta.service.ts`

```typescript
export interface IFloresta {
    id?: number;
    uuid?: string;
    nome?: string;
    identificacao?: string;
    latitude?: string;
    longitude?: string;
    pais_origem?: string; // NOVO CAMPO
    status?: string;
    fornecedor_id?: number;
    materia_prima?: IpropsMateriaPrima[];
}
```

#### 1.2 Atualização da Interface IFlorestaExport

**Arquivo**: `src/services/floresta.service.ts`

```typescript
export interface IFlorestaExport {
    uuid: string;
    id: string;
    nome: string;
    identificacao: string;
    pais_origem: string; // NOVO CAMPO
    status: string;
    fornecedor: string;
    coordenadas: string;
    car?: IFlorestaDocumento | null;
}
```

### Fase 2: Frontend - Interface do Usuário

#### 2.1 Adição do Estado no Componente

**Arquivo**: `src/pages/Fornecedores/NewSupplier/Floresta/index.tsx`

```typescript
// Adicionar novo estado após os estados existentes (linha ~66)
const [paisOrigemFloresta, setPaisOrigemFloresta] = useState('');
```

#### 2.2 Atualização do useEffect para Carregamento de Dados

**Arquivo**: `src/pages/Fornecedores/NewSupplier/Floresta/index.tsx`

```typescript
// Atualizar o useEffect loadDocuments (linha ~123)
setFlorestaName(floresta?.nome ?? '');
setIdentificacao(floresta?.identificacao ?? '');
setLatitude(floresta?.latitude ?? '');
setLongitude(floresta?.longitude ?? '');
setPaisOrigemFloresta(floresta?.pais_origem ?? ''); // NOVA LINHA
```

#### 2.3 Validação no Método handleSubmit

**Arquivo**: `src/pages/Fornecedores/NewSupplier/Floresta/index.tsx`

```typescript
// Atualizar validação no handleSubmit (linha ~266)
if (!florestaName || !identificacao || !paisOrigemFloresta) {
    toastMsg(ToastType.Error, t('messages.errorRequiredFields'));
    return;
}
```

#### 2.4 Atualização dos Objetos de Dados

**Arquivo**: `src/pages/Fornecedores/NewSupplier/Floresta/index.tsx`

```typescript
// Para atualização de floresta existente (linha ~307)
const updateloresta: IFloresta = {
    id: floresta.id,
    uuid: floresta.uuid,
    nome: florestaName,
    identificacao: identificacao,
    latitude: latitude,
    longitude: longitude,
    pais_origem: paisOrigemFloresta, // NOVA LINHA
    status: 'A',
    fornecedor_id: idFornecedor ? Number(idFornecedor) : 0,
    materia_prima: materiaPrimaFinal || null,
};

// Para criação de nova floresta (linha ~390)
const newFloresta: IFloresta = {
    nome: florestaName,
    identificacao: identificacao,
    latitude: latitude,
    longitude: longitude,
    pais_origem: paisOrigemFloresta, // NOVA LINHA
    status: 'A',
    fornecedor_id: idFornecedor ? Number(idFornecedor) : 0,
    materia_prima: materiaPrimaFinal || null,
};
```

#### 2.5 Adição do Campo no Formulário

**Arquivo**: `src/pages/Fornecedores/NewSupplier/Floresta/index.tsx`

```typescript
// Adicionar após o campo Identificação (linha ~678)
<Col md={12} className="mt-2">
  <CustomInput
    cy={'test-pais-origem'}
    id={'paisOrigem'}
    name={'paisOrigem'}
    label={`${t('labels.paisOrigemFloresta')}*`}
    placeholder={t('labels.paisOrigemFloresta')}
    value={paisOrigemFloresta}
    onChange={(e) => {
      setPaisOrigemFloresta?.(e.target.value);
    }}
    type="text"
    maxLength={100}
  />
</Col>
```

### Fase 3: Internacionalização (i18n)

#### 3.1 Tradução em Português

**Arquivo**: `src/translate/languages/pt.ts`

```typescript
// Adicionar na seção labels (linha ~419)
labels: {
    // ... labels existentes
    paisOrigemFloresta: 'País de Origem', // NOVA LINHA
    // ... demais labels
}
```

#### 3.2 Tradução em Inglês

**Arquivo**: `src/translate/languages/en.ts`

```typescript
// Adicionar na seção labels
labels: {
    // ... labels existentes
    paisOrigemFloresta: 'Country of Origin', // NOVA LINHA
    // ... demais labels
}
```

### Fase 4: Validações e Melhorias

#### 4.1 Validação de Formato (Opcional)

Caso seja necessário validar o formato do país (ex: código ISO), adicionar função de validação:

```typescript
// Função de validação (adicionar no início do arquivo)
const isValidCountry = (country: string): boolean => {
    // Implementar validação conforme necessário
    // Ex: lista de países válidos, código ISO, etc.
    return country.length >= 2 && country.length <= 100;
};

// Usar na validação do handleSubmit
if (paisOrigemFloresta && !isValidCountry(paisOrigemFloresta)) {
    toastMsg(ToastType.Error, t('messages.errorInvalidCountry'));
    setShowLoader(false);
    return;
}
```

#### 4.2 Mensagem de Erro para Validação

**Arquivo**: `src/translate/languages/pt.ts`

```typescript
// Adicionar na seção messages
messages: {
    // ... mensagens existentes
    errorInvalidCountry: 'País de origem inválido.', // NOVA LINHA
    // ... demais mensagens
}
```

### Fase 5: Tabela de Listagem

#### 5.1 Atualização da Tabela de Florestas

**Arquivo**: `src/pages/Fornecedores/NewSupplier/Floresta/TableFloresta/index.tsx`

Adicionar coluna "País de Origem" na tabela de listagem de florestas, seguindo o padrão das colunas existentes.

### Fase 6: Banco de Dados

#### 6.1 Migração de Banco de Dados

```sql
-- Adicionar coluna na tabela de florestas
ALTER TABLE florestas 
ADD COLUMN pais_origem VARCHAR(100);

-- Opcional: Adicionar índice para consultas
CREATE INDEX idx_florestas_pais_origem ON florestas(pais_origem);
```

## Considerações Técnicas

### Compatibilidade
- O campo é adicionado como opcional na interface para manter compatibilidade com dados existentes
- Validação obrigatória apenas para novos cadastros
- Dados existentes podem ser atualizados gradualmente

### Performance
- Campo de texto simples não impacta performance significativamente
- Índice opcional para consultas por país

### Segurança
- Validação de entrada para prevenir XSS
- Limitação de caracteres (100) para prevenir ataques de buffer

## Testes Recomendados

### Testes Unitários
1. Validação de campo obrigatório
2. Validação de formato (se implementada)
3. Salvamento e carregamento de dados
4. Tradução de labels

### Testes de Integração
1. Fluxo completo de cadastro de floresta
2. Edição de floresta existente
3. Listagem com novo campo
4. Exportação de dados

### Testes de Interface
1. Responsividade do formulário
2. Validação em tempo real
3. Mensagens de erro
4. Navegação entre campos

## Cronograma Estimado

- **Backend (Modelo)**: 2 horas
- **Frontend (Interface)**: 4 horas  
- **Tradução**: 1 hora
- **Testes**: 3 horas
- **Documentação**: 2 horas

**Total Estimado**: 12 horas de desenvolvimento

## Dependências

- Nenhuma dependência externa adicional necessária
- Utiliza componentes existentes do sistema
- Compatível com a arquitetura atual

## Observações Finais

Esta implementação segue os padrões estabelecidos no sistema WoodFlow, mantendo consistência com os campos existentes e garantindo a integridade dos dados. O campo "País de Origem" contribuirá para o atendimento aos requisitos de rastreabilidade exigidos pela regulamentação EUDR.
