apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: woodflowexporter-web-ingress-stable
  namespace: stable
  annotations:
    kubernetes.io/ingress.class: 'nginx'
spec:
  rules:
    - host: hmg.woodflowexporter.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: woodflowexporter-web-service-stable
                port:
                  number: 80
