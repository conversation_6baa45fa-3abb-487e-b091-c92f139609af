import jwtDecode, { JwtPayload } from 'jwt-decode';

/**
 * @description
 * Retorna se o token jwt é válido ou não.
 * Exemplo de uso:
 * checkTokenIsValid('token');
 * @param {String} key
 * @return {Boolean} true or false
 */

const checkTokenIsValid = (key = ''): boolean => {
  try {
    const token = localStorage.getItem(key);

    if (!token) return false;

    const decoded = jwtDecode<JwtPayload>(token);

    // Verifica se o token tem data de expiração
    if (!decoded.exp) return true;

    // Verifica se o token não está expirado
    const currentTime = Date.now() / 1000;
    return decoded.exp > currentTime;
  } catch {
    return false;
  }
};

export default checkTokenIsValid;
