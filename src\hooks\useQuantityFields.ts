import { useState, useCallback } from 'react';
import { convertM3ToTons, convertTonsToM3, validateQuantityConsistency } from '../utils/woodConversion';

interface UseQuantityFieldsProps {
  initialM3?: string;
  initialTons?: string;
  species?: string;
  autoConvert?: boolean;
}

export const useQuantityFields = ({
  initialM3 = '',
  initialTons = '',
  species,
  autoConvert = false,
}: UseQuantityFieldsProps) => {
  const [quantityM3, setQuantityM3] = useState(initialM3);
  const [quantityTons, setQuantityTons] = useState(initialTons);
  const [isValid, setIsValid] = useState(true);

  const handleM3Change = useCallback(
    (value: string) => {
      setQuantityM3(value);

      if (autoConvert && value && !quantityTons) {
        const convertedTons = convertM3ToTons(value, species);
        setQuantityTons(convertedTons);
      }

      if (value && quantityTons) {
        const valid = validateQuantityConsistency(value, quantityTons, species);
        setIsValid(valid);
      } else {
        setIsValid(true);
      }
    },
    [quantityTons, species, autoConvert]
  );

  const handleTonsChange = useCallback(
    (value: string) => {
      setQuantityTons(value);

      if (autoConvert && value && !quantityM3) {
        const convertedM3 = convertTonsToM3(value, species);
        setQuantityM3(convertedM3);
      }

      if (quantityM3 && value) {
        const valid = validateQuantityConsistency(quantityM3, value, species);
        setIsValid(valid);
      } else {
        setIsValid(true);
      }
    },
    [quantityM3, species, autoConvert]
  );

  const reset = useCallback(() => {
    setQuantityM3('');
    setQuantityTons('');
    setIsValid(true);
  }, []);

  const hasValue = quantityM3 || quantityTons;

  return {
    quantityM3,
    quantityTons,
    isValid,
    hasValue,
    handleM3Change,
    handleTonsChange,
    reset,
  };
};
