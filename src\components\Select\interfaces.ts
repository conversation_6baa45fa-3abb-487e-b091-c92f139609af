import { ISelectOption } from '../../interfaces';

export interface ISelect {
  className?: string;
  title?: string;
  children?: React.ReactElement;
  placeholder?: string;
  isOptionDisabled?: any;
  msg?: string;
  cacheOptions?: boolean;
  isDisabled?: boolean;
  renderKey?: boolean;
  defaultOptions?: boolean;
  isMulti?: boolean;
  isInvalid?: boolean | false;
  defaultValue?: any;
  onChange?: (event: any) => void;
  loadOptions?: (value: string) => Promise<ISelectOption[]>;
  loadingMessage?: () => string;
  noOptionsMessage?: () => string;
  tabIndex?: number | 0;
  value?: any;
  cy: string;
  id?: any;
  menuPosition?: 'fixed' | 'absolute';
  onBlur?: React.FocusEventHandler<HTMLInputElement> | undefined;
  isClearable?: boolean;
}
