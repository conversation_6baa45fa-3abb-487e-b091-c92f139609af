.footer {
  background-color: #201e40;
  padding: 3rem 3rem;
  right: 0;
  left: 0;
  position: absolute;
  padding-left: 5rem;
  margin-top: 5rem;

  & .footerRow {
    @media (max-width: 990px) {
      gap: 16px;
    }
  }

  strong {
    font-weight: var(--is-600);
    color: var(--white-100);
  }

  span {
    font-weight: var(--is-400);
    color: var(--white-100);
  }

  img {
    width: 16rem !important;
  }

  a {
    text-decoration: none;
  }

  .linkUtil {
    display: block;
    padding: 10px;
    margin: 0.125rem;
    width: 3rem;
    height: 3rem;
    color: var(--white-100);
    font-size: 1.375rem;
    border: solid 2px;
    text-align: center;
    border-radius: 50%;
  }
}
svg {
  vertical-align: baseline;
}
