import React from 'react';
import { Redirect } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const RootRedirect: React.FC = () => {
  const { user, getImporter } = useAuth();

  // Se não há usuário logado, redireciona para landing
  if (!user) {
    return <Redirect to="/landing" />;
  }

  // Se há usuário logado e é importer, redireciona para dashboard-importer
  if (getImporter().includes(user?.profileId as number)) {
    return <Redirect to="/dashboard-importer" />;
  }

  // Se há usuário logado e não é importer (seller), redireciona para dashboard
  return <Redirect to="/dashboard" />;
};

export default RootRedirect;
