import React, { useCallback, useState } from 'react';
import { Row, Col, Form, But<PERSON>, Card } from 'react-bootstrap';

import { useTranslation } from 'react-i18next';
import { useHistory, useParams, useLocation, Link } from 'react-router-dom';

import Text from '../../../components/Text';
import Section from '../../../components/Section';
import toastMsg, { ToastType } from '../../../utils/toastMsg';
import AuthsService from '../../../services/auth.service';
import Recaptcha from '../../../components/Recaptcha';
import './styles.scss';
import CustomInput from '../../../components/Input/CustomInput';

interface IParams {
  token: string;
}

export default function RestPassword(): React.ReactElement {
  const params = useParams<IParams>();
  const location: any = useLocation();

  const { t } = useTranslation();
  const history = useHistory();
  const [password, setPassword] = useState<string>('');
  const [passwordConfirm, setPasswordConfirm] = useState<string>('');
  const [generatedPassword, setGeneratedPassword] = useState<string>('');

  const [loading, setLoading] = useState(false);

  const registration = location?.pathname;

  const handleSubmit = useCallback(
    async (e: React.SyntheticEvent) => {
      e.preventDefault();

      try {
        if (!password || !passwordConfirm) return;
        setLoading(true);

        if (registration.includes('/registration')) {
          await AuthsService.resetPasswordFirstAccess(generatedPassword, password, passwordConfirm, params.token);
        } else {
          await AuthsService.resetPassword(password, passwordConfirm, params.token);
        }
        toastMsg(ToastType.Success, t('messages.passwordChanged'));
        history.push('/login');
      } catch (error) {
        toastMsg(ToastType.Error, t('exceptions.passwordNotEqual'));

        setLoading(false);
      }
    },
    [generatedPassword, history, params.token, password, passwordConfirm, registration, t]
  );

  return (
    <Section title="Esqueci minha senha" description="Esqueci minha senha">
      <Row className="forgot-password d-flex align-items-center justify-content-center mt-5">
        <Col className="box-forgot">
          <Card>
            <Card.Body>
              <Row className="justify-content-start">
                <p className="titleReset">
                  {registration.includes('/registration')
                    ? t('labels.labelAlterPassword')
                    : t('labels.labelForgotPassword')}
                </p>
              </Row>
              <Form onSubmit={handleSubmit} autoComplete="off" className="mt-4">
                {registration.includes('/registration') && (
                  <Form.Group className="mb-3 mt-3">
                    <Form.Label>{`${t('placeholders.holderConfirmReicevedPassword')}`}</Form.Label>
                    <CustomInput
                      type="password"
                      onChange={(e) => setGeneratedPassword(e.target.value)}
                      required
                      id="passwordGenerate"
                      cy="test-passwordGenerate"
                    />
                  </Form.Group>
                )}
                <Form.Group className="mb-3 mt-3">
                  <Form.Label>{`${t('placeholders.holderNewPassword')}`}</Form.Label>
                  <CustomInput
                    type="password"
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    id="password"
                    cy="test-password"
                  />
                </Form.Group>
                <Form.Group className="mb-3 mt-3">
                  <Form.Label>{`${t('placeholders.holderConfirmPassword')}`}</Form.Label>
                  <CustomInput
                    type="password"
                    onChange={(e) => setPasswordConfirm(e.target.value)}
                    required
                    id="passwordConfirm"
                    cy="test-passwordConfirm"
                  />
                </Form.Group>
                <Row className="rowBtn mt-2">
                  <Form.Group controlId="button">
                    <Button disabled={loading} className="controlBtn" type="submit">
                      <Text className="labelConfirm" as="span">{`${t('buttons.confirmPassword')}`}</Text>
                    </Button>
                  </Form.Group>
                </Row>
                <Row className="notGetEmail mt-4">
                  <Text as="span">{`${t('labels.labelsNotGetEmail')}`}</Text>
                </Row>
                <Row className="notGetEmail">
                  <Text as="span" className="w-auto ml-2">
                    <Link to="/forgotPassword">{t('labels.labelsReSendEmail')}</Link>
                  </Text>
                </Row>
                <Row className="notGetEmail">
                  <Text as="span" className="w-auto ml-2">
                    <a href="https://api.whatsapp.com/send?phone=5541996858551" target="_blank" rel="noreferrer">
                      {t('labels.labelContactWhatsApp')}
                    </a>
                  </Text>
                </Row>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
      <Recaptcha />
    </Section>
  );
}
