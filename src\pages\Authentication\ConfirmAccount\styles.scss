.confirm-account {
  height: 70vh;
  @media (max-width: 768px) {
    height: 85vh;
  }

  .box-confirm {
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 600px;

    .card {
      width: 100%;
      border-radius: 8px;

      .card-body {
        padding: 4rem 2rem 4rem 4rem;
        width: 100%;

        .logo {
          padding: 1.75rem 0;
        }

        h1 {
          color: #201e40;
          font-size: 2rem;
          font-weight: 600;
        }
        .controlBtn {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #18b680;
          height: 54px;
          border-radius: 8px;
          &:hover {
            background-color: #bac5c5;
          }
        }

        .labelConfirm {
          color: #201e40;
          font-weight: 700;
          line-height: 72px;
          font-style: bold;
        }

        .labelConfirm {
          color: #ffffffff;
        }

        .notGetEmail {
          padding-left: 0.7rem;
        }

        form {
          button {
            background-color: var(--green-400);
            border: 0;
            transition: all 0.2s;

            &:hover {
              background-color: var(--green-800);
            }
          }
        }
      }
    }
  }

  a {
    color: #64a4ec;
    cursor: pointer;
  }
}
