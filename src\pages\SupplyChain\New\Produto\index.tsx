import React, { useState } from 'react';
import { Row, Col, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import CustomCard from '../../../../components/Card';
import Modal from '../../../../components/Modal';
import './styles.scss';
import toastMsg, { ToastType } from '../../../../utils/toastMsg';
import SupplyChainService, { IPropsSaveSupplyChainSpecie } from '../../../../services/supplyChain.service';
import Textarea from '../../../../components/Textarea';
import { IColumnsProps } from '../TableColumnProperties';
import { TableSpecies } from '../Specie/TableSpecie';
import { useAnalytics } from '../../../../contexts/AnalyticsContext';
import CardSupplySpecie from '../Specie';
import { useLoader } from '../../../../contexts/LoaderContext';
import MaskedInput from 'react-text-mask';

interface IPropsCardProductSupply {
  supplyChain: any;
}
const CardProductSupply = ({ supplyChain }: IPropsCardProductSupply): React.ReactElement => {
  const { t } = useTranslation();
  //const { id } = useParams<IParam>();
  //const history = useHistory();
  const [quantity, setQuantity] = useState<string>('');
  const [quantityTons, setQuantityTons] = useState<string>(''); // NOVO: Estado para toneladas
  const [productDescription, setProductDescription] = useState<string>('');
  const [supplyChainProductSave, setSupplyChainProductSave] = useState<any>({} as any);
  const [species, setSpecies] = React.useState([] as IPropsSaveSupplyChainSpecie[]);
  //const [saldo, setSaldo] = useState<string>('0');
  const [isMobile, setIsMobile] = React.useState<boolean>(window.innerWidth <= 768);
  const [tableLength, setTableLength] = React.useState(0);
  const [lastPage, setLastPage] = React.useState(1);
  const { trackEvent } = useAnalytics();
  const [showModalDocument, setShowModalDocument] = useState<any>({ show: false });
  const { setShowLoader } = useLoader();
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  //eslint-disable-next-line
  const [page, setPage] = React.useState(1);
  //eslint-disable-next-line
  const [limit, setLimit] = React.useState(100);
  //eslint-disable-next-line
  const [query, setQuery] = React.useState<string>('');

  const collums: IColumnsProps[] = [
    {
      field: 'fornecedor',
      headerName: t('labels.fornecedor'),
      fixed: true,
      color: '#201E40',
      flexGrow: 6,
      resizable: !!isMobile,
    },
    {
      field: 'floresta',
      headerName: t('labels.wild'),
      color: '#201E40',
      flexGrow: 4,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'actions',
      headerName: t('modal.modalTitleActions'),
      color: '#201E40',
      flexGrow: 2,
      fixed: false,
      resizable: !!isMobile,
    },
  ];

  // Estados para controle de erro de upload
  const [uploadError, setUploadError] = useState<any>(null);
  const [showHelpModal, setShowHelpModal] = useState<boolean>(false);

  // Função para limpar erro de upload
  const clearUploadError = () => {
    setUploadError(null);
    localStorage.removeItem('uploadError');
  };

  // Verificar se há erro salvo no localStorage ao montar o componente
  React.useEffect(() => {
    const savedError = localStorage.getItem('uploadError');
    if (savedError) {
      try {
        const errorObj = JSON.parse(savedError);
        setUploadError(errorObj);
      } catch (e) {
        localStorage.removeItem('uploadError');
      }
    }
  }, []);

  const handleChangeLimit = (dataKey: number): void => {
    setPage(1);
    setLimit(dataKey);
    trackEvent('Quotes per page', {
      action: `See ${dataKey} per page`,
      param: 'page',
    });
  };

  const handleDownPage = (): void => {
    const newPage = page - 1;
    if (newPage >= 1) {
      setPage(newPage);
    }

    trackEvent('Quotes Previus Page', {
      action: `Clicou previus page`,
      param: 'Page',
    });
  };

  const handleUpPage = (): void => {
    const newPage = page + 1;
    if (page <= tableLength / limit) {
      setPage(newPage);
    }

    trackEvent('Quotes Next Page', {
      action: `Clicou next page`,
      param: 'Page',
    });
  };

  const handleWindowSizeChange = (): void => {
    if (window.innerWidth <= 768) {
      setIsMobile(true);
    } else {
      setIsMobile(false);
    }
  };
  React.useEffect(() => {
    window.addEventListener('resize', handleWindowSizeChange);
  }, []);

  const getSupplyChainProduct = React.useCallback(async (): Promise<any> => {
    try {
      const res = await SupplyChainService.getProductBySupplyChain(String(supplyChain.supply_chain_id));

      if (res) {
        return res;
      }
      return {};
    } catch (error) {
      return {};
    }
  }, []);

  const getSupplyChainSpecies = React.useCallback(async (scp_id: number, name: string): Promise<any> => {
    try {
      const res = await SupplyChainService.getSpeciesBySupplyChainProduct(scp_id, name, page, limit);

      if (res) {
        return res;
      }
      return {};
    } catch (error) {
      return {};
    }
  }, []);

  async function loadSupplyChainProduct(): Promise<void> {
    const res = await getSupplyChainProduct();

    if (res.data != undefined) {
      setQuantity(res?.data?.product_quantity?.replace(/\.(?=[^.]*$)/, ',') || '');
      setQuantityTons(res?.data?.product_quantity_tons?.replace(/\.(?=[^.]*$)/, ',') || '');
      setProductDescription(res?.data?.product_description);
      const productData: any = {
        id: res?.data?.id,
        uuid: res?.data?.uuid,
        status: res?.data?.status,
        supply_chain_id: res?.data?.supply_chain_id,
        product_description: res?.data?.product_description,
        product_quantity: res?.data?.product_quantity,
        product_quantity_tons: res?.data?.product_quantity_tons,
      };

      setSupplyChainProductSave(productData);
    }
  }

  React.useEffect(() => {
    if (isInitialLoad) {
      loadSupplyChainProduct();
    }

    return () => {
      setIsInitialLoad(false);
    };
  }, []);

  async function loadSpecies(): Promise<void> {
    const resSpecies = await getSupplyChainSpecies(supplyChainProductSave.id, query);

    if (resSpecies?.data?.length > 0) {
      const rows: any = resSpecies.data.map((item: any, index: number) => ({
        fornecedor: item.fornecedor_nome,
        floresta: item.floresta_nome || item.nome,
        //quantidade: item.mp_qtde.replace('.', ','),
        supplyChainSpecie: item,
        className: index % 2 === 0 ? 'custom-row' : '',
        uuid: item.uuid || item.uuid,
        id: item.id || item.id,
      }));

      setSpecies(rows);
      setLastPage(resSpecies.meta.last_page);
      setTableLength(resSpecies.meta.total);
    }
  }

  React.useEffect(() => {
    let controle = true;
    if (controle && supplyChainProductSave?.id) {
      loadSpecies();
    }
    return () => {
      controle = false;
    };
  }, [supplyChainProductSave?.id, query, page, limit]);

  const handleSubmitProduct = async () => {
    try {
      setShowLoader(true);

      // Validação: pelo menos um campo deve ser preenchido (apenas para novos produtos)
      if (!supplyChainProductSave?.id && !quantity?.trim() && !quantityTons?.trim()) {
        toastMsg(ToastType.Error, t('labels.quantityRequired'));
        setShowLoader(false);
        return;
      }

      if (supplyChainProductSave?.id) {
        const productUpdate: any = {
          id: supplyChainProductSave?.id,
          product_description: productDescription,
          product_quantity: quantity?.trim() ? quantity.replace(',', '.') : null,
          product_quantity_tons: quantityTons?.trim() ? quantityTons.replace(',', '.') : null,
        };

        await SupplyChainService.updateProductSupplyChain(supplyChainProductSave.uuid, productUpdate);

        // Atualizar o estado local com os dados salvos
        setSupplyChainProductSave({
          ...supplyChainProductSave,
          product_description: productDescription,
          product_quantity: quantity?.trim() ? quantity.replace(',', '.') : null,
          product_quantity_tons: quantityTons?.trim() ? quantityTons.replace(',', '.') : null,
        });
      } else {
        const newProduct: any = {
          product_description: productDescription,
          product_quantity: quantity?.trim() ? quantity.replace(',', '.') : null,
          product_quantity_tons: quantityTons?.trim() ? quantityTons.replace(',', '.') : null,
          status: 'A',
          supply_chain_id: supplyChain.supply_chain_id,
        };

        const productSave: any = await SupplyChainService.saveProductSupplyChain(newProduct);
        setSupplyChainProductSave(productSave);
      }

      toastMsg(ToastType.Success, t('response.saveSuccess'));
      setShowLoader(false);
      loadSpecies();
    } catch (error: any) {
      //toastMsg(ToastType.Error, error?.response?.data?.errors);
      //eslint-disable-next-line
      console.log('supplyChainProductSave', supplyChainProductSave);
    }
  };

  return (
    <CustomCard
      cy="card-test-product"
      renderBody={
        <>
          <Modal
            show={showModalDocument.show}
            handleClose={() => setShowModalDocument({ ...showModalDocument, show: false })}
            title={showModalDocument.tipo === 1 ? t('titles.novaFloresta') : t('titles.novaMateriaPrima')}
            size="xl"
            className="styleModalConfirm"
            colorIcon
          >
            <CardSupplySpecie
              closeModal={setShowModalDocument}
              atualizarSpecie={loadSpecies}
              supplyChainId={supplyChain.id}
              supplyChainProductSaveId={supplyChainProductSave.id}
            />
          </Modal>
          <Row>
            <Col md={12} className="mt-2">
              <Textarea
                cy={'test-description' + supplyChain.id}
                id={'description' + supplyChain.id}
                name={'description' + supplyChain.id}
                label={t('labels.productDescriptionInvoice')}
                onChange={(e) => {
                  setProductDescription(e.target.value);
                }}
                value={productDescription}
                placeholder={t('labels.selectProductName')}
              />
            </Col>
            <Col md={4} className="mt-2">
              <label htmlFor="quantity-m3">{t('labels.productQuantityM3')}</label>
              <MaskedInput
                value={quantity}
                onChange={(e) => {
                  setQuantity(e.target.value);
                }}
                onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                  if (e.key === 'Delete' || e.key === 'Backspace') {
                    const input = e.target as HTMLInputElement;
                    const hasSelection = input.selectionStart !== input.selectionEnd;

                    if (hasSelection) {
                      setQuantity('');
                    }
                  }
                }}
                mask={(rawValue) => {
                  const digits = rawValue.replace(/[^\d]/g, '');
                  const decimalPart = [/\d/, /\d/, /\d/]; // Sempre 3 casas decimais

                  // Ajusta a parte inteira com base na quantidade de dígitos
                  const integerPart = [];
                  for (let i = 0; i < digits.length - 3; i++) {
                    integerPart.push(/\d/);
                    // Adiciona ponto como separador de milhar após cada 3 dígitos
                    if ((digits.length - 3 - i) % 3 === 1 && i < digits.length - 4) {
                      integerPart.push('.');
                    }
                  }

                  return [...integerPart, ',', ...decimalPart];
                }}
                guide={false}
                placeholderChar={'\u2000'}
                placeholder={t('labels.productQuantityM3')}
                className="form-control"
              />
            </Col>

            {/* NOVO CAMPO TONELADAS */}
            <Col md={4} className="mt-2">
              <label htmlFor="quantity-tons">{t('labels.productQuantityTons')}</label>
              <MaskedInput
                value={quantityTons}
                onChange={(e) => {
                  setQuantityTons(e.target.value);
                }}
                onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                  if (e.key === 'Delete' || e.key === 'Backspace') {
                    const input = e.target as HTMLInputElement;
                    const hasSelection = input.selectionStart !== input.selectionEnd;

                    if (hasSelection) {
                      setQuantityTons('');
                    }
                  }
                }}
                mask={(rawValue) => {
                  const digits = rawValue.replace(/[^\d]/g, '');
                  const decimalPart = [/\d/, /\d/, /\d/]; // Sempre 3 casas decimais

                  // Ajusta a parte inteira com base na quantidade de dígitos
                  const integerPart = [];
                  for (let i = 0; i < digits.length - 3; i++) {
                    integerPart.push(/\d/);
                    // Adiciona ponto como separador de milhar após cada 3 dígitos
                    if ((digits.length - 3 - i) % 3 === 1 && i < digits.length - 4) {
                      integerPart.push('.');
                    }
                  }

                  return [...integerPart, ',', ...decimalPart];
                }}
                guide={false}
                placeholderChar={'\u2000'}
                placeholder={t('labels.productQuantityTons')}
                className="form-control"
                data-cy="quantity-tons"
              />
            </Col>

            <Col md={4} className="mt-4" style={{ display: 'flex', justifyContent: 'flex-end' }}>
              {supplyChainProductSave.id != undefined ? (
                <Button variant="outline-green" onClick={handleSubmitProduct} style={{ marginTop: '14px' }}>
                  {t('buttons.updateProduct')}
                </Button>
              ) : (
                <Button
                  variant="outline-green"
                  onClick={handleSubmitProduct}
                  style={{ marginTop: '14px', marginBottom: '30px' }}
                >
                  {t('buttons.saveProduct')}
                </Button>
              )}
            </Col>
          </Row>

          {supplyChainProductSave.id != undefined && species.length > 0 && (
            <>
              <Row>
                <Col md={12} className="mt-4">
                  <label className="titleCard" style={{ marginBottom: '20px' }}>
                    {t('labels.specieUtilizada')}
                  </label>
                </Col>

                {/* Mensagem de erro do upload */}
                {uploadError && (
                  <Col md={12} className="mt-2">
                    <div
                      className="alert alert-danger d-flex justify-content-between align-items-center mb-3"
                      role="alert"
                    >
                      <div className="d-flex align-items-center">
                        <i className="fas fa-exclamation-triangle me-2"></i>
                        <span>{uploadError.message}</span>
                      </div>
                      <div className="d-flex gap-2">
                        {uploadError.showHelpButton && (
                          <Button variant="outline-primary" size="sm" onClick={() => setShowHelpModal(true)}>
                            Como resolver
                          </Button>
                        )}
                        <Button variant="outline-secondary" size="sm" onClick={clearUploadError}>
                          ✕
                        </Button>
                      </div>
                    </div>
                  </Col>
                )}

                <TableSpecies
                  isMobile={isMobile}
                  data={species}
                  tableColumns={collums}
                  page={page}
                  lastPage={lastPage}
                  limit={limit}
                  handleChangeLimit={handleChangeLimit}
                  handleDownPage={handleDownPage}
                  handleUpPage={handleUpPage}
                  produto={supplyChainProductSave}
                  atualizarSpecie={loadSpecies}
                />
              </Row>
              {/* Modal de ajuda para conversão de PDF */}
              <Modal
                show={showHelpModal}
                handleClose={() => setShowHelpModal(false)}
                title="Como resolver problemas com PDF"
                size="lg"
                className="styleModalConfirm"
              >
                <div className="p-4">
                  <div className="alert alert-info mb-4">
                    <strong>Problemas mais comuns:</strong>
                    <ul className="mb-0 mt-2">
                      <li>Arquivo muito grande (limite: 10MB)</li>
                      <li>PDF corrompido ou com erro</li>
                      <li>Versão do PDF incompatível</li>
                      <li>Arquivo protegido por senha</li>
                    </ul>
                  </div>

                  <h5>Soluções recomendadas:</h5>
                  <div className="mt-3">
                    <h6>1. Corrigir erros no PDF (Recomendado):</h6>
                    <div className="ms-3 mb-3">
                      <strong>Ferramentas online gratuitas:</strong>
                      <ul>
                        <li>
                          <strong>1°. Reduzir compressão do PDF: </strong>
                          <a href="https://www.pdfyeah.com/decompress-pdf/" target="_blank" rel="noreferrer">
                            https://www.pdfyeah.com/decompress-pdf/
                          </a>
                        </li>
                        <li>
                          <strong>2°. SmallPDF: </strong>
                          <a href="https://smallpdf.com/compress-pdf" target="_blank" rel="noreferrer">
                            smallpdf.com/compress-pdf
                          </a>
                        </li>
                        <li>
                          <strong>3°. ILovePDF: </strong>
                          <a href="https://ilovepdf.com/compress_pdf" target="_blank" rel="noreferrer">
                            ilovepdf.com/compress_pdf
                          </a>
                        </li>
                        <li>
                          <strong>4°. PDF24: </strong>
                          <a href="https://tools.pdf24.org/pt/comprimir-pdf" target="_blank" rel="noreferrer">
                            tools.pdf24.org/pt/comprimir-pdf
                          </a>
                        </li>
                      </ul>
                      <div className="alert alert-success mt-2">
                        <small>
                          <strong>Dica:</strong> Essas ferramentas otimizam o PDF, desconprimindo ou reduzindo o tamanho
                          e mantendo a qualidad e.
                        </small>
                      </div>
                    </div>

                    <h6>2. Usando Adobe Acrobat (Se disponível):</h6>
                    <div className="ms-3 mb-3">
                      <ol>
                        <li>Abra o PDF no Adobe Acrobat</li>
                        <li>Vá em &quot;Arquivo&quot; → &quot;Salvar como outro&quot; → &quot;PDF otimizado&quot;</li>
                        <li>Nas configurações, reduza a qualidade das imagens para 150 DPI</li>
                        <li>Desmarque opções desnecessárias</li>
                        <li>Salve o arquivo</li>
                      </ol>
                    </div>

                    <h6>3. Verificações antes do upload:</h6>
                    <div className="ms-3">
                      <ul>
                        <li>✅ Arquivo em formato PDF</li>
                        <li>✅ Tamanho menor que 10MB</li>
                        <li>✅ PDF não protegido por senha</li>
                        <li>✅ Arquivo não corrompido (abre normalmente)</li>
                        <li>✅ Conteúdo legível e completo</li>
                      </ul>
                    </div>
                  </div>

                  <div className="d-flex justify-content-end mt-4">
                    <Button variant="outline-secondary" onClick={() => setShowHelpModal(false)}>
                      Fechar
                    </Button>
                  </div>
                </div>
              </Modal>
            </>
          )}
          {supplyChainProductSave.id != undefined && (
            <Col md={12} className="mt-2" style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button variant="outline-secondary" onClick={() => setShowModalDocument({ show: true })}>
                {t('buttons.addSpecie')}
              </Button>
            </Col>
          )}
        </>
      }
    />
  );
};

export default CardProductSupply;
