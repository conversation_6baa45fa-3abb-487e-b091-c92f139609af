// Densidades médias por espécie de madeira (kg/m³)
const WOOD_DENSITIES: { [key: string]: number } = {
  eucalipto: 600,
  pinus: 450,
  cedro: 400,
  mogno: 650,
  default: 500, // Densidade padrão
};

export const convertM3ToTons = (volumeM3: string, species?: string): string => {
  const volume = parseFloat(volumeM3.replace(',', '.'));
  if (isNaN(volume)) return '';

  const density = WOOD_DENSITIES[species?.toLowerCase() || 'default'];
  const tons = (volume * density) / 1000; // Converter kg para toneladas

  return tons.toFixed(3).replace('.', ',');
};

export const convertTonsToM3 = (weightTons: string, species?: string): string => {
  const weight = parseFloat(weightTons.replace(',', '.'));
  if (isNaN(weight)) return '';

  const density = WOOD_DENSITIES[species?.toLowerCase() || 'default'];
  const volume = (weight * 1000) / density; // Converter toneladas para kg

  return volume.toFixed(3).replace('.', ',');
};

export const validateQuantityConsistency = (
  volumeM3: string,
  weightTons: string,
  species?: string,
  tolerance = 0.1
): boolean => {
  if (!volumeM3 || !weightTons) return true; // Se um está vazio, não validar

  const expectedTons = convertM3ToTons(volumeM3, species);
  const actualTons = parseFloat(weightTons.replace(',', '.'));
  const expectedTonsNum = parseFloat(expectedTons.replace(',', '.'));

  const difference = Math.abs(actualTons - expectedTonsNum);
  const percentageDiff = difference / expectedTonsNum;

  return percentageDiff <= tolerance;
};
