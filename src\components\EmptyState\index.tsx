import React from 'react';
import { Col, Row, Image } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';

import './styles.scss';

interface IEmptyState {
  text: string;
  secondaryText?: string;
  img: any;
}

const EmptyState = ({ text, secondaryText, img }: IEmptyState): React.ReactElement => {
  const { t } = useTranslation();

  return (
    <Col md={12} className="data-table-error p-5 text-center mt-5">
      <Row className="d-flex justify-content-center">
        <Col md={5}>
          <Image src={img} />
          <p className="titleEmpty">{text || t('emptyState.default')}</p>
          <p className="subTitle">{secondaryText}</p>
        </Col>
      </Row>
    </Col>
  );
};

export default EmptyState;
