import React, { useState } from 'react';
import { Table } from 'rsuite';
import { useHistory } from 'react-router-dom';
import { Col, Container, Dropdown, Image, OverlayTrigger, Row, Tooltip } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, ArrowRight } from '@mui/icons-material';
import EmptyState from '../../../components/EmptyState';
import EmptyStateImage from '../../../statics/emptyState.png';
import { FiEdit2 } from 'react-icons/fi';
import InactiveUser from '../../../statics/BsXCircle.svg';
import Modal from '../../../components/Modal';
import Button from '../../../components/Button';

import '../styles.scss';
import { useAuth } from '../../../contexts/AuthContext';
import toastMsg, { ToastType } from '../../../utils/toastMsg';
import ProductService from '../../../services/products.service';
import { useLoader } from '../../../contexts/LoaderContext';

export const TableComponent = ({
  sortColumn,
  sortType,
  handleSortColumn,
  isMobile,
  data,
  tableColumns,
  page,
  lastPage,
  limit,
  handleChangeLimit,
  handleDownPage,
  handleUpPage,
  showLoader,
}: any): React.ReactElement => {
  const { Column, HeaderCell, Cell } = Table;
  const { setShowLoader } = useLoader();
  const { t } = useTranslation();
  const { user } = useAuth();
  const history = useHistory();
  const [show, setShow] = useState<any>({ show: false, product: null });

  const newTableColumns = !user?.default_company?.features?.includes('market')
    ? tableColumns?.filter((obj: { field: string }) => !(obj.field === 'status'))
    : tableColumns;

  function breakLinesInTooltip(text: string): any {
    const lines = text.split(',');
    return (
      <div style={{ textAlign: 'left' }}>
        {lines.map((line, index) => (
          <span key={index}>
            {line}
            {index < lines.length - 1 && <br />}
          </span>
        ))}
      </div>
    );
  }

  const handleDeleteProduct = async (product: any): Promise<void> => {
    if (!product?.uuid) {
      return;
    }

    try {
      setShowLoader(true);

      await ProductService.deleteProductById(product?.uuid);
      toastMsg(ToastType.Success, t('response.deleteSuccess'));

      history.push('/products');

      setShowLoader(false);
      setShow(false);
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  };

  return (
    <Container className="containerBackground">
      <Modal
        show={show.show}
        handleClose={() => setShow({ ...show, show: false })}
        title={t('modal.removeProductTitle')}
        size="lg"
        className="styleModalConfirm"
        colorIcon
      >
        <Container>
          <Row className="mt-4 p-2">
            <Col className="d-flex align-items-center ">
              <h6>
                {t('modal.removeProductSubTitle')}
                <br />
                {t('modal.removeProductDescription')}
              </h6>
            </Col>
          </Row>
        </Container>

        <Row className="mt-4">
          <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
            <Button
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={() => {
                setShow(false);
              }}
            >
              {t('buttons.cancel')}
            </Button>
            <Button cy="btn-save" type="button" variant="danger" onClick={() => handleDeleteProduct(show.product)}>
              {t('buttons.delete')}
            </Button>
          </Col>
        </Row>
      </Modal>

      {data.length > 0 ? (
        <>
          <Table
            bordered
            cellBordered
            sortColumn={sortColumn}
            sortType={sortType}
            onSortColumn={handleSortColumn}
            autoHeight={isMobile}
            height={500}
            affixHorizontalScrollbar={!isMobile}
            data={data}
            className="table"
            rowClassName={(rowData: any) => rowData?.className || ''}
          >
            {newTableColumns.map((column: any) => {
              const { field, headerName, color, cursor, textDecoration, fixed, ...rest } = column;

              if (field === 'actions') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            <div
                              onClick={() => {
                                history.push(`/add-product`, { product: rowData });
                              }}
                              role="presentation"
                              title={t('business.editProduct')}
                              style={{ cursor: 'pointer', color: '#494747' }}
                            >
                              <FiEdit2 size={20} color="green" />
                            </div>
                            <div
                              onClick={() => setShow({ show: true, product: rowData })}
                              role="presentation"
                              title={t('business.removeProduct')}
                              style={{ cursor: 'pointer' }}
                            >
                              <Image src={InactiveUser} height={20} />
                            </div>
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              if (field === 'specie') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip id="tooltip-specie"> {breakLinesInTooltip(rowData.specie)}</Tooltip>}
                          >
                            <div className="specieStyle"> {rowData.specie}</div>
                          </OverlayTrigger>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }
              if (field === 'qualities') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip id="tooltip-specie"> {breakLinesInTooltip(rowData.qualities)}</Tooltip>}
                          >
                            <div className="specieStyle"> {rowData.qualities}</div>
                          </OverlayTrigger>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }
              if (field === 'certificates') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <OverlayTrigger
                            placement="top"
                            overlay={
                              <Tooltip id="tooltip-specie"> {breakLinesInTooltip(rowData.certificates)}</Tooltip>
                            }
                          >
                            <div className="specieStyle"> {rowData.certificates}</div>
                          </OverlayTrigger>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              return (
                <Column {...rest} key={field} fixed={fixed}>
                  <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                  <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap />
                </Column>
              );
            })}
          </Table>
          <div className="pagination">
            <div className="pageGroup">
              {!isMobile && <span>{t('pagination.productsPerPage')}</span>}
              <Dropdown className="selectPerPage">
                <Dropdown.Toggle id="dropdown-limit">{limit}</Dropdown.Toggle>
                <Dropdown.Menu>
                  <Dropdown.Item onClick={() => handleChangeLimit(10)}>10</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(30)}>30</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(50)}>50</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(70)}>70</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(100)}>100</Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            </div>
            <div className="paginator">
              <span>
                {t('pagination.page')} {page} {t('pagination.of')} {lastPage}
              </span>
              <ArrowLeft className="navigator" onClick={handleDownPage} />
              <ArrowRight className="navigator" onClick={handleUpPage} />
            </div>
          </div>
        </>
      ) : (
        <>
          {!showLoader && (
            <EmptyState
              text={t('labels.labelProductNotFound')}
              secondaryText={t('labels.labelDescProductNotFound')}
              img={EmptyStateImage}
            />
          )}
        </>
      )}
    </Container>
  );
};
