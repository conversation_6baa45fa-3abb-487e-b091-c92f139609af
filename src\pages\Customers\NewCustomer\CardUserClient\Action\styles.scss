.alignBody {
  display: flex;
  @media (max-width: 768px) {
    margin-left: 0;
    margin-right: 0;
  }
}

.style {
  .modal-header {
    background-color: var(--green-500);
    .modal-title {
      color: var(--white-100);
    }
  }
}

.styleModalConfirm {
  padding-top: 10rem;
  background: rgba(#000000, 0.4);

  .modal-header {
    background-color: #201e40;
    .modal-title {
      color: var(--white-100);
    }
  }
}
.selectApprove {
  .css-qc6sy-singleValue {
    padding-right: 8px;
    padding-left: 8px;
    background-color: #18b680;
    border-radius: 4px;
    color: #ffffffff;
    width: max-content;
    min-height: 20px;
  }
}

.selectApproveAndDisplayNone {
  @extend .selectApprove;

  .css-26l3qy-menu {
    display: none;
  }
}

.selectUnapproved {
  .css-qc6sy-singleValue {
    padding-right: 8px;
    padding-left: 8px;
    background-color: #fabb00;
    border-radius: 4px;
    color: #ffffffff;
    width: max-content;
    min-height: 20px;
  }
}
