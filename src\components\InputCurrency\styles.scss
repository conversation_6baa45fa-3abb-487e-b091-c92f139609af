.input-currency {
  input {
    display: block;
    color: var(--gray-800);
    border-color: var(--gray-200);
    font-weight: var(--is-400);
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--gray-200);
    border-radius: 0.25rem;
    width: 100%;

    &:focus {
      box-shadow: 0.5rem var(--orange-200);
      border-color: var(--gray-200);
      outline: var(--orange-200) 0.125rem solid;
    }

    &:read-only {
      background-color: transparent;
      border: 0.06rem solid var(--gray-200);
      color: var(--gray-700);
      pointer-events: none;
      &::placeholder {
        color: var(--gray-200);
      }
    }
  }
  label {
    margin-bottom: 0;
  }
  &__error {
    border: 0.06rem solid var(--red-500) !important;
  }
  &__label-error,
  &__invalid-feedback {
    color: var(--red-500);
    font-weight: var(--is-400);
  }
  &__invalid-feedback {
    font-size: 0.87rem;
  }
  &__label-disabled {
    color: var(--gray-200);
  }
}
