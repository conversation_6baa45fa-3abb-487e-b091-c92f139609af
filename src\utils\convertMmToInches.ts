import { t } from 'i18next';

interface IConvcertToInchesProsp {
  length: any;
  width: any;
  thickness: any;
  unity: string;
}

export default function convertMmToInches({ length, width, thickness, unity }: IConvcertToInchesProsp): string {
  const formatValue = (value: any, unit: string): string => {
    if (value === undefined || value === null || value.length === 0) {
      return '';
    }
    return `${value.toLocaleString('pt-br')}${unit}`;
  };

  let text = '';

  if (unity === 'm3') {
    text = ` ${formatValue(length, 'mm x ')} ${formatValue(width, `mm ${thickness ? 'x' : ''}`)} ${
      thickness ? formatValue(thickness, 'mm') : t('labels.notInformed')
    }`;
  }

  return text;
}
