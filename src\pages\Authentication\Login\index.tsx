import React, { useCallback, useRef, useState, useEffect } from 'react';
import { Link, useHistory, useParams } from 'react-router-dom';
import { Row, Col, Form, But<PERSON>, Card } from 'react-bootstrap';
import { useIntercom } from 'react-use-intercom';
import { useTranslation } from 'react-i18next';
import { AxiosError } from 'axios';
import Text from '../../../components/Text';
import CustomInput from '../../../components/Input/CustomInput';
import toastMsg, { ToastType } from '../../../utils/toastMsg';
import Section from '../../../components/Section';
import { useAuth } from '../../../contexts/AuthContext';
import Recaptcha from '../../../components/Recaptcha';
import checkTokenIsValid from '../../../utils/checkTokenIsValid';
import { IParam } from '../../../interfaces';
import UsersService from '../../../services/users.service';
import './styles.scss';

export default function Login(): React.ReactElement {
  const emailRef = useRef<HTMLInputElement>(null);
  const [password, setPassword] = useState<string>('');
  const { signIn, getImporter, getSeller } = useAuth();
  const [loading, setLoading] = useState(false);
  const history = useHistory();

  //eslint-disable-next-line
  const { hash } = useParams<IParam>();
  const { t } = useTranslation();
  const { update } = useIntercom();

  useEffect(() => {
    const token = checkTokenIsValid('@WoodFlowExporter:token');
    if (token) {
      history.push('/');
      history.go(0);
    }
  }, [history]);

  const handleSubmit = useCallback(
    async (e: React.SyntheticEvent) => {
      e.preventDefault();

      try {
        if (!emailRef || !emailRef.current || !password) {
          toastMsg(ToastType.Error, 'Existem campos inválidos');
          return;
        }

        setLoading(true);

        const newUser = await signIn({ email: emailRef.current.value, password });

        update({
          name: newUser.displayName,
          email: newUser.email,
          customAttributes: { purchase_order: 'GCM-32' },
          company: {
            companyId: String(newUser.default_company?.uuid),
            name: String(newUser.default_company?.name),
          },
        });

        history.push('/dashboard');

        const userVerify = await UsersService.checkToken();
        if (
          (getImporter().includes(userVerify?.profileId as number) ||
            getSeller().includes(userVerify?.profileId as number)) &&
          !userVerify.first_login_at
        ) {
          await UsersService.setFirstLogin();
        }
      } catch (error) {
        const err = error as AxiosError;
        console.error('Erro de login:', err);
        const errorMessage = (err.response?.data as { message: string })?.message || 'Erro ao fazer login';
        toastMsg(ToastType.Error, errorMessage);
      } finally {
        setLoading(false);
      }
    },
    [history, password, signIn, update]
  );

  return (
    <Section title="Login" description="Login" className="p-0 m-0">
      <Row className="login d-flex align-items-center justify-content-center p-0">
        <Col className="box-login">
          <Card>
            <Card.Body>
              <p className="titleLogin">Login</p>

              <Form onSubmit={handleSubmit} autoComplete="off">
                <Form.Group className="mb-3 mt-4" controlId="email">
                  <Form.Label>{`${t('placeholders.holderEmail')}`}</Form.Label>
                  <Form.Control type="email" ref={emailRef} required />
                </Form.Group>
                <Form.Group className="mb-3">
                  <Form.Label>{`${t('placeholders.holderPassword')}`}</Form.Label>
                  <CustomInput
                    type="password"
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    id="password"
                    cy="test-password"
                  />
                </Form.Group>
                <Text as="span" className="w-auto ml-2">
                  <Link to="/forgotPassword">{`${t('labels.labelForgotPassword')}`}</Link>
                </Text>
                <Row className="align-items-center justify-content-end">
                  <Form.Group className="w-auto" controlId="button">
                    <Button disabled={loading} className="btnLogin w-10 d-flex align-items-center" type="submit">
                      <Text className="labelLogin" as="b">{`${t('buttons.login')}`}</Text>
                    </Button>
                  </Form.Group>
                </Row>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
      <Recaptcha />
    </Section>
  );
}
