import React from 'react';
import { FieldProps } from 'formik';
import Select from 'react-select';
import { OptionsType, ValueType } from 'react-select/lib/types';
import Text from '../../Text';

interface Option {
  label: string;
  value: string;
}

export interface IOnSelectedInterface {
  name: string;
  value: string;
  label: string;
}

interface CustomSelectProps extends FieldProps {
  options: OptionsType<Option>;
  isMulti?: boolean;
  handleFormSubmit?: boolean;
  className?: string;
  placeholder?: string;
  onSelected?: (params: IOnSelectedInterface) => void;
  isInvalid?: boolean | false;
  msg?: string;
  title?: string;
  children?: React.ReactElement;
  cy: string;
  onBlur?: React.FocusEventHandler<HTMLInputElement> | undefined;
  defaultValue?: any;
  isQuery: boolean;
  isDisabled?: boolean;
  renderKey?: boolean;
  id?: any;
}

export const CustomSelect = ({
  className,
  placeholder,
  field,
  form,
  options,
  isMulti = false,
  handleFormSubmit = false,
  onSelected,
  isInvalid,
  msg,
  children,
  title,
  cy,
  onBlur,
  defaultValue,
  isQuery = false,
  renderKey = false,
  isDisabled,
  id,
}: CustomSelectProps): React.ReactElement => {
  const onChange = (option: ValueType<Option | Option[]>): void => {
    let optValue = null;
    if (option) {
      if (isMulti) {
        optValue = (option as Option[]).map((item: Option) => item.value);
      } else {
        optValue = isQuery
          ? { value: (option as Option).value, label: (option as Option).label }
          : (option as Option).value;
      }

      if (onSelected instanceof Function && !isMulti) {
        onSelected({
          name: id,
          value: (option as Option).value,
          label: (option as Option).label,
        });
      }
    }

    form.setFieldValue(field.name, optValue);
    if (handleFormSubmit) {
      form.submitForm();
    }
  };

  const getValue = (): [] | any => {
    if (options.length) {
      if (!field.value) {
        return null;
      }

      return isMulti
        ? options.filter((option) =>
            field.value.some((value: any) => JSON.stringify(value) === JSON.stringify(option.value))
          )
        : options.find((option) => option.value === field.value);
    }

    return isMulti ? [] : ('' as any);
  };

  return (
    <div className={className} data-cy={cy}>
      {children ? (
        <div className="d-flex gap-2 align-items-center justify-content-between">
          <Text as="span" className={`form-label d-block ${isInvalid && !getValue().length && 'select-error__title'}`}>
            {title}
          </Text>
          {children}
        </div>
      ) : (
        <Text as="span" className={`form-label d-block ${isInvalid && !getValue().length && 'select-error__title'}`}>
          {title}
        </Text>
      )}
      <Select
        isDisabled={isDisabled}
        key={renderKey ? id : null}
        className={className}
        name={field.name}
        value={getValue() || defaultValue}
        onChange={onChange}
        placeholder={placeholder}
        options={options}
        isMulti={isMulti}
        isClearable={!isMulti}
        onBlur={onBlur}
        defaultValue={defaultValue}
      />
      {isInvalid && !getValue().length ? (
        <Text as="span" className="select-error__invalid-feedback">
          {msg}
        </Text>
      ) : (
        ''
      )}
    </div>
  );
};

export default CustomSelect;
