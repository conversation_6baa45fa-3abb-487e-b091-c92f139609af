export const renderProductName = (product: any, lng: string): string => {
  if (!product) return '';

  if (!product?.negocio_produto_id) {
    return product?.productName;
  }

  if (!product?.produto?.type && !product?.specie && lng === 'pt-BR') {
    return product?.name_ptbr;
  }

  if (!product?.produto?.type && !product?.specie && lng !== 'pt-BR') {
    return product?.name;
  }

  let specie = '';
  if (lng === 'pt-BR') {
    if (product?.specie && product?.specie?.description_ptbr) {
      specie = `(${product?.specie?.description_ptbr})`;
    }

    if (!specie && product?.specie?.label) {
      specie = `(${product?.specie?.label})`;
    }

    return `${product?.produto?.type?.description_ptbr} ${specie}`;
  }

  if (product?.specie) {
    specie = `(${product?.specie?.description})`;
  }

  if (!specie && product?.specie?.label) {
    specie = `(${product?.specie?.label})`;
  }

  return `${product?.produto?.type?.description} ${specie}`;
};
