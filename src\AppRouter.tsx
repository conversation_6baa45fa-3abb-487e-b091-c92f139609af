import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BrowserRouterProps } from 'react-router-dom';

const AppRouter: React.FC<BrowserRouterProps> = ({ children, ...props }) => {
  // Forçamos o BrowserRouter a ser tratado como um componente que pode ser usado em JSX
  const Router = BrowserRouter as unknown as React.ComponentType<BrowserRouterProps>;
  return <Router {...props}>{children}</Router>;
};

export default AppRouter;
