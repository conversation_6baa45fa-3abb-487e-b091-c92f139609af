import { Links, Meta } from './IMeta';

export interface User {
  usuario_id: number;
  nom_usuario: string;
  des_usuario_acesso: string;
  perfil_id: number;
  arq_imagem_usuario?: any;
  num_celular: string;
  json_endereco?: any;
  cod_lingua: string;
}

export interface UsuarioResponsavel {
  usuario_id: number;
  nom_usuario: string;
  des_usuario_acesso: string;
  perfil_id: number;
  arq_imagem_usuario?: any;
  num_celular: string;
  json_endereco?: any;
  cod_lingua: string;
}

export interface Fornecedor {
  fornecedor_id: number;
  nom_fornecedor: string;
  num_telefone: string;
  json_endereco?: any;
  nom_contato_comercial: string;
  des_email_contato_comercial: string;
  nom_contato_producao: string;
  des_email_contato_producao: string;
  nom_contato_logistica: string;
  des_email_contato_logistica: string;
  nom_contato_despachante?: any;
  des_email_contato_despachante?: any;
  des_observacao?: any;
  dt_inativacao?: any;
  usuario_id_inativacao?: any;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface Trader {
  trader_id: number;
  nom_trader: string;
  num_telefone: string;
  json_endereco: string;
  des_observacao: string;
  dt_inativacao?: any;
  usuario_id_inativacao?: any;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface Cliente {
  tax_identifier_number: any;
  cliente_id: number;
  nom_cliente: string;
  num_telefone: string;
  json_endereco?: any;
  address?: any;
  contact_fullname?: any;
  nom_contato_comercial: string;
  des_email_contato_comercial: string;
  nom_contato_producao?: any;
  des_email_contato_producao?: any;
  nom_contato_logistica: string;
  des_email_contato_logistica: string;
  nom_contato_despachante: string;
  des_email_contato_despachante: string;
  des_observacao: string;
  dt_inativacao?: any;
  usuario_id_inativacao?: any;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
  email: string;
}

export interface Etapa {
  etapa_id: number;
  nom_etapa_pt: string;
  nom_etapa_en: string;
  num_ordem: number;
  des_cor_hexa_fundo: string;
  des_cor_hexa_fonte: string;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface PortoOrigem {
  porto_id: number;
  nom_porto: string;
  cod_pais: string;
  nom_pais: string;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface PortoDestino {
  porto_id: number;
  nom_porto: string;
  cod_pais: string;
  nom_pais: string;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
  data: PortoDestino[];
  current_page: number;
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  next_page_url?: any;
  path: string;
  per_page: number;
  prev_page_url?: any;
  to: number;
  total: number;
}

export interface Carga {
  carga_id: number;
  description: string;
  product_type: string;
  species: string;
  pallets: Pallet[];
  data: Carga[];
  current_page: number;
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  next_page_url?: any;
  path: string;
  per_page: number;
  prev_page_url?: any;
  to: number;
  total: number;
}

export interface NewRomaneio {
  total_volume_m3: string;
  total_pallet: number;
  pallets: RomaneioPallets[];
}

export interface RomaneioModel {
  romaneio_id: number;
  description: string;
  volume_total: string;
  total_pallets: number;
  usuario_id_criacao: number;
  romaneio_pallets: RomaneioPallets[];
  data: RomaneioModel[];
  current_page: number;
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  next_page_url?: any;
  path: string;
  per_page: number;
  prev_page_url?: any;
  to: number;
  total: number;
}

export interface RomaneioPallets{
  romaneio_pallet_id: number;
  romaneio_id: number;
  pallet_id: number;
  pallets: Pallet[];
}

export interface NewRomaneioPallets {
  pallet_id: number;
}

export interface Pallet {
  pallet_id: number;
  quality: string;
  total_m3: string;
  nr_pallet: number;
  thickness: string;
  width?: string;
  length: string;
  nr_pecas_pallet?: number;
  carga_id: number;
  images: PalletImages[];
}

export interface PalletImages {
  id: number;
  pallet_id: number;
  image_path: string;
}

export interface NewPort {
  porto_id: string;
  nom_porto: string;
  cod_pais: string;
  nom_pais: string;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface Produto {
  name: string;
  name_ptbr: string;
  val_preco_prod: any;
  produto_id: number;
  cod_produto: string;
  nom_produto_pt: string;
  nom_produto_en: string;
  cod_produto_tp: string;
  cod_especie: string;
  cod_qualidade: string;
  cod_cola: string;
  list_certificado: string;
  val_comprimento_mm: number;
  val_largura_mm: number;
  val_espessura_mm: number;
  qtd_camada?: number;
  trader_id?: any;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
  glue?: any;
  thickness?: any;
  width?: any;
  layers?: any;
  length?: any;
  quality?: any;
  certificates?: any;
}

export interface NegocioProd {
  negocio_produto_id: number;
  negocio_id: number;
  produto_id: number;
  qtd_peca: number;
  val_volume_m3_prod: string;
  val_comprimento_mm: string;
  val_espessura_mm: string;
  val_largura_mm: string;
  cod_unid_medida_preco: string;
  val_preco_unit: string;
  val_preco_prod: string;
  qtd_peca_palete: number;
  val_volume_m3_prod_palete: string;
  qtd_palete: number;
  des_negocio_prod: string;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
  produto: Produto;
  product: Produto;
  thickness?: string | number | null;
  width?: string | number | null;
  layers?: string | number | null;
  length?: string | number | null;
  certificates?: any;
  qualities?: any;
  finishes?: any;
  glue?: any;
}

export interface NegocioDoc {
  negocio_doc_id: number;
  negocio_id: number;
  documento_id: number;
  etapa_id: number;
  des_documento: string;
  des_arq_documento: string;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface ResponsavelFornecedor {
  usuario_id: number;
  nom_usuario: string;
  des_usuario_acesso: string;
  perfil_id: number;
  arq_imagem_usuario?: any;
  num_celular?: any;
  json_endereco?: any;
  cod_lingua: string;
}

export interface ResponsavelCliente {
  usuario_id: number;
  nom_usuario: string;
  des_usuario_acesso: string;
  perfil_id: number;
  arq_imagem_usuario?: any;
  num_celular?: any;
  json_endereco?: any;
  cod_lingua: string;
}

export interface CidadeDestino {
  cidade_id: number;
  cod_pais: string;
  cod_cidade: string;
  nom_cidade: string;
  nom_cidade_sem_acento: string;
  localidade_id?: number;
  cod_subdivisao: string;
  cod_localizacao: string;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface Incoterm {
  incoterm_id: number;
  cod_incoterm: string;
  nom_incoterm_pt: string;
  nom_incoterm_en: string;
  des_tipo: string;
  des_detalhe: string;
  num_ordem: number;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface CurrentFollowUp {
  arrival_estimate: string;
  business_id: number;
  completion_date: string;
  created_at: string;
  finished: boolean | number;
  notes: string;
  shipment_estimate: string;
  uuid: string;
  status: any;
}

export interface StatusInspecion {
  id: number;
  description: string;
  description_ptbr: string;
  created_at: string;
  updated_at: string;
}

export interface InspectionComplete {
  id: number;
  business_id: number;
  status_id: number;
  realized_in: string;
  created_at: string;
  updated_at: string;
  status: StatusInspecion;
}

export interface NegocioDownload {
  negocio_doc_id: number;
  negocio_id: number;
  documento_id: number;
  etapa_id: number;
  documento: NegocioDoc;
  des_documento: string;
  des_arq_documento: string;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}
export interface IBusiness {
  total_products_business: any;
  buyer_id: any;
  buyer: any;
  freight: any;
  quantity_of_pieces: string;
  estimated_boarding_at: any;
  tolerance_volume_m3: string;
  consignee: any;
  notifyOne: any;
  notifyTwo: any;
  sharedLinks: any;
  responsible: string;
  customer: string;
  contact: string;
  cost_freight: string;
  inspection: InspectionComplete;
  order_date: string;
  data: any;
  negocio_id: number;
  usuario_id_plataforma: number;
  trader_id: number;
  usuario_id_trader: number;
  fornecedor_id: number;
  usuario_id_fornecedor: number;
  cliente_id: number;
  usuario_id_cliente: number;
  cod_ordem_compra: string;
  cod_nota_fiscal: string;
  etapa_id: number;
  des_negocio: string;
  cidade_id_origem?: any;
  porto_id_carga: number;
  porto_id_destino: number;
  cidade_id_destino?: number;
  incoterm_id: number;
  cod_booking: string;
  des_agente_carga: string;
  des_navio: string;
  qtd_container: number;
  cod_tp_container: string;
  val_volume_m3_total: string;
  dt_producao_fim_prev_ini: string;
  dt_producao_fim_prev: string;
  dt_producao_fim_real: string;
  dt_chegada_porto_ori_prev_ini: string;
  dt_chegada_porto_ori_prev: string;
  dt_chegada_porto_ori_real: string;
  dt_saida_porto_ori_prev_ini: string;
  dt_saida_porto_ori_prev: string;
  dt_saida_porto_ori_real: string;
  dt_chegada_porto_dest_prev_ini: string;
  dt_chegada_porto_dest_prev: string;
  dt_chegada_porto_dest_real: string;
  dt_saida_porto_dest_prev_ini?: any;
  dt_saida_porto_dest_prev?: any;
  dt_saida_porto_dest_real?: any;
  dt_chegada_ponto_dest_prev_ini: string;
  dt_chegada_ponto_dest_prev: string;
  dt_chegada_ponto_dest_real: string;
  num_dia_atraso?: any;
  dt_pagamento_prev_ini?: any;
  dt_pagamento_prev?: any;
  dt_pagamento_real: string;
  cod_pagamento?: any;
  des_forma_pagamento: string;
  des_status_pagamento?: any;
  forma_pagamento_id?: any;
  val_negocio_antecipado: string;
  val_negocio_total: string;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
  user: User;
  usuario_responsavel: UsuarioResponsavel;
  fornecedor: Fornecedor;
  trader: Trader;
  cliente: Cliente;
  etapa: Etapa;
  porto_origem: PortoOrigem;
  porto_destino: PortoDestino;
  negocio_prod: NegocioProd[];
  negocio_acao: any[];
  negocio_doc: NegocioDoc[];
  responsavel_fornecedor: ResponsavelFornecedor;
  responsavel_cliente: ResponsavelCliente;
  cidade_origem?: any;
  cidade_destino: CidadeDestino;
  incoterm: Incoterm;
  current_follow_up: CurrentFollowUp;
  consignee_id: any;
  notify_one_id: any;
  notify_two_id: any;
  documents_to_be_issued: string;
  beneficiary?: string;
  beneficiary_account?: string;
  beneficiary_bank_final?: string;
  swift_bank_final?: string;
  intermediary_bank?: string;
  swift_intermediary_bank?: string;
  iban_code?: string;
  additional_info?: string;
  partial_shipments?: string;
  transhipment?: string;
  last_date_shipment?: string;
  insurance?: string;
  desc_pro_forma?: string;
  dynamic_title?: string;
  dynamic_description?: string;
}

export interface IListBusinessResponse {
  data: IBusiness[];
  links: Links;
  meta: Meta;
}

export interface Certificate {
  id: number;
}

export interface Thickness {
  type: string;
  value: number[];
}

export interface Length {
  type: string;
  value: number | number[];
}

export interface Width {
  type: string;
  value: number | number[];
}

export interface IProdutoBusiness {
  id: number;
  uuid: string;
  description: null;
  description_ptbr: null;
  certificates: number[];
  thickness: Thickness[];
  length: Length[];
  width: Width[];
  layers: any[];
  created_at: string;
  inactived: number;
  image: null;
  visibility: number;
  name: string;
  name_ptbr: string;
  species: number[];
  glues: any[];
  qualities: number[];
  finishes: number[];
  observation: null;
  layers_observation: null;
  certificates_backup: null;
}

export interface IPO {
  cod_po: string;
}
export interface IProductsReturn {
  negocio_produto_id: number;
  negocio_id: number;
  produto_id: number;
  qtd_peca: number | null;
  val_volume_m3_prod: string | null;
  cod_unid_medida_preco: string | null;
  val_preco_unit: string;
  val_preco_prod: string | null;
  qtd_peca_palete: number | null;
  val_volume_m3_prod_palete: string | null;
  qtd_palete: number | null;
  des_negocio_prod: null;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
  thickness: number;
  length: number;
  width: number;
  certificates: Certificate[];
  qualities: any[];
  layers: null;
  glue_id: null;
  specie_id: number;
  finishes: any[];
  produto: IProdutoBusiness;
}
