import { t } from 'i18next';

interface Item {
  id: number;
  description: string;
  description_ptbr: string;
  types: number[];
}

export interface RangeProps {
  max: string;
  min: string;
  type: 'range';
}

interface UniqueProps {
  value: string;
  type: 'unique';
}

interface ListProps {
  value: string[];
  type: 'list';
}

type Props = RangeProps | UniqueProps | ListProps;

export function renderPropertiesValues(arr: Props[] | any, isCatalog = false): string {
  if (arr) {
    let obj;
    if (arr && arr.length > 0) {

      obj = arr[0];
    }
    if (!obj) {
      return '';
    }
    if (obj.type === 'range') {
      const { max, min } = obj;
      return `${min} - ${max}`;
    }
    if (obj.type === 'unique') {
      const { value } = obj;
      return value;
    }
    if (isCatalog && obj.type === 'list') {
      const { value } = obj;
      if (value.length > 3) {
        const firstThree = value.slice(0, 3);
        return `${firstThree.join(' | ')}...`;
      }
      return value.join(' | ');
    }
    if (!isCatalog && obj.type === 'list') {
      const { value } = obj;
      return value.join(' | ');
    }
  }
  return '';
}

export function renderDefaultObjectsTable(arr: Item[], language: string): string {
  if (!arr) {
    return '';
  }

  if (arr.length > 0) {
    if ('description' in arr[0]) {
      return arr
        .map((element: Item) => (language === 'USA' ? element.description : element.description_ptbr))
        .join(', ');
    }
    if ('label' in arr[0]) {
      return arr.map((element: any) => element.label).join(', ');
    }
  }

  return `${t('labels.notInformed')}`;
}
