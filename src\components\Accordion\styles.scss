.dataAccordion {
  display: block;

  .accordion-item {
    border-radius: 0.5rem !important;
  }
  .accordion-button:not(.collapsed) {
    background-color: #ffffffff;
  }

  .accordion-header {
    .accordion-button {
      color: var(--basics-secondary, #201e40);
      font-family: Poppins;
      font-size: 1.25rem;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      border-radius: 0.5rem 0.5rem 0 0 !important;
      &:focus {
        box-shadow: 0.5rem var(--teal-200);
        border-color: var(--gray-200);
        outline: var(--teal-200) 0.125rem solid;
      }
    }
    h4 {
      padding-left: 1rem;
      font-size: 1.3rem;
      font-weight: 600;
      color: rgb(32, 30, 64);
    }

    .collapsed {
      border-radius: 0.5rem !important;
    }
  }

  .collapse {
    padding: 0;
    height: 100%;
  }

  .accordion-body {
    padding: 0;
    height: 100%;
  }

  @media (max-width: 768px) {
    padding: 0;
    padding-bottom: 1rem;
  }
}
