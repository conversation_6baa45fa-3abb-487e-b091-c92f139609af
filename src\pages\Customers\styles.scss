.dashboard-container {
  max-width: 1320px;
  margin: 0 auto;
  margin-top: 100px;
}

.customersLimit {
  max-width: 96rem;
  margin-left: auto;
  margin-right: auto;

  /*@media (max-width: 1440px) {
    max-width: 76rem;
  }

  @media (max-width: 1140px) {
    max-width: 52rem;
  }

  @media (max-width: 940px) {
    max-width: 36rem;
  }*/

  .row > * {
    padding-right: 0;
  }
}

.headerTitle {
  text-transform: uppercase !important;
  font-family: 'Poppins' !important;
  color: #8e8e93;
}

.alignButton {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 0.5rem;
}

.table > :not(caption) > * > * {
  padding: 0;
}

.custom-row .rs-table-cell {
  background: #f1f1f1;
}

.containerBackgroundCustomers {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  padding: 0;
  margin: 0;

  .table {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    border-radius: 8px;
    overflow-y: hidden;

    & .rs-table-cell-header-icon-sort {
      color: #bac5c5;
    }

    & .rs-table-scrollbar-vertical {
      opacity: unset;

      & .rs-table-scrollbar-handle {
        background-color: #201e40;
      }
    }

    & .rs-table-cell-content {
      vertical-align: text-bottom !important;
    }

    & .rs-table-scrollbar-horizontal {
      opacity: unset;
      border-radius: 4px;

      @media (max-width: 820px) {
        padding-left: 0rem !important;
        padding-right: 0rem !important;
        margin-left: 0rem !important;
        margin-right: 0rem !important;
      }

      & .rs-table-scrollbar-handle {
        background-color: #201e40;
      }
    }

    & .rs-table-cell {
      font-family: 'Poppins';
      border-right: 1px solid #dfdfdf !important;
    }

    & .rs-table-body-row-wrapper {
      border-top: none;
    }

    & .rs-table-row {
      color: #bac5c5;
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 500;
      font-size: 12px;
      line-height: 20px;
      padding-left: 0;
    }
  }

  & .pagination {
    padding: 20px;
    justify-content: space-between;
    width: 100%;

    span {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 300;
      font-size: 14px;
      line-height: 21px;
      margin-right: 8px;

      @media (max-width: 820px) {
        margin-right: 2px !important;
        white-space: nowrap;
      }
    }

    & .pageGroup {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 2;

      & .selectPerPage button {
        background-color: #ffffff;
        color: #323c32;
        border: 1px solid #bac5c5;
        border-radius: 4px;

        font-family: 'Poppins';
        font-style: normal;
        font-weight: 300;
        font-size: 14px;
        line-height: 21px;

        @media (max-width: 820px) {
          margin-right: 15px !important;
        }
      }

      .dropdown-toggle::after {
        color: #18b680;
      }
    }

    .paginator {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .navigator {
      font-size: 1.8rem;
      cursor: pointer;
      color: #18b680;
    }
  }
}

.customInput {
  border: none;
  line-height: 2.2;
  margin-top: 0.5rem;
  min-width: 31.25rem;
  border-radius: 2px;

  @media (max-width: 768px) {
    min-width: 100% !important;
    margin-left: 0.9rem !important;
    margin-bottom: 0.5rem !important;
  }
}

.colorImage {
  margin-top: 0.5rem;
  width: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  margin-left: 0;

  @media (max-width: 768px) {
    display: none;
  }
}
