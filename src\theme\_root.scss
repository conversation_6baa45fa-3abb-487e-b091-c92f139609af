body {
  font-family: var(--font-family);
  color: var(--gray-900);
  font-size: var(--is-md);
  font-weight: var(--is-400);
  line-height: 1.5;
  overflow-x: hidden;
  background-color: var(--gray-100);
}

.scroll-container {
  overflow-y: scroll;
  height: 300px;
  padding: 10px;
}

.scroll-container::-webkit-scrollbar {
  width: 31.6701%;
  min-width: 14px;
  transform: translate3d(0px, 0px, 0px);
  backface-visibility: hidden;
}

.scroll-container::-webkit-scrollbar-track {
  background: #edf0f0;
  border-radius: 5.78205px;
}

.scroll-container::-webkit-scrollbar-thumb {
  background: var(--gray-200);
  border-radius: 9.5px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
}

.scroll-container::-webkit-scrollbar-thumb:hover {
  background: var(--gray-300);
}

input::-ms-reveal,
input::-ms-clear {
  display: none;
}

main {
  flex: 1 0 auto;
  .container {
    min-width: 100%;
  }
}

select,
input,
table {
  font-family: var(--font-family);
}

.cursor-pointer {
  cursor: pointer;
}

.row > * {
  padding-left: 0;
}
