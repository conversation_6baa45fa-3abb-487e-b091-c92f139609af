import axios, { AxiosError } from 'axios';
import checkTokenIsValid from '../utils/checkTokenIsValid';
import UsersService from './users.service';

const defaultHeaders = {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'Access-Control-Allow-Origin': '*',
  'X-App-Session': 'session_app',
};

const axiosConfig = axios.create({
  baseURL: `${process.env.REACT_APP_API_URL}`,
  //timeout de 1 minuto
  timeout: 1 * 60 * 1000,
  headers: defaultHeaders,
  withCredentials: true,
});

// Interceptor para verificar token antes de cada requisição
axiosConfig.interceptors.request.use(
  async (config) => {
    // Lista de rotas que não precisam de token (públicas)
    const publicRoutes = [
      '/oauth/token',
      '/users/esqueciMinhaSenhaEudr',
      '/users/gerarNovaSenha',
      '/health-check',
      '/newUserForm' // Adicionar rota do formulário de novo usuário
    ];

    // Verifica se a URL atual é uma rota pública
    const isPublicRoute = publicRoutes.some(route => config.url?.includes(route));

    // Só verifica o token se não for uma rota pública
    if (!isPublicRoute) {
      const isTokenValid = checkTokenIsValid('@WoodFlowExporter:token');
      if (!isTokenValid) {
        // Se o token não for válido, redireciona para login
        localStorage.removeItem('@WoodFlowExporter:token');
        localStorage.removeItem('@WoodFlowExporter:user');
        window.location.href = '/login';
        return Promise.reject(new Error('Token inválido ou expirado'));
      }
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add retry interceptor
axiosConfig.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const config = error.config as any;
    
    // Retry on timeout errors
    if (error.code === 'ECONNABORTED' && !config._retry) {
      config._retry = true;
      config.timeout = 180000; // 3 minutes for retry
      return axiosConfig.request(config);
    }
    
    //eslint-disable-next-line no-console
    console.log('Interceptor response', error);
    // Verifica se o erro é de conexão ou timeout
    if (!error.response) {
      // Sem resposta do servidor - problema de conexão
      console.error('Erro de conexão com a API');
      // Você pode adicionar uma notificação aqui
    } else if (error.response.status === 401) {
      // Token expirado ou inválido
      localStorage.removeItem('@WoodFlowExporter:token');
      localStorage.removeItem('@WoodFlowExporter:user');
      window.location.href = '/login';
    }

    return Promise.reject(error);
  }
);

class HttpClient {
  static api = axiosConfig;
  // Método para verificar conexão com a API
  static async checkApiConnection(): Promise<boolean> {
    try {
      //usuarioLogado
      //await axiosConfig.get('/health-check');
      const res = await UsersService.checkToken();
      if (res) {
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }
}

export default HttpClient;
