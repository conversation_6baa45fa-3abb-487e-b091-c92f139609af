import React from 'react';
import { Col } from 'react-bootstrap';
import './styles.scss';

export interface IOptionsInterface {
  label: string;
  value: string;
}

export interface ICheckBoxProps {
  title: string;
  options: IOptionsInterface[];
  defaultKeys: any[];
  setSelected: (selectedItems: string[]) => void;
}

const CheckBox = ({ title, options, defaultKeys, setSelected }: ICheckBoxProps): React.ReactElement => {
  const [newDefaultKey, setNewDefaultKey] = React.useState<any>([]);
  const onChangeCheckBox = (value: any, event: any): void => {
    if (event.target.checked) {
      setSelected([...defaultKeys, value]);
    } else {
      const newColumnsKeys = defaultKeys?.filter((opt: string) => opt !== value).map((opt: string) => opt);

      setSelected(newColumnsKeys);
    }
  };

  React.useEffect(() => {
    setNewDefaultKey(defaultKeys);
  }, [defaultKeys]);

  return (
    <div className="checkedMain">
      <p>{title}</p>
      <div>
        {options?.map((opt: any) => (
          <Col className="d-flex" key={opt.value}>
            <label className="text-checked" htmlFor={opt.value}>
              <>
                <input
                  className="checkFilters"
                  type="checkbox"
                  id={opt.value}
                  value={opt.value}
                  defaultChecked={newDefaultKey?.find((optChecked: string) => optChecked === opt.value)}
                  checked={newDefaultKey?.find((optChecked: string) => optChecked === opt.value)}
                  onChange={(e) => {
                    onChangeCheckBox(opt.value, e);
                  }}
                />
                {opt.label}
              </>
            </label>
          </Col>
        ))}
      </div>
    </div>
  );
};

export default CheckBox;
