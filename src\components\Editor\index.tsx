import React from 'react';
import ReactQuill from 'react-quill';
import { Form } from 'react-bootstrap';
import classNames from 'classnames';
import './styles.scss';

interface IProp {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  id?: string;
  cy?: string;
  isInvalid?: boolean;
  msg?: string;
  label?: string;
  renderKey?: boolean;
}

const Editor = ({
  id,
  placeholder,
  value,
  onChange,
  cy,
  isInvalid,
  label,
  msg,
  renderKey = false,
}: IProp): React.ReactElement => {
  const handleChange = (text: string): void => {
    const val = text.substring(0, 1252);
    onChange(val);
  };
  return (
    <Form.Group controlId={id}>
      <Form.Label>{label}</Form.Label>
      <ReactQuill
        key={renderKey ? id : null}
        placeholder={placeholder}
        data-cy={cy}
        theme="snow"
        value={value}
        onChange={handleChange}
      />
      <Form.Label className={classNames(isInvalid && 'ql-editor__label-error')}>{isInvalid ? msg : ''}</Form.Label>
    </Form.Group>
  );
};

Editor.defaultValue = { isInvalid: false, msg: '', label: '' };

export default Editor;
