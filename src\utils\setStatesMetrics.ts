export const stateMetrics = (setState: any, type: any): any => {
  if (type?.value === 'unique') {
    return setState((prev: any) => ({ ...prev, range: { min: '', max: '' }, list: [] }));
  }
  if (type?.value === 'list') {
    return setState((prev: any) => ({ ...prev, range: { min: '', max: '' }, unique: '' }));
  }
  if (type?.value === 'range') {
    return setState((prev: any) => ({ ...prev, list: [], unique: '' }));
  }
  return setState({ list: [], unique: '', range: { min: '', max: '' } });
};
