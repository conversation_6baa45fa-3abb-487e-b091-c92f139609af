import { Meta } from './IMeta';

export interface ICustomer {
  cliente_id: number;
  nom_cliente: string;
  num_telefone: string;
  json_endereco?: any;
  nom_contato_comercial: string;
  des_email_contato_comercial: string;
  nom_contato_producao: string;
  des_email_contato_producao: string;
  nom_contato_logistica: string;
  des_email_contato_logistica: string;
  nom_contato_despachante: string;
  des_email_contato_despachante: string;
  des_observacao?: any;
  dt_inativacao?: any;
  usuario_id_inativacao?: any;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface INewCompany {
  uuid?: string;
  id?: number;
  email?: string;
  name?: string;
  phone?: string;
  rules: string;
  notes?: string;
  status?: string;
  created_user_id?: number;
  updated_user_id?: number;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  zip_code?: string;
  coordinates?: string;
  profile?: any;
}

export interface Link {
  url: string;
  label: string;
  active: boolean;
}

export interface ICustormerServiceResponse {
  current_page: number;
  data: ICustomer[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: Link[];
  next_page_url?: any;
  path: string;
  per_page: number;
  prev_page_url?: any;
  to: number;
  total: number;
  meta: Meta;
}

export interface ICustomerResponseData {
  usuario_id?: number;
  nom_usuario?: string;
  des_usuario_acesso?: string;
  perfil_id?: number;
  arq_imagem_usuario?: string | null;
  num_celular?: string | null;
  json_endereco?: string | null;
  cod_lingua?: string;
  data: ICustomerResponseData[];
}

export interface ICompanyServiceResponse {
  current_page: number;
  data: INewCompany[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: Link[];
  next_page_url?: any;
  path: string;
  per_page: number;
  prev_page_url?: any;
  to: number;
  total: number;
  meta: Meta;
}

export interface INewSupplyChain {
  id: number;
  uuid: string;
  name: string;
  pedido: string;
  bl: string;
  invoice: string;
  status: string;
  importador: INewCompany;
}

export interface ISupplyChainServiceResponse {
  current_page: number;
  data: INewSupplyChain[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: Link[];
  next_page_url?: any;
  to: number;
  total: number;
  meta: Meta;
}