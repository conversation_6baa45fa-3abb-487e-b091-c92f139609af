import React, { useState, useCallback } from 'react';
import { Table } from 'rsuite';
import { Col, Container, Dropdown, Image, Row } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, ArrowRight } from '@mui/icons-material';
import EmptyState from '../../../../../components/EmptyState';
import EmptyStateImage from '../../../../../statics/emptyState.png';
import { FiEdit2 } from 'react-icons/fi';
import InactiveUser from '../../../../../statics/BsXCircle.svg';
import Modal from '../../../../../components/Modal';
import CustomButton from '../../../../../components/Button';

import '../../../styles.scss';
import { useLoader } from '../../../../../contexts/LoaderContext';
import toastMsg, { ToastType } from '../../../../../utils/toastMsg';
import CardInfoFloresta from '..';
import FlorestaService from '../../../../../services/floresta.service';
import FileGeoJson from '../../../../../components/SupplyChainImporter/file-geojson';

export const TableFloresta = ({
  sortColumn,
  sortType,
  handleSortColumn,
  isMobile,
  data,
  tableColumns,
  page,
  lastPage,
  limit,
  handleChangeLimit,
  handleDownPage,
  handleUpPage,
  showLoader,
  idFornecedor,
  atualziarFlorestas,
  setUploadError,
}: any): React.ReactElement => {
  const { Column, HeaderCell, Cell } = Table;
  const { t } = useTranslation();
  const [show, setShow] = useState<any>({ show: false, florestaId: null });
  const { setShowLoader } = useLoader();
  const [filesFlorestaMap, setFilesFlorestaMap] = useState<{ [florestaId: string]: any[] }>({});
  const [showModalDocument, setShowModalDocument] = useState<any>({ show: false, floresta: null });
  const [showModalShapes, setShowModalShapes] = useState<any>({ show: false, floresta: null });

  const getDocumentoFloresta = useCallback(async (idF: number): Promise<any[]> => {
    const florestaRes = await FlorestaService.documentoFlorestaById(String(idF) || '');

    interface NewFile {
      file: File;
      preview: string;
      vencimento: Date;
      path: string;
      idDoc: string;
      tamanho_arquivo: string;
    }

    if (florestaRes && florestaRes.documento && florestaRes.documento.documento_path) {
      const newFile: NewFile = {
        file: new File([''], florestaRes.documento.nome || 'CAR - Cadastro Ambiental Rural'),
        preview: florestaRes.documento.documento_path,
        vencimento: new Date(florestaRes.documento.vencimento || Date.now()),
        path: florestaRes.documento.documento_path,
        idDoc: florestaRes.id?.toString() || '',
        tamanho_arquivo: florestaRes.documento.tamanho_arquivo,
      };

      return [newFile];
    }
    return [];
  }, []);

  // Carrega o filesFlorestaMap sempre no carregamento usando o parâmetro "data"
  React.useEffect(() => {
    if (data && data.length > 0) {
      let execute = true;

      const loadAllDocuments = async () => {
        const newFilesMap: { [florestaId: string]: any[] } = {};

        // Carrega todos os documentos das florestas
        for (const floresta of data) {
          const files = await getDocumentoFloresta(floresta.id);
          newFilesMap[floresta.id] = files;
        }
        //eslint-disable-next-line
        console.log('newFilesMap', newFilesMap);
        setFilesFlorestaMap(newFilesMap);
      };

      if (execute) {
        loadAllDocuments();
      }

      return () => {
        execute = false;
      };
    }
  }, [data, getDocumentoFloresta]);

  const handleDelete = async (florestaId: any): Promise<void> => {
    try {
      setShowLoader(true);
      await FlorestaService.deleteFloresta(florestaId);
      toastMsg(ToastType.Success, t('response.deleteSuccess'));
      setShowLoader(false);
      setShow(false);
      atualziarFlorestas();
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    }
  };

  const getDocumentNameLimited = (name: string): string => {
    if (name.length <= 45) {
      return name;
    }

    // Extract the file extension
    const lastDotIndex = name.lastIndexOf('.');
    const extension = lastDotIndex !== -1 ? name.substring(lastDotIndex) : '';

    // Calculate how much of the name we can keep
    const maxNameLength = 45 - extension.length;

    if (maxNameLength <= 0) {
      // If extension is too long, just truncate the whole name
      return name.substring(0, 45);
    }

    // Truncate name and append original extension
    return name.substring(0, maxNameLength) + extension;
  };

  const getDocumentName = (path: string): string => {
    const parts = path.split('/');
    if (parts.length > 0) {
      return getDocumentNameLimited(parts[parts.length - 1]);
    }
    return '';
  };

  const formatFileSize = (bytes: number) => {
    // Format file size
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];

    if (bytes < k) {
      return bytes + ' ' + sizes[0];
    } else if (bytes < k * k) {
      return parseFloat((bytes / k).toFixed(2)) + ' ' + sizes[1];
    } else if (bytes < k * k * k) {
      return parseFloat((bytes / (k * k)).toFixed(2)) + ' ' + sizes[2];
    } else {
      return parseFloat((bytes / (k * k * k)).toFixed(2)) + ' ' + sizes[3];
    }
  };

  return (
    <Container className="containerBackgroundCustomers">
      <Modal
        show={show.show}
        handleClose={() => setShow({ ...show, show: false })}
        title={t('titles.removeFloresta')}
        size="lg"
        className="styleModalConfirm"
        colorIcon
      >
        <Container>
          <Row className="mt-4 p-2">
            <Col className="d-flex align-items-center ">
              <h6>
                {t('modal.removerFloresta')}
                <br />
                {t('modal.removeProductDescription')}
              </h6>
            </Col>
          </Row>
        </Container>

        <Row className="mt-4">
          <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
            <CustomButton
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={() => {
                setShow(false);
              }}
            >
              {t('buttons.cancel')}
            </CustomButton>
            <CustomButton cy="btn-save" type="button" variant="danger" onClick={() => handleDelete(show.florestaId)}>
              {t('buttons.delete')}
            </CustomButton>
          </Col>
        </Row>
      </Modal>

      <Modal
        show={showModalDocument.show}
        handleClose={() => setShowModalDocument({ ...showModalDocument, show: false })}
        title={t('titles.editFloresta')}
        size="xl"
        className="styleModalConfirm"
        colorIcon
      >
        <CardInfoFloresta
          idFornecedor={idFornecedor}
          closeModal={setShowModalDocument}
          atualziarFlorestas={atualziarFlorestas}
          setUploadError={setUploadError}
          car={showModalDocument.floresta?.documento}
          floresta={showModalDocument.floresta}
        />
      </Modal>

      <Modal
        show={showModalShapes.show}
        handleClose={() => setShowModalShapes({ ...showModalShapes, show: false, floresta: null })}
        title={t('titles.documentoCar')}
        size="xl"
        className="styleModalConfirm"
        colorIcon
      >
        <div className="p-4">
          {showModalShapes.floresta ? (
            <>
              <div className="mb-3">
                <strong>Floresta:</strong> {showModalShapes.floresta.nome || 'Nome não disponível'}
              </div>
              {filesFlorestaMap[showModalShapes.floresta.id]?.length > 0 ? (
                filesFlorestaMap[showModalShapes.floresta.id].map((file: any, idx: number) => (
                  <FileGeoJson
                    key={idx}
                    filename={file.path ? getDocumentName(file.path || '') : file.file.name}
                    fileSize={file.tamanho_arquivo ? file.tamanho_arquivo : formatFileSize(file.file.size)}
                    filepath={file.path ? file.path : ''}
                    documentId={file.idDoc}
                  />
                ))
              ) : (
                <div className="text-center p-4">
                  <p>Nenhum arquivo de shape encontrado para esta floresta.</p>
                  <small className="text-muted">
                    Os arquivos de shape (GeoJSON) são carregados automaticamente quando disponíveis.
                  </small>
                </div>
              )}
            </>
          ) : (
            <div className="text-center p-4">
              <p>Nenhuma floresta selecionada.</p>
            </div>
          )}
        </div>
      </Modal>

      {data.length > 0 ? (
        <>
          <Table
            bordered
            cellBordered
            sortColumn={sortColumn}
            sortType={sortType}
            onSortColumn={handleSortColumn}
            autoHeight={isMobile}
            height={400}
            affixHorizontalScrollbar={!isMobile}
            data={data}
            className="table"
            rowClassName={(rowData: any) => rowData?.className || ''}
          >
            {tableColumns.map((column: any) => {
              const { field, headerName, color, cursor, textDecoration, fixed, ...rest } = column;

              if (field === 'actions') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            <div
                              onClick={() => {
                                setShowModalDocument({ show: true, floresta: rowData });
                              }}
                              role="presentation"
                              title={t('titles.editFloresta')}
                              style={{ cursor: 'pointer', color: '#494747' }}
                            >
                              <FiEdit2 size={20} color="green" />
                            </div>
                            <div
                              onClick={() => setShow({ show: true, florestaId: rowData.id })}
                              role="presentation"
                              title={t('titles.removeFloresta')}
                              style={{ cursor: 'pointer' }}
                            >
                              <Image src={InactiveUser} height={20} />
                            </div>
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              if (field === 'car') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) =>
                        (rowData.car !== 'semdoc' && (
                          <>
                            <button
                              type="button"
                              className="btn btn-link p-0"
                              style={{ fontSize: '12px' }}
                              onClick={() => {
                                setShowModalShapes({ show: true, floresta: rowData });
                              }}
                            >
                              CAR - Cadastro Ambiental Rural
                            </button>
                          </>
                        )) || <a rel="noreferrer">{t('labels.withoutDocument')}</a>
                      }
                    </Cell>
                  </Column>
                );
              }

              return (
                <Column {...rest} key={field} fixed={fixed}>
                  <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                  <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap />
                </Column>
              );
            })}
          </Table>
          <div className="pagination">
            <div className="pageGroup">
              {!isMobile && <span>{t('pagination.clientsPerPage')}</span>}
              <Dropdown className="selectPerPage">
                <Dropdown.Toggle id="dropdown-limit">{limit}</Dropdown.Toggle>
                <Dropdown.Menu>
                  <Dropdown.Item onClick={() => handleChangeLimit(10)}>10</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(30)}>30</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(50)}>50</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(70)}>70</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(100)}>100</Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            </div>
            <div className="paginator">
              <span>
                {t('pagination.page')} {page} {t('pagination.of')} {lastPage}
              </span>
              <ArrowLeft className="navigator" onClick={handleDownPage} />
              <ArrowRight className="navigator" onClick={handleUpPage} />
            </div>
          </div>
        </>
      ) : (
        <>
          {!showLoader && (
            <EmptyState
              text={t('labels.labelCustomerNotFound')}
              secondaryText={t('labels.labelDescCustomerNotFound')}
              img={EmptyStateImage}
            />
          )}
        </>
      )}
    </Container>
  );
};
