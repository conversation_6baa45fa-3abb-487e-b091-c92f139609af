import React, { useState } from 'react';
import { Table } from 'rsuite';
import { Col, Container, Dropdown, Image, Row } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, ArrowRight } from '@mui/icons-material';
import EmptyState from '../../../../../components/EmptyState';
import EmptyStateImage from '../../../../../statics/emptyState.png';
import { FiEdit2 } from 'react-icons/fi';
import InactiveUser from '../../../../../statics/BsXCircle.svg';
import Modal from '../../../../../components/Modal';
import CustomButton from '../../../../../components/Button';

import '../../../styles.scss';
import { useLoader } from '../../../../../contexts/LoaderContext';
import toastMsg, { ToastType } from '../../../../../utils/toastMsg';
import CardInfoMateriaPrima from '..';
import FlorestaService from '../../../../../services/floresta.service';

export const TableMateriaPrima = ({
  sortColumn,
  sortType,
  handleSortColumn,
  isMobile,
  data,
  tableColumns,
  page,
  lastPage,
  limit,
  handleChangeLimit,
  handleDownPage,
  handleUpPage,
  showLoader,
  idFornecedor,
  atualziarMateriaPrima,
}: any): React.ReactElement => {
  const { Column, HeaderCell, Cell } = Table;
  const { t } = useTranslation();
  const [show, setShow] = useState<any>({ show: false, mtId: null });
  const { setShowLoader } = useLoader();

  const [showModalDocument, setShowModalDocument] = useState<any>({ show: false, materiaPrima: null });

  const handleDelete = async (mtId: any): Promise<void> => {
    try {
      setShowLoader(true);

      await FlorestaService.deleteMateriaPrima(mtId);
      toastMsg(ToastType.Success, t('response.deleteSuccess'));
      setShowLoader(false);
      setShow(false);
      atualziarMateriaPrima();
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  };

  return (
    <Container className="containerBackgroundCustomers">
      <Modal
        show={show.show}
        handleClose={() => setShow({ ...show, show: false })}
        title={t('titles.removeMateriaPrima')}
        size="lg"
        className="styleModalConfirm"
        colorIcon
      >
        <Container>
          <Row className="mt-4 p-2">
            <Col className="d-flex align-items-center ">
              <h6>
                {t('modal.removeMateriaPrima')}
                <br />
                {t('modal.removeProductDescription')}
              </h6>
            </Col>
          </Row>
        </Container>

        <Row className="mt-4">
          <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
            <CustomButton
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={() => {
                setShow(false);
              }}
            >
              {t('buttons.cancel')}
            </CustomButton>
            <CustomButton cy="btn-save" type="button" variant="danger" onClick={() => handleDelete(show.mtId)}>
              {t('buttons.delete')}
            </CustomButton>
          </Col>
        </Row>
      </Modal>

      <Modal
        show={showModalDocument.show}
        handleClose={() => setShowModalDocument({ ...showModalDocument, show: false })}
        title={t('titles.editMateriaPrima')}
        size="xl"
        className="styleModalConfirm"
        colorIcon
      >
        <CardInfoMateriaPrima
          idFornecedor={idFornecedor}
          closeModal={setShowModalDocument}
          atualziarMateriaPrima={atualziarMateriaPrima}
          materiaPrima={showModalDocument.materiaPrima}
        />
      </Modal>

      {data.length > 0 ? (
        <>
          <Table
            bordered
            cellBordered
            sortColumn={sortColumn}
            sortType={sortType}
            onSortColumn={handleSortColumn}
            autoHeight={isMobile}
            height={400}
            affixHorizontalScrollbar={!isMobile}
            data={data}
            className="table"
            rowClassName={(rowData: any) => rowData?.className || ''}
          >
            {tableColumns.map((column: any) => {
              const { field, headerName, color, cursor, textDecoration, fixed, ...rest } = column;

              if (field === 'actions') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            <div
                              onClick={() => {
                                setShowModalDocument({ show: true, materiaPrima: rowData });
                              }}
                              role="presentation"
                              title={t('titles.editMateriaPrima')}
                              style={{ cursor: 'pointer', color: '#494747' }}
                            >
                              <FiEdit2 size={20} color="green" />
                            </div>
                            <div
                              onClick={() => setShow({ show: true, mtId: rowData.uuid })}
                              role="presentation"
                              title={t('titles.removeMateriaPrima')}
                              style={{ cursor: 'pointer' }}
                            >
                              <Image src={InactiveUser} height={20} />
                            </div>
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              return (
                <>
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap />
                  </Column>
                </>
              );
            })}
          </Table>
          <div className="pagination">
            <div className="pageGroup">
              {!isMobile && <span>{t('pagination.clientsPerPage')}</span>}
              <Dropdown className="selectPerPage">
                <Dropdown.Toggle id="dropdown-limit">{limit}</Dropdown.Toggle>
                <Dropdown.Menu>
                  <Dropdown.Item onClick={() => handleChangeLimit(10)}>10</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(30)}>30</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(50)}>50</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(70)}>70</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(100)}>100</Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            </div>
            <div className="paginator">
              <span>
                {t('pagination.page')} {page} {t('pagination.of')} {lastPage}
              </span>
              <ArrowLeft className="navigator" onClick={handleDownPage} />
              <ArrowRight className="navigator" onClick={handleUpPage} />
            </div>
          </div>
        </>
      ) : (
        <>
          {!showLoader && (
            <EmptyState
              text={t('labels.labelCustomerNotFound')}
              secondaryText={t('labels.labelDescCustomerNotFound')}
              img={EmptyStateImage}
            />
          )}
        </>
      )}
    </Container>
  );
};
