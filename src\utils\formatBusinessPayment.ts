import { IBusiness } from '../interfaces';

export const formatBusinessPayment = (data: Partial<IBusiness> | undefined): Partial<IBusiness> =>
  data
    ? {
        des_forma_pagamento: data?.des_forma_pagamento,
        val_negocio_antecipado: data?.val_negocio_antecipado,
        val_negocio_total: data?.val_negocio_total,
        des_status_pagamento: data?.des_status_pagamento,
        cost_freight: data?.cost_freight,
        beneficiary: data?.beneficiary,
        beneficiary_account: data?.beneficiary_account,
        beneficiary_bank_final: data?.beneficiary_bank_final,
        swift_bank_final: data?.swift_bank_final,
        intermediary_bank: data?.intermediary_bank,
        swift_intermediary_bank: data?.swift_intermediary_bank,
        iban_code: data?.iban_code,
        additional_info: data?.additional_info,
      }
    : {};
