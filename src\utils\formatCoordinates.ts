/**
 * @description
 * Utilitários para formatação de coordenadas geográficas (latitude e longitude)
 */

/**
 * Formata latitude com máscara
 * Formato: -90.000000 a +90.000000
 * @param {string} value - Valor da latitude
 * @return {string} Latitude formatada
 */
export const formatLatitude = (value: string): string => {
  if (!value) return '';
  
  // Remove caracteres não permitidos (mantém apenas números, ponto, vírgula e sinal de menos)
  let cleanValue = value.replace(/[^0-9.,-]/g, '');
  
  // Substitui vírgula por ponto para padronização
  cleanValue = cleanValue.replace(',', '.');
  
  // Garante apenas um ponto decimal
  const parts = cleanValue.split('.');
  if (parts.length > 2) {
    cleanValue = parts[0] + '.' + parts.slice(1).join('');
  }
  
  // Limita a 6 casas decimais
  if (parts.length === 2 && parts[1].length > 6) {
    cleanValue = parts[0] + '.' + parts[1].substring(0, 6);
  }
  
  // Converte para número para validação
  const numValue = parseFloat(cleanValue);
  
  // Valida range da latitude (-90 a +90)
  if (!isNaN(numValue)) {
    if (numValue > 90) {
      return '90.000000';
    } else if (numValue < -90) {
      return '-90.000000';
    }
  }
  
  return cleanValue;
};

/**
 * Formata longitude com máscara
 * Formato: -180.000000 a +180.000000
 * @param {string} value - Valor da longitude
 * @return {string} Longitude formatada
 */
export const formatLongitude = (value: string): string => {
  if (!value) return '';
  
  // Remove caracteres não permitidos (mantém apenas números, ponto, vírgula e sinal de menos)
  let cleanValue = value.replace(/[^0-9.,-]/g, '');
  
  // Substitui vírgula por ponto para padronização
  cleanValue = cleanValue.replace(',', '.');
  
  // Garante apenas um ponto decimal
  const parts = cleanValue.split('.');
  if (parts.length > 2) {
    cleanValue = parts[0] + '.' + parts.slice(1).join('');
  }
  
  // Limita a 6 casas decimais
  if (parts.length === 2 && parts[1].length > 6) {
    cleanValue = parts[0] + '.' + parts[1].substring(0, 6);
  }
  
  // Converte para número para validação
  const numValue = parseFloat(cleanValue);
  
  // Valida range da longitude (-180 a +180)
  if (!isNaN(numValue)) {
    if (numValue > 180) {
      return '180.000000';
    } else if (numValue < -180) {
      return '-180.000000';
    }
  }
  
  return cleanValue;
};

/**
 * Valida se uma latitude está no formato correto
 * @param {string} latitude - Valor da latitude
 * @return {boolean} True se válida
 */
export const isValidLatitude = (latitude: string): boolean => {
  if (!latitude) return false;
  
  const num = parseFloat(latitude);
  return !isNaN(num) && num >= -90 && num <= 90;
};

/**
 * Valida se uma longitude está no formato correto
 * @param {string} longitude - Valor da longitude
 * @return {boolean} True se válida
 */
export const isValidLongitude = (longitude: string): boolean => {
  if (!longitude) return false;
  
  const num = parseFloat(longitude);
  return !isNaN(num) && num >= -180 && num <= 180;
};

/**
 * Formata coordenadas para exibição
 * @param {string} latitude - Valor da latitude
 * @param {string} longitude - Valor da longitude
 * @return {string} Coordenadas formatadas para exibição
 */
export const formatCoordinatesDisplay = (latitude: string, longitude: string): string => {
  if (!latitude || !longitude) return '';
  
  const lat = parseFloat(latitude);
  const lng = parseFloat(longitude);
  
  if (isNaN(lat) || isNaN(lng)) return '';
  
  const latDirection = lat >= 0 ? 'N' : 'S';
  const lngDirection = lng >= 0 ? 'E' : 'W';
  
  return `${Math.abs(lat).toFixed(6)}°${latDirection}, ${Math.abs(lng).toFixed(6)}°${lngDirection}`;
};
