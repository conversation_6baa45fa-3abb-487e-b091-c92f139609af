.cardProducts {
  display: flex;
  margin-right: 2rem;
  margin-top: 0.9rem;
  max-height: 34.6rem;
  min-height: 34.6rem;
  position: relative;
  .rs-panel-body {
    flex: 1;
    min-height: 15.6rem;
    padding: 0.8rem;
  }

  .redirect {
    color: #201e40;
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    margin-top: 1rem;
    text-decoration: none;
    cursor: pointer;
  }

  .redirectHome {
    margin-top: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    color: #18b680;
    cursor: pointer;
  }
  .specieLayout {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #203245;
  }
  .cardOptionsLayout {
    display: flex;
    flex-direction: row;
    align-items: center;
    min-width: max-content;
    max-width: max-content;
    padding: 3px 3px;
    gap: 2px;
    background: rgba(3, 29, 135, 0.08);
    border: 1px solid #201e40;
    border-radius: 16px;
    margin-left: 0.5rem;
  }
  h4 {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
    min-height: 20px;
    color: #201e40;
  }
  p {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    min-height: 20px;
    color: #54666f;
  }

  .btnReturn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 3rem;
    min-width: 100%;
    border-radius: 8px;
    margin-right: 0.7rem;
    border-color: #18b680;
    color: #18b680;
  }
  span {
    font-family: 'Poppins';
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    min-height: 20px;
    color: #201e40;
  }
  small {
    font-family: 'Nunito';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 23px;
    min-height: 23px;
    color: #54666f;
  }
  .viewMore {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    text-align: right;
    text-decoration-line: underline;
    color: #201e40;
    cursor: pointer;
    margin-top: 1rem;
    position: relative;
    right: 0;
    bottom: 0;
  }
}

.modal-title {
  font-family: 'Nunito';
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 27px;

  color: #201e40;
}

.modalProduct {
  margin-left: 0.6rem;
  margin-top: 0.7rem;

  .title {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 24px;
    color: #201e40;
  }

  .subTitle {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    color: #203245;
  }
  .desc {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #54666f;
  }
  span {
    font-family: 'Poppins';
    font-weight: 700;
    font-size: 14px;
    line-height: 20px;
    color: #201e40;
  }
  .numbers {
    font-family: 'Nunito';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;

    color: #54666f;
  }
  .cardOptionsLayout {
    display: flex;
    flex-direction: row;
    align-items: center;
    min-width: max-content;
    max-width: max-content;
    padding: 3px 3px;
    gap: 2px;
    background: rgba(3, 29, 135, 0.08);
    border: 1px solid #201e40;
    border-radius: 16px;
  }
  .btnReturn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 3rem;
    min-width: 70%;
    border-radius: 8px;
    margin-right: 0.7rem;
    border-color: #18b680;
    color: #18b680;
  }

  .btnHome {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 3rem;
    min-width: 100%;
    border-radius: 8px;
    margin-right: 0.7rem;
    border-color: #18b680;
    color: #18b680;
  }
}

.shipper {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  margin-bottom: 1.4rem;
}

.btn-style {
  width: 100%;
  text-align: center;
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 23px;
  color: #ffffff;
}
