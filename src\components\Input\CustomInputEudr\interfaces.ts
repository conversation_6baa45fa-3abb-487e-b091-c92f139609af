import React from 'react';

export interface IInput {
  value?: string;
  placeholder?: string;
  maxLength?: number;
  minLength?: number;
  id: string;
  label?: string;
  readOnly?: boolean | false;
  type: 'text' | 'email' | 'password' | 'numeric' | 'hidden';
  required?: boolean | false;
  disabled?: boolean | false;
  isInvalid?: boolean | false;
  desc?: string;
  msg?: string;
  className?: string;
  tabIndex?: number | 0;
  ref?: React.Ref<HTMLInputElement | HTMLElement | string | any | undefined>;
  onChange?: React.ChangeEventHandler<HTMLInputElement> | undefined;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onKeyPress?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onKeyUp?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  cy: string;
  name?: string;
  defaultValue?: string;
}
