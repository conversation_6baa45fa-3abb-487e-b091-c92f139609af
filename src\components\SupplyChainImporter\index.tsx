import React, { useCallback, useState } from 'react';
import { ChevronDown, ChevronLeft, ChevronUp, Download } from 'lucide-react';
import FileAttachment from './file-attachment';
import Button from '../Button';
import './styles.scss';
import { useTranslation } from 'react-i18next';
import { useLoader } from '../../contexts/LoaderContext';
import { useAuth } from '../../contexts/AuthContext';
import DocumentosService from '../../services/documentos.service';
import { format } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';
import Text from '../Text';
import { useHistory } from 'react-router';
import SupplyChainService from '../../services/supplyChain.service';
import toastMsg, { ToastType } from '../../utils/toastMsg';
import FlorestaService, { IpropsMateriaPrima } from '../../services/floresta.service';
import FileGeoJson from './file-geojson';
import { DownloadArquivo } from '../../pages/DashboardImporter/UploadArquivo';

// Função para obter o tamanho real do arquivo
const getFileSize = async (filePath: string): Promise<number> => {
  try {
    // Se o caminho não é uma URL completa, construir a URL base
    let fullUrl = filePath;
    if (!filePath.startsWith('http')) {
      // Assumir que é um caminho relativo e construir URL completa
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8081';
      fullUrl = `${baseUrl}${filePath.startsWith('/') ? '' : '/'}${filePath}`;
    }

    const response = await fetch(fullUrl, {
      method: 'HEAD',
      headers: {
        Accept: '*/*',
      },
    });

    if (!response.ok) {
      return 0;
    }

    const contentLength = response.headers.get('content-length');
    const size = contentLength ? parseInt(contentLength, 10) : 0;

    return size;
  } catch (error) {
    return 0;
  }
};

// Componente para exibir notas fiscais de uma espécie
const NotasFiscaisSection = ({
  especieId,
  t,
  formatFileSize,
}: {
  especieId: string;
  t: any;
  formatFileSize: (size: number) => string;
}) => {
  const [notasFiscais, setNotasFiscais] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  React.useEffect(() => {
    const loadNotasFiscais = async () => {
      if (!especieId) {
        return;
      }

      setLoading(true);
      try {
        // Tentar primeiro o endpoint específico para documentos de espécies
        let response;
        try {
          response = await SupplyChainService.getDocumentosEspecie(especieId);
        } catch (error) {
          // Fallback para o endpoint genérico
          response = await SupplyChainService.getDocumentoBySupplyChainId(especieId);
        }

        if (response) {
          let documentos = [];

          // Verificar diferentes estruturas de resposta
          if (Array.isArray(response)) {
            documentos = response;
          } else if (response.data && Array.isArray(response.data)) {
            documentos = response.data;
          } else if (response.data && response.data.documento) {
            documentos = [response.data];
          } else if (response.data && response.data.documentos && Array.isArray(response.data.documentos)) {
            documentos = response.data.documentos.map((doc: any) => ({
              documento: doc,
            }));
          } else if (response.documento) {
            documentos = [response];
          }

          // Processar documentos - podem estar em diferentes estruturas
          // Filtrar documentos que são notas fiscais
          const documentosFiltrados = documentos.filter((doc: any) => {
            // O documento pode estar em doc.documento ou diretamente em doc
            const documento = doc.documento || doc;
            const isNotaFiscal =
              documento &&
              documento.documento_path &&
              (documento.tipo_documento_id === 32 ||
                documento.tipo_documento_id === '32' ||
                documento.nome?.toLowerCase().includes('nota fiscal') ||
                documento.nome?.toLowerCase().includes('invoice') ||
                documento.nome?.toLowerCase().includes('nf'));

            return isNotaFiscal;
          });

          // Processar cada documento e calcular tamanho real
          const notasFiscaisPromises = documentosFiltrados.map(async (doc: any) => {
            const documento = doc.documento || doc;
            const fileName = documento.documento_path?.split('/').pop() || documento.nome || 'documento.pdf';

            // Calcular o tamanho real do arquivo
            let tamanhoReal = documento.tamanho || 0;
            if (documento.documento_path && !tamanhoReal) {
              try {
                tamanhoReal = await getFileSize(documento.documento_path);

                // Se ainda não conseguiu obter o tamanho, usar estimativa baseada no tipo
                if (!tamanhoReal) {
                  const extension = fileName.split('.').pop()?.toLowerCase();
                  switch (extension) {
                    case 'pdf':
                      tamanhoReal = 512 * 1024; // 512KB estimado para PDF
                      break;
                    case 'jpg':
                    case 'jpeg':
                    case 'png':
                      tamanhoReal = 256 * 1024; // 256KB estimado para imagem
                      break;
                    case 'doc':
                    case 'docx':
                      tamanhoReal = 128 * 1024; // 128KB estimado para documento
                      break;
                    default:
                      tamanhoReal = 64 * 1024; // 64KB padrão
                  }
                }
              } catch (error) {
                console.error('Erro ao obter tamanho do arquivo:', fileName, error);
                tamanhoReal = 64 * 1024; // 64KB padrão se falhar
              }
            }

            return {
              nome: fileName,
              caminho: documento.documento_path,
              id: doc.id || documento.id,
              tamanho_arquivo: documento.tamanho_arquivo,
            };
          });

          // Aguardar todos os cálculos de tamanho
          const notasFiscaisData = await Promise.all(notasFiscaisPromises);
          setNotasFiscais(notasFiscaisData);
        } else {
          //eslint-disable-next-line
          console.log('NotasFiscaisSection: Nenhum dado encontrado na resposta');
        }
      } catch (error) {
        //eslint-disable-next-line
        console.error('NotasFiscaisSection: Erro ao carregar notas fiscais:', error);
        setNotasFiscais([]);
      } finally {
        setLoading(false);
      }
    };

    loadNotasFiscais();
  }, [especieId]);

  if (loading) {
    return (
      <div className="mt-6">
        <label className="block text-sm font-medium text-gray-700 mb-1">{t('uploadFiles.fiscalNoteFloresta')}</label>
        <p className="text-gray-500">Loading...</p>
      </div>
    );
  }

  if (!notasFiscais || notasFiscais.length === 0) {
    return null;
  }

  return (
    <div className="mt-6">
      <label className="block text-sm font-medium text-gray-700 mb-1">{t('uploadFiles.fiscalNoteFloresta')}</label>
      {notasFiscais.map((nota, index) => (
        <FileAttachment
          key={index}
          filename={nota.nome}
          fileSize={nota.tamanho_arquivo ? nota.tamanho_arquivo : formatFileSize(nota.tamanho)}
          filepath={nota.caminho}
        />
      ))}
    </div>
  );
};

// Supply Chain Flow Component
const SupplyChainFlow = ({
  species,
  materiasPrima,
  company,
  supplyChainProduct,
  supplyChain,
  t,
}: {
  species: any[];
  materiasPrima: IpropsMateriaPrima[];
  company: any;
  supplyChainProduct: any;
  supplyChain: any;
  t: any;
}) => {
  return (
    <div className="supply-chain-flow bg-white rounded-lg shadow p-6 mb-6">
      <h2 className="text-2xl font-bold text-center mb-8 text-gray-800">
        {t('titles.supplyChainFlow') || 'Fluxo da Cadeia de Suprimentos'}
      </h2>

      <div className="flow-container-visual">
        <table className="supply-chain-flow-table" style={{ marginLeft: '30px' }}>
          <tr>
            <td style={{ verticalAlign: 'bottom' }}>
              <div className="row" style={{ width: '100%', margin: '0 auto' }}>
                <table style={{ padding: 0 }} cellSpacing="0" cellPadding="0">
                  <tr style={{ height: '177px' }}>
                    <td width="153px" style={{ verticalAlign: 'bottom' }}>
                      <img
                        src="https://woodflow-attachments.s3.us-east-1.amazonaws.com/eudr/etap_01.png"
                        alt="Log Supply"
                      />
                    </td>
                    <td colSpan={2} width="83%" style={{ textAlign: 'left', paddingLeft: '5px' }}>
                      <div style={{ width: '83%' }}>
                        <h3>Log Supply</h3>
                        {species.length > 0 && (
                          <div className="step-details" style={{ width: '83%' }}>
                            {species.map((specie, index) => (
                              <div key={specie.id || index} className="mb-4" style={{ width: '83%' }}>
                                <p>
                                  <strong>Supplier {index + 1}:</strong> {specie?.fornecedor?.name}
                                  <br></br>
                                  <strong>Forest {index + 1}:</strong> {specie?.floresta?.nome}
                                  {specie?.floresta?.latitude && specie?.floresta?.longitude && (
                                    <>
                                      <br></br>
                                      <strong>Latitude:</strong> {specie?.floresta?.latitude} |{' '}
                                      <strong>Longitude:</strong> {specie?.floresta?.longitude}
                                    </>
                                  )}
                                  {materiasPrima.length > index && (
                                    <>
                                      <br></br>
                                      <strong>Botanical Name {index + 1}:</strong>{' '}
                                      {materiasPrima[index]?.nome_cientifico}
                                    </>
                                  )}
                                </p>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                  <tr style={{ height: '11px' }}>
                    <td
                      width="153px"
                      height="11px"
                      style={{
                        backgroundImage:
                          "url('https://woodflow-attachments.s3.us-east-1.amazonaws.com/eudr/bottom_etap_01.png')",
                        backgroundRepeat: 'no-repeat',
                      }}
                    ></td>
                    <td
                      width="630px"
                      height="11px"
                      style={{
                        backgroundImage:
                          "url('https://woodflow-attachments.s3.us-east-1.amazonaws.com/eudr/bg_linha.png')",
                        backgroundRepeat: 'repeat-x',
                      }}
                    ></td>
                    <td
                      width="152px"
                      height="11px"
                      style={{
                        backgroundImage:
                          "url('https://woodflow-attachments.s3.us-east-1.amazonaws.com/eudr/top_etap_04.png')",
                        backgroundRepeat: 'no-repeat',
                      }}
                    ></td>
                  </tr>
                  <tr style={{ height: '212px' }}>
                    <td colSpan={2} width="83%" style={{ textAlign: 'right', paddingRight: '5px' }}>
                      <h3>Manufacturing</h3>
                      <div className="step-details">
                        <p>
                          <strong>Exporter:</strong> {company.razaoSocial}
                          <br></br>
                          <strong>Product:</strong> {supplyChainProduct.product_description}
                          <br></br>
                          <strong>Amount:</strong>{' '}
                          {[
                            supplyChainProduct.product_quantity && `${supplyChainProduct.product_quantity} m³`,
                            supplyChainProduct.product_quantity_tons &&
                              `${supplyChainProduct.product_quantity_tons.replace(
                                /(\d+).(\d{3}).(\d+)/,
                                '$1.$2,$3'
                              )} kg`,
                          ]
                            .filter(Boolean)
                            .join(' / ')}
                        </p>
                      </div>
                    </td>
                    <td width="152px">
                      <img src="https://woodflow-attachments.s3.us-east-1.amazonaws.com/eudr/etap_02.png" />
                    </td>
                  </tr>
                  <tr style={{ height: '11px' }}>
                    <td
                      width="153px"
                      style={{
                        backgroundImage:
                          "url('https://woodflow-attachments.s3.us-east-1.amazonaws.com/eudr/top_etap_03.png')",
                        backgroundRepeat: 'no-repeat',
                      }}
                    ></td>
                    <td
                      width="630px"
                      style={{
                        backgroundImage:
                          "url('https://woodflow-attachments.s3.us-east-1.amazonaws.com/eudr/bg_linha.png')",
                        backgroundRepeat: 'repeat-x',
                      }}
                    ></td>
                    <td
                      width="152px"
                      style={{
                        backgroundImage:
                          "url('https://woodflow-attachments.s3.us-east-1.amazonaws.com/eudr/bottom_etap_02.png')",
                        backgroundRepeat: 'no-repeat',
                      }}
                    ></td>
                  </tr>
                  <tr style={{ height: '214px' }}>
                    <td width="153px">
                      <img src="https://woodflow-attachments.s3.us-east-1.amazonaws.com/eudr/etap_03.png" />
                    </td>
                    <td colSpan={2} width="83%" style={{ textAlign: 'left', paddingLeft: '5px' }}>
                      <h3>Shipment Information</h3>
                      <div className="step-details">
                        <p>
                          <strong>Order:</strong> {supplyChain.pedido}
                          <br></br>
                          <strong>BL:</strong> {supplyChain.bl || t('labels.notInformed')}
                          <br></br>
                          <strong>Invoice:</strong> {supplyChain.invoice || t('labels.notInformed')}
                        </p>
                      </div>
                    </td>
                  </tr>
                  <tr style={{ height: '11px' }}>
                    <td
                      width="153px"
                      style={{
                        backgroundImage:
                          "url('https://woodflow-attachments.s3.us-east-1.amazonaws.com/eudr/bottom_etap_03.png')",
                        backgroundRepeat: 'no-repeat',
                      }}
                    ></td>
                    <td
                      width="630px"
                      style={{
                        backgroundImage:
                          "url('https://woodflow-attachments.s3.us-east-1.amazonaws.com/eudr/bg_linha.png')",
                        backgroundRepeat: 'repeat-x',
                      }}
                    ></td>
                    <td
                      width="152px"
                      style={{
                        backgroundImage:
                          "url('https://woodflow-attachments.s3.us-east-1.amazonaws.com/eudr/top_etap_04.png')",
                        backgroundRepeat: 'no-repeat',
                      }}
                    ></td>
                  </tr>
                  <tr style={{ height: '194px' }}>
                    <td colSpan={2} width="83%" style={{ textAlign: 'right', paddingRight: '5px' }}>
                      <h3>Import</h3>
                      <div className="step-details">
                        <p>
                          <strong>Importer:</strong> {supplyChain.importador?.name || t('labels.notInformed')}
                          <br></br>
                          <strong>Phone:</strong> {supplyChain.importador?.phone || t('labels.notInformed')}
                          <br></br>
                          <strong>E-mail:</strong> {supplyChain.importador?.email || t('labels.notInformed')}
                          <br></br>
                          <strong>Address:</strong> {supplyChain.importador?.address || t('labels.notInformed')}
                          <br></br>
                          <strong>Identification:</strong>{' '}
                          {supplyChain.importador?.tax_identifier_number || t('labels.notInformed')}
                        </p>
                      </div>
                    </td>
                    <td width="152px">
                      <img
                        src="https://woodflow-attachments.s3.us-east-1.amazonaws.com/eudr/etap_04.png"
                        alt="Import"
                      />
                    </td>
                  </tr>
                </table>
              </div>
            </td>
          </tr>
        </table>
      </div>
    </div>
  );
};

interface IColumnsProps {
  field?: string;
  headerName?: string;
  fixed?: boolean | 'left' | 'right' | undefined;
  sortable?: boolean;
  color?: string;
  cursor?: string;
  align?: 'left' | 'right' | 'center' | undefined;
  textDecoration?: string;
  width?: number;
  flexGrow?: number;
  name?: string;
  resizable: boolean;
}

interface IPropsCard {
  supplyChain: any;
}

interface FileItem {
  file: File;
  preview?: string;
  vencimento?: Date;
  path?: string;
  idDoc?: string;
  tamanho_arquivo?: string;
}

export const SupplyChainImporter = ({ supplyChain }: IPropsCard): React.ReactElement => {
  const { user } = useAuth();
  const history = useHistory();
  const [supplyChainExpanded, setSupplyChainExpanded] = useState(true);
  const [productExpanded, setProductExpanded] = useState(true);
  const [exporterExpanded, setExporterExpanded] = useState(true);
  const [gestaoNegocioExpanded, setGestaoNegocioExpanded] = useState(true);
  const [meioAmbienteExpanded, setMeioAmbienteExpanded] = useState(true);
  const [gestaoPessoasExpanded, setGestaoPessoasExpanded] = useState(true);
  const [climaExpanded, setClimaExpanded] = useState(true);
  const [files, setFiles] = useState<FileItem[]>([] as FileItem[]);
  const { setShowLoader, showLoader } = useLoader();
  const [isMobile] = React.useState<boolean>(window.innerWidth <= 768);
  const { t, i18n } = useTranslation();
  const [tipo1, setTipo1] = React.useState({} as any);
  const [tipo2, setTipo2] = React.useState({} as any);
  const [tipo3, setTipo3] = React.useState({} as any);
  const [tipo4, setTipo4] = React.useState({} as any);
  const [grupos, setGrupos] = React.useState({} as any);
  const [documentos, setDocumentos] = React.useState({} as any);
  const [supplyChainProduct, setSupplyChainProduct] = useState<any>({} as any);
  //eslint-disable-next-line
  const [species, setSpecies] = React.useState([] as any[]);
  //eslint-disable-next-line
  const [materiasPrima, setMateriasPrima] = useState<IpropsMateriaPrima[]>([]);
  //eslint-disable-next-line
  const [materiaPrimaQuantities, setMateriaPrimaQuantities] = useState<{ [key: string]: string }>({});
  //eslint-disable-next-line
  const [materiaPrimaQuantitiesTons, setMateriaPrimaQuantitiesTons] = useState<{ [key: string]: string }>({});
  //eslint-disable-next-line
  const [materiasPrimaPorFloresta, setMateriasPrimaPorFloresta] = useState<{
    [florestaId: string]: IpropsMateriaPrima[];
  }>({});
  //eslint-disable-next-line
  const [doumentCar, setDoumentCar] = React.useState({} as any);
  //eslint-disable-next-line
  const [company, setCompany] = React.useState({} as any);

  //eslint-disable-next-line
  const [page, setPage] = React.useState(1);
  //eslint-disable-next-line
  const [limit, setLimit] = React.useState(100);
  //eslint-disable-next-line
  const [query, setQuery] = React.useState<string>('');

  const collums: IColumnsProps[] = [
    {
      field: 'documento',
      headerName: t('table.documento'),
      color: '#201E40',
      flexGrow: 10,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'ondeEmitir',
      headerName: t('table.detalhes'),
      color: '#201E40',
      flexGrow: 1,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'vencimento',
      headerName: t('table.vencimento'),
      color: '#201E40',
      flexGrow: 1.5,
      fixed: false,
      resizable: !!isMobile,
    },
    {
      field: 'actions',
      headerName: 'Actions',
      color: '#201E40',
      flexGrow: 1,
      fixed: false,
      resizable: !!isMobile,
    },
  ];

  async function loadDocSave(): Promise<void> {
    const newFiles = Array.from(supplyChain.documentos).map((file: any) => ({
      file: new File([file.documento_path], file.nome),
      preview: URL.createObjectURL(new File([file.documento_path], file.documento_path)),
      vencimento: new Date(file.vencimento),
      path: file.documento_path,
      tamanho_arquivo: file.tamanho_arquivo,
    }));
    setFiles([...newFiles]);
  }

  React.useEffect(() => {
    let controle = true;
    if (supplyChain.id !== undefined && supplyChain.id !== 0 && controle) {
      loadDocSave();
    }

    return () => {
      controle = false;
    };
  }, [setFiles, supplyChain]);

  const getDocumentos = React.useCallback(async (idExportador: number): Promise<any> => {
    try {
      const res: any = await DocumentosService.getDocumentoExportador(idExportador);

      if (res) {
        return res;
      }
    } catch (error) {
      //eslint-disable-next-line
      console.log(error);
    }
  }, []);

  const getGruposDocumento = React.useCallback(async (): Promise<any> => {
    try {
      const res: any = await DocumentosService.getGruposDocumento();

      if (res) {
        return res;
      }
    } catch (error) {
      //eslint-disable-next-line
      console.log(error);
    }
  }, []);

  const getCompanyByImportee = React.useCallback(async (): Promise<any> => {
    try {
      const res = await SupplyChainService.getCompanyByImporterSF(String(supplyChain.client_id));
      if (res.data != undefined) {
        const companyData: any = {
          razaoSocial: res.data.name,
          fantasia: res.data.name_sender,
          email: res.data.email,
          cnpj: res.data.tax_identifier_number,
          address: res.data.street,
          numero: res.data.address_number,
          telefone: res.data.phone,
          inscricaoEstadual: res.data.address_complement,
          cep: res.data.zip_code,
          city: res.data.city,
          state: res.data.state,
          rules: {
            customer: false,
            seller: true,
            trader: false,
          },
        };

        setCompany(companyData);
      }

      return {};
    } catch (error) {
      return {};
    }
  }, [setCompany, supplyChain]);

  React.useEffect(() => {
    let controle = true;

    if (supplyChain.id !== 0 && controle) {
      getCompanyByImportee();
    }

    return () => {
      controle = false;
    };
  }, [getCompanyByImportee, supplyChain]);

  const getSupplyChainProduct = React.useCallback(async (): Promise<any> => {
    try {
      const res = await SupplyChainService.getProductBySupplyChain(String(supplyChain.supply_chain_id));

      if (res.data != undefined) {
        const productData: any = {
          id: res?.data?.id,
          uuid: res?.data?.uuid,
          status: res?.data?.status,
          supply_chain_id: res?.data?.supply_chain_id,
          product_description: res?.data?.product_description,
          product_quantity: res?.data?.product_quantity,
          product_quantity_tons: res?.data?.product_quantity_tons, // NOVO: Quantidade em toneladas
        };

        setSupplyChainProduct(productData);
      }

      return {};
    } catch (error) {
      return {};
    }
  }, [setSupplyChainProduct, supplyChain]);

  React.useEffect(() => {
    let conrole = true;

    if (supplyChain.id !== undefined && supplyChain.id && conrole) {
      getSupplyChainProduct();
    }

    return () => {
      conrole = false;
    };
  }, [getSupplyChainProduct, supplyChain]);

  const getSupplyChainSpecies = React.useCallback(
    async (scp_id: number, name: string): Promise<any> => {
      try {
        const res = await SupplyChainService.getSpeciesBySupplyChainProduct(scp_id, name, page, limit);

        if (res?.data?.length > 0) {
          const rows: any = res.data.map((item: any, index: number) => ({
            fornecedor: item.fornecedor,
            floresta: item.floresta,
            supplyChainSpecie: item,
            className: index % 2 === 0 ? 'custom-row' : '',
            uuid: item.uuid || item.uuid,
            id: item.id || item.id,
          }));

          setSpecies(rows);
        }

        return {};
      } catch (error) {
        return {};
      }
    },
    [setSpecies, page, limit]
  );

  const loadMateriaPrimaQuantities = useCallback(async (): Promise<void> => {
    try {
      const quantities: { [key: string]: string } = {};
      const quantitiesTons: { [key: string]: string } = {}; // NOVO: Para armazenar quantidades em toneladas
      const materiasPorFloresta: { [florestaId: string]: IpropsMateriaPrima[] } = {};

      // Para cada specie, buscar as quantidades das matérias primas
      for (const specieItem of species) {
        const florestaId = specieItem.supplyChainSpecie?.floresta_id || specieItem.floresta?.id;

        if (specieItem.supplyChainSpecie?.id && florestaId) {
          // Buscar matérias primas da floresta se ainda não foi carregada
          if (!materiasPorFloresta[florestaId]) {
            const resMT = await FlorestaService.getMateriaPrimaSelect(florestaId.toString());
            materiasPorFloresta[florestaId] = resMT.data || [];
          }

          // Buscar quantidades das matérias primas para esta specie
          const res = await SupplyChainService.getSupplyChainSpecieMP(specieItem.supplyChainSpecie.id);

          if (res && Array.isArray(res)) {
            res.forEach((item: any) => {
              if (item.materia_prima_id) {
                const key = `${florestaId}_${item.materia_prima_id}`;

                // Armazenar quantidade em m³ se existir
                if (item.mp_qtde) {
                  quantities[key] = item.mp_qtde;
                  //quantities[item.materia_prima_id] = item.mp_qtde;
                }

                // NOVO: Armazenar quantidade em toneladas se existir
                if (item.mp_qtde_tons) {
                  quantitiesTons[key] = item.mp_qtde_tons;
                  //quantitiesTons[item.materia_prima_id] = item.mp_qtde_tons;
                }
              }
            });
          }
        }
      }

      setMateriaPrimaQuantities(quantities);
      setMateriasPrimaPorFloresta(materiasPorFloresta);
      // Armazenar as quantidades em toneladas em um estado separado
      setMateriaPrimaQuantitiesTons(quantitiesTons);
    } catch (error) {
      console.error('Erro ao carregar quantidades das matérias primas:', error);
    }
  }, [species]);

  React.useEffect(() => {
    let controle = true;
    if (supplyChainProduct !== undefined && controle) {
      getSupplyChainSpecies(supplyChainProduct.id, query);
    }
    return () => {
      controle = false;
    };
  }, [getSupplyChainSpecies, supplyChainProduct, query]);

  // Carregar quantidades das matérias primas quando as species mudarem
  React.useEffect(() => {
    if (species.length > 0) {
      loadMateriaPrimaQuantities();
    }
  }, [species, loadMateriaPrimaQuantities]);

  const [filesFlorestaMap, setFilesFlorestaMap] = useState<{ [florestaId: string]: any[] }>({});

  const getDocumentoFloresta = useCallback(async (idF: number): Promise<any[]> => {
    const florestaRes = await FlorestaService.documentoFlorestaById(String(idF) || '');

    interface NewFile {
      file: File;
      preview: string;
      vencimento: Date;
      path: string;
      idDoc: string;
    }

    if (florestaRes && florestaRes.documento && florestaRes.documento.documento_path) {
      if (florestaRes.documento.documento_path != undefined) {
        const newFile: NewFile = {
          file: new File([florestaRes.documento.documento_path], florestaRes.documento.nome),
          preview: URL.createObjectURL(
            new File([florestaRes.documento.documento_path], florestaRes.documento.documento_path)
          ),
          vencimento: new Date(florestaRes.documento.vencimento),
          path: florestaRes.documento.documento_path,
          idDoc: florestaRes.id?.toString() || '',
        };

        setFilesFlorestaMap((prev) => ({
          ...prev,
          [idF]: [newFile],
        }));
        return [newFile];
      }
    }

    setFilesFlorestaMap((prev) => ({
      ...prev,
      [idF]: [],
    }));
    return [];
  }, []);

  React.useEffect(() => {
    species.forEach((especie) => {
      if (especie.floresta?.id) {
        getDocumentoFloresta(especie.floresta.id);
      }
    });
  }, [species, getDocumentoFloresta]);

  const loadGrupos = React.useCallback(async (): Promise<any> => {
    const res = await getGruposDocumento();
    setGrupos(res.data);
    //eslint-disable-next-line

    if (supplyChain.client_id !== undefined && supplyChain.client_id !== '') {
      const resDocumentos = await getDocumentos(supplyChain.client_id);
      setDocumentos(resDocumentos.data);
    }

    if (supplyChain.importador_id !== undefined && supplyChain.importador_id !== '') {
      const resImportador = await SupplyChainService.getCompanyByImporterSF(String(supplyChain.importador_id));
      if (resImportador.data) {
        const importadorData = {
          name: resImportador.data.name,
          fantasia: resImportador.data.name_sender,
          tax_identifier_number: resImportador.data.tax_identifier_number,
          phone: resImportador.data.phone,
          email: resImportador.data.email,
          address: resImportador.data.address,
          // Add other fields as needed
        };
        supplyChain.importador = importadorData;
      }
    }
  }, [supplyChain.client_id, getGruposDocumento, getDocumentos, setDocumentos, setGrupos]);

  React.useEffect(() => {
    let controle = true;

    if (controle) {
      loadGrupos();
    }

    return () => {
      controle = false;
    };
  }, [loadGrupos]);

  const addDocumentoTipo = (tipos: any): any[] => {
    const retorno: any[] = [];

    tipos.forEach((tp: any) => {
      if (documentos) {
        documentos.forEach((doc: any) => {
          if (doc.documento.tipo_documento_id === tp.idTipo) {
            // Cria um novo objeto com as propriedades atualizadas
            const docExitente = {
              ...tp,
              recebido: 'true',
              enviarDocumento: 'false',
              filepath: doc.documento.documento_path,
              vencimento: format(utcToZonedTime(doc.documento.vencimento, 'America/Sao_Paulo'), t('format.date')),
              idDocumento: doc.id,
            };
            retorno.push(docExitente);
          }
        });
      }
    });

    return retorno;
  };

  async function loadList(): Promise<void> {
    try {
      if (grupos) {
        grupos.forEach((grupo: any) => {
          const rows: any = grupo.tipos_grupo.map((item: any, index: number) => ({
            enviarDocumento: 'true',
            idTipo: item.id,
            documento: i18n.language === 'pt-BR' ? item.titulo : item.titulo_en,
            descricao: i18n.language === 'pt-BR' ? item.descricao : item.descricao_en,
            site: item.site,
            orgao: item.orgao,
            local: item.local,
            vencimento: '',
            idDocumento: '',
            filepath: '',
            idExportador: user?.default_company?.id ?? '',
            className: index % 2 === 0 ? 'custom-row' : '',
          }));

          if (grupo.id === 1) {
            setTipo1(addDocumentoTipo(rows));
          } else if (grupo.id === 2) {
            setTipo2(addDocumentoTipo(rows));
          } else if (grupo.id === 3) {
            setTipo3(addDocumentoTipo(rows));
          } else if (grupo.id === 4) {
            setTipo4(addDocumentoTipo(rows));
          }
        });
      }
    } catch (error) {
      setShowLoader(false);
    }
  }
  React.useEffect(() => {
    loadList();
  }, [setTipo1, setTipo2, setTipo3, setTipo4, user, grupos, documentos]);

  const getDocumentNameLimited = (name: string): string => {
    if (name.length <= 45) {
      return name;
    }

    // Extract the file extension
    const lastDotIndex = name.lastIndexOf('.');
    const extension = lastDotIndex !== -1 ? name.substring(lastDotIndex) : '';

    // Calculate how much of the name we can keep
    const maxNameLength = 45 - extension.length;

    if (maxNameLength <= 0) {
      // If extension is too long, just truncate the whole name
      return name.substring(0, 45);
    }

    // Truncate name and append original extension
    return name.substring(0, maxNameLength) + extension;
  };

  const getDocumentName = (path: string): string => {
    const parts = path.split('/');
    if (parts.length > 0) {
      return getDocumentNameLimited(parts[parts.length - 1]);
    }
    return '';
  };

  const formatFileSize = (bytes: number) => {
    // Format file size
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];

    if (bytes < k) {
      return bytes + ' ' + sizes[0];
    } else if (bytes < k * k) {
      return parseFloat((bytes / k).toFixed(2)) + ' ' + sizes[1];
    } else if (bytes < k * k * k) {
      return parseFloat((bytes / (k * k)).toFixed(2)) + ' ' + sizes[2];
    } else {
      return parseFloat((bytes / (k * k * k)).toFixed(2)) + ' ' + sizes[3];
    }
  };

  const handleDownloadFile = async (id: string): Promise<void> => {
    try {
      setShowLoader(true);
      await SupplyChainService.downloadDocumento(id);
      toastMsg(ToastType.Success, t('uploadFiles.downloadSuccess'));
    } catch (error: any) {
      toastMsg(ToastType.Error, t('messages.erroDocEudr') + error.message);
    } finally {
      setShowLoader(false);
    }
  };

  const getMateriaPrimaQuantity = (mp: any, florestaId?: string) => {
    // Se temos o ID da floresta, usar a chave composta primeiro
    if (florestaId) {
      const compositeKey = `${florestaId}_${mp.id}`;
      const quantityWithForest = materiaPrimaQuantities[compositeKey];
      if (quantityWithForest) {
        return quantityWithForest;
      }
    }

    // Fallback para a chave simples (compatibilidade)
    return materiaPrimaQuantities[mp.id] || '';
  };

  const getMateriaPrimaQuantityTons = (mp: any, florestaId?: string) => {
    // Se temos o ID da floresta, usar a chave composta primeiro
    if (florestaId) {
      const compositeKey = `${florestaId}_${mp.id}`;
      const quantityWithForest = materiaPrimaQuantitiesTons[compositeKey];
      if (quantityWithForest) {
        return quantityWithForest.replace(/(\d+),(\d{3}),(\d+)/, '$1.$2,$3');
      }
    }

    // Fallback para a chave simples (compatibilidade)
    return materiaPrimaQuantitiesTons[mp.id] || '';
  };

  const getMateriasPrimaForFloresta = (florestaId: string): IpropsMateriaPrima[] => {
    return materiasPrimaPorFloresta[florestaId] || [];
  };

  return (
    <>
      <div className="flex justify-between items-center p-4 mb-4">
        <Text as="b" size="2rem" weight={600} color="black" className="headerTitle">
          {t('titles.supply')} #{supplyChain.pedido}
        </Text>
        <div className="flex gap-2">
          {files && files.length > 0 && files[0].file.name != 'undefined' && (
            <Button
              cy="btn-download-eudr"
              variant="secondary"
              size="sm"
              className="flex items-center gap-1 btn-no-wrap"
              onClick={() => handleDownloadFile(supplyChain.id)}
            >
              <span>{t('buttons.generateDocument')}</span>
              <Download size={16} style={{ color: '#fff' }} />
            </Button>
          )}
          <Button
            cy="btn-back"
            variant="primary"
            size="sm"
            className="flex items-center gap-1"
            onClick={() => {
              history.push(`/dashboard-importer`);
            }}
          >
            <ChevronLeft size={16} />
            <span>{t('labels.labelReturn')}</span>
          </Button>
        </div>
      </div>

      {/* Supply Chain Flow */}
      <SupplyChainFlow
        species={species}
        materiasPrima={materiasPrima}
        company={company}
        supplyChainProduct={supplyChainProduct}
        supplyChain={supplyChain}
        t={t}
      />
      <div className="bg-white rounded-lg shadow pl-4 pr-4 pt-3 pb-3">
        {/* Supply Chain Data Section */}
        <div className="border-b">
          <div
            className="flex justify-between items-center p-4 cursor-pointer"
            onClick={() => setSupplyChainExpanded(!supplyChainExpanded)}
          >
            <h2 className="text-xl font-bold accordion-title">{t('labels.labelDataSupply')}</h2>
            {supplyChainExpanded ? (
              <ChevronUp size={18} className="accordion-icon" />
            ) : (
              <ChevronDown size={18} className="accordion-icon" />
            )}
          </div>

          {supplyChainExpanded && (
            <div className="p-4">
              <div className="mb-4">
                <p className="text-gray-500">{t('messages.responsabilidade')}</p>
                <br></br>
                <label className="block text-sm font-medium text-gray-700 mb-1">{t('labels.labelExporter')}</label>
                <p className="text-gray-500">{company.razaoSocial}</p>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">{t('labels.labelProduct')}</label>
                <p className="text-gray-500">
                  {supplyChainProduct.product_description
                    ? supplyChainProduct.product_description
                    : t('labels.noDescription')}
                </p>
              </div>

              <div className="materia-prima">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('labels.order')}</label>
                  <p className="text-gray-500">{supplyChain.pedido}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">BL</label>
                  <p className="text-gray-500">{supplyChain.bl || t('labels.notInformed')}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Invoice</label>
                  <p className="text-gray-500">{supplyChain.invoice || t('labels.notInformed')}</p>
                </div>
              </div>
              {files && files.length > 0 && files[0].file.name != 'undefined' && (
                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('uploadFiles.fiscalNote')}</label>
                  <p className="text-gray-500">
                    Documents related to shipment, such as: Export invoices (Nota Fiscal), BL, DUE (Export Legal
                    Declaration) and Commercial Invoice.
                  </p>
                  {files.map((file, index) => (
                    <FileAttachment
                      key={index}
                      filename={file.path ? getDocumentName(file.path || '') : getDocumentNameLimited(file.file.name)}
                      fileSize={file.tamanho_arquivo ? file.tamanho_arquivo : formatFileSize(file.file.size)}
                      filepath={file.path ? file.path : ''}
                    />
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Product Section */}
        {supplyChainProduct.product_description && (
          <div className="border-b">
            <div
              className="flex justify-between items-center p-4 cursor-pointer"
              onClick={() => setProductExpanded(!productExpanded)}
            >
              <h2 className="text-xl font-bold accordion-title">{t('labels.labelProduct')}</h2>
              {productExpanded ? (
                <ChevronUp size={18} className="accordion-icon" />
              ) : (
                <ChevronDown size={18} className="accordion-icon" />
              )}
            </div>

            {productExpanded && (
              <div className="p-4">
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('business.nameDescProForma')}
                  </label>
                  <p className="text-gray-500">
                    {supplyChainProduct.product_description
                      ? supplyChainProduct.product_description
                      : t('labels.noDescription')}
                  </p>
                </div>

                {/* Exibir quantidade em M³ se disponível */}
                {supplyChainProduct.product_quantity && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-1">{t('business.qtdM3')}</label>
                    <p className="text-gray-500">{supplyChainProduct.product_quantity} m³</p>
                  </div>
                )}

                {/* Exibir quantidade em Toneladas se disponível */}
                {supplyChainProduct.product_quantity_tons && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-1">{t('business.qtdTons')}</label>
                    <p className="text-gray-500">{supplyChainProduct.product_quantity_tons} kg</p>
                  </div>
                )}

                {species.map((especie, index) => {
                  const florestaId = especie.supplyChainSpecie?.floresta_id || especie.floresta?.id;
                  const materiasPrimaFloresta = getMateriasPrimaForFloresta(florestaId?.toString() || '');
                  getDocumentoFloresta(especie.floresta?.id);

                  return (
                    <div key={especie.id} className="border-b mb-6 pb-6">
                      <div>
                        <h2 className="text-xl font-bold mb-4 accordion-title">
                          {t('titles.specieUsed')} {index + 1}
                        </h2>
                        <div className="mb-6">
                          <label className="block text-sm font-medium text-gray-700 mb-1">{t('titles.supplier')}</label>
                          <p className="text-gray-500">{especie.fornecedor.name}</p>
                        </div>
                        <div className="mb-6">
                          <div className="materia-prima">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                {t('titles.floresta')}
                              </label>
                              <p className="text-gray-500">{especie.floresta.nome}</p>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                {t('labels.identificacao')}
                              </label>
                              <p className="text-gray-500">{especie.floresta.identificacao}</p>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                {t('labels.paisOrigemFloresta')}
                              </label>
                              <p className="text-gray-500">{especie.floresta.pais_origem}</p>
                            </div>
                          </div>
                        </div>
                        {especie.floresta.latitude && especie.floresta.longitude && (
                          <div className="mb-6">
                            <div className="materia-prima">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Latitude:</label>
                                <p className="text-gray-500">{especie.floresta.latitude}</p>
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Longitude:</label>
                                <p className="text-gray-500">{especie.floresta.longitude}</p>
                              </div>
                              <div></div>
                            </div>
                          </div>
                        )}
                        {materiasPrimaFloresta.map((mp) => {
                          const quantity = getMateriaPrimaQuantity(mp, florestaId?.toString());
                          const quantityTons = getMateriaPrimaQuantityTons(mp, florestaId?.toString());

                          // Só renderiza se houver quantidade (em m³ ou toneladas)
                          if (!quantity && !quantityTons) return null;

                          return (
                            <div key={mp.id} className="materia-prima mb-6">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  {t('titles.materiaPrima')}
                                </label>
                                <p className="text-gray-500">{mp.nome}</p>
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  {t('labels.scientificName')}
                                </label>
                                <p className="text-gray-500">{mp.nome_cientifico}</p>
                              </div>

                              {/* Exibir quantidade em M³ se disponível */}
                              {quantity && (
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    {t('business.qtdM3')}
                                  </label>
                                  <p className="text-gray-500">{quantity} m³</p>
                                </div>
                              )}

                              {/* Exibir quantidade em Toneladas se disponível */}
                              {quantityTons && (
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    {t('business.qtdTons')}
                                  </label>
                                  <p className="text-gray-500">{quantityTons.replace(/\.(\d+)$/, ',$1')} kg</p>
                                </div>
                              )}
                            </div>
                          );
                        })}
                        {filesFlorestaMap[especie.floresta?.id]?.length > 0 && (
                          <div className="mt-6">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              CAR (Rural Environmental Registry)
                            </label>
                            <p className="text-gray-500">
                              The CAR is a crucial environmental management tool in Brazil. It combines land-use
                              planning, environmental monitoring, and legal enforcement to promote more sustainable
                              rural development and combat deforestation—especially in sensitive biomes like the Amazon.
                            </p>
                            {filesFlorestaMap[especie.floresta?.id]?.map((file: any, idx) => (
                              <FileGeoJson
                                key={idx}
                                filename={file.path ? getDocumentName(file.path || '') : file.file.name}
                                fileSize={file.tamanho_arquivo ? file.tamanho_arquivo : formatFileSize(file.file.size)}
                                filepath={file.path ? file.path : ''}
                                documentId={file.idDoc}
                              />
                            ))}
                          </div>
                        )}
                        <NotasFiscaisSection especieId={especie.id} t={t} formatFileSize={formatFileSize} />
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        )}

        {/* Exporter Data Section */}
        <div className="border-b">
          <div
            className="flex justify-between items-center p-4 cursor-pointer"
            onClick={() => setExporterExpanded(!exporterExpanded)}
          >
            <h2 className="text-xl font-bold accordion-title">{t('titles.exporter')}</h2>
            {exporterExpanded ? (
              <ChevronUp size={18} className="accordion-icon" />
            ) : (
              <ChevronDown size={18} className="accordion-icon" />
            )}
          </div>

          {exporterExpanded && (
            <div className="p-4">
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">{t('labels.razaoSocial')}</label>
                <p className="text-gray-500">{company.razaoSocial}</p>
              </div>
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">{t('labels.fantasia')}</label>
                <p className="text-gray-500">{company.fantasia}</p>
              </div>

              <div className="materia-prima mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('placeholders.holderEmail')}
                  </label>
                  <p className="text-gray-500">{company.email}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('table.phone')}</label>
                  <p className="text-gray-500">{company.telefone}</p>
                </div>
                <div></div>
              </div>

              <div className="materia-prima mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('labels.cnpj')}</label>
                  <p className="text-gray-500">{company.cnpj}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('table.incricaoEstadual')}</label>
                  <p className="text-gray-500">{company.inscricaoEstadual}</p>
                </div>
                <div></div>
              </div>

              <div className="materia-prima mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('labels.cep')}</label>
                  <p className="text-gray-500">{company.cep}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('labels.city')}</label>
                  <p className="text-gray-500">{company.city}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('labels.state')}</label>
                  <p className="text-gray-500">{company.state}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Gestão e Negócio Section */}
        {tipo1.length > 0 && (
          <div className="border-b">
            <div
              className="flex justify-between items-center p-4 cursor-pointer"
              onClick={() => setGestaoNegocioExpanded(!gestaoNegocioExpanded)}
            >
              <h2 className="text-xl font-bold accordion-title">{t('titles.gestaoNegocio')}</h2>
              {gestaoNegocioExpanded ? (
                <ChevronUp size={18} className="accordion-icon" />
              ) : (
                <ChevronDown size={18} className="accordion-icon" />
              )}
            </div>
            {gestaoNegocioExpanded && (
              <div className="p-4">
                <p className="text-gray-500">
                  Legal requirements and documentation necessary for the commercialization of wood in Brazil.
                </p>
                <div className="mb-6">
                  <DownloadArquivo isMobile={isMobile} data={tipo1} tableColumns={collums} showLoader={showLoader} />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Meio Ambiente Section */}
        {tipo2.length > 0 && (
          <div className="border-b">
            <div
              className="flex justify-between items-center p-4 cursor-pointer"
              onClick={() => setMeioAmbienteExpanded(!meioAmbienteExpanded)}
            >
              <h2 className="text-xl font-bold accordion-title">{t('titles.meioAmbiente')}</h2>
              {meioAmbienteExpanded ? (
                <ChevronUp size={18} className="accordion-icon" />
              ) : (
                <ChevronDown size={18} className="accordion-icon" />
              )}
            </div>

            {meioAmbienteExpanded && (
              <div className="p-4">
                <p className="text-gray-500">
                  Environmental regularization is essential for the commercialization and processing of wood in Brazil.
                  Companies in the sector must comply with a series of legal requirements to ensure the traceability of
                  the raw material and compliance with environmental standards. Some documents are fundamental to prove
                  that the activity is in accordance with current legislation, avoiding penalties and restrictions.
                </p>
                <div className="mb-6">
                  <DownloadArquivo isMobile={isMobile} data={tipo2} tableColumns={collums} showLoader={showLoader} />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Gestão de Pessoas Section */}
        {tipo3.length > 0 && (
          <div className="border-b">
            <div
              className="flex justify-between items-center p-4 cursor-pointer"
              onClick={() => setGestaoPessoasExpanded(!gestaoPessoasExpanded)}
            >
              <h2 className="text-xl font-bold accordion-title">{t('titles.gestaoPessoas')}</h2>
              {gestaoPessoasExpanded ? (
                <ChevronUp size={18} className="accordion-icon" />
              ) : (
                <ChevronDown size={18} className="accordion-icon" />
              )}
            </div>

            {gestaoPessoasExpanded && (
              <div className="p-4">
                <p className="text-gray-500">
                  To ensure compliance with current legislation and safety in the workplace, it is essential to present
                  certain documents. These prove the labor regularity of the company and the adoption of protective
                  measures for employees.
                </p>
                <div className="mb-6">
                  <DownloadArquivo isMobile={isMobile} data={tipo3} tableColumns={collums} showLoader={showLoader} />
                </div>
              </div>
            )}
          </div>
        )}
        {/* Certificações Adicionais Section */}
        {tipo4.length > 0 && (
          <div>
            <div
              className="flex justify-between items-center p-4 cursor-pointer"
              onClick={() => setClimaExpanded(!climaExpanded)}
            >
              <h2 className="text-xl font-bold accordion-title">{t('titles.clima')}</h2>
              {climaExpanded ? (
                <ChevronUp size={18} className="accordion-icon" />
              ) : (
                <ChevronDown size={18} className="accordion-icon" />
              )}
            </div>

            {climaExpanded && (
              <div className="p-4">
                <p className="text-gray-500">
                  In addition to the main certifications, other recognitions reinforce the commitment to sustainability,
                  management and organizational well-being, such as: ISO 14001 - Environmental management, ensuring
                  sustainable practices, Carbon Neutral - Certification for companies that neutralize their carbon
                  emissions, LEED (Leadership in Energy and Environmental Design) - Certification for sustainable
                  buildings, GPTW (Great Place to Work) - Recognition of excellence in the work environment,
                  Sustainability and Innovation Awards - National and international distinctions that validate good
                  environmental and industrial practices.
                </p>
                <div className="mb-6">
                  <DownloadArquivo isMobile={isMobile} data={tipo4} tableColumns={collums} showLoader={showLoader} />
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
};
