import React, { useState } from 'react';
import { Table } from 'rsuite';
import { Col, Container, Dropdown, Image, Row, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, ArrowRight } from '@mui/icons-material';
import { FiEdit2 } from 'react-icons/fi';
import InactiveUser from '../../../../../statics/BsXCircle.svg';
import Modal from '../../../../../components/Modal';
import CustomButton from '../../../../../components/Button';

import '../../../styles.scss';
import { useLoader } from '../../../../../contexts/LoaderContext';
import toastMsg, { ToastType } from '../../../../../utils/toastMsg';
import CardSupplySpecie from '..';
import SupplyChainService from '../../../../../services/supplyChain.service';

export const TableSpecies = ({
  sortColumn,
  sortType,
  handleSortColumn,
  isMobile,
  data,
  tableColumns,
  page,
  lastPage,
  limit,
  handleChangeLimit,
  handleDownPage,
  handleUpPage,
  produto,
  atualizarSpecie,
}: any): React.ReactElement => {
  const { Column, HeaderCell, Cell } = Table;
  const { t } = useTranslation();
  const [show, setShow] = useState<any>({ show: false, especieId: null });
  const { setShowLoader } = useLoader();

  const [showModalDocument, setShowModalDocument] = useState<any>({ show: false, especie: null });

  const handleDelete = async (especieId: any): Promise<void> => {
    try {
      setShowLoader(true);
      await SupplyChainService.deleteSpecie(especieId);
      toastMsg(ToastType.Success, t('response.deleteSuccess'));
      setShow(false);
      atualizarSpecie();
      setShowLoader(false);
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  };

  const downloadDoc = (filePath: string) => {
    window.open(filePath, '_blank');
  };

  return (
    <Container className="containerBackgroundCustomers">
      <Modal
        show={show.show}
        handleClose={() => setShow({ ...show, show: false })}
        title={t('titles.removeFloresta')}
        size="lg"
        className="styleModalConfirm"
        colorIcon
      >
        <Container>
          <Row className="mt-4 p-2">
            <Col className="d-flex align-items-center ">
              <h6>
                {t('modal.removerFloresta')}
                <br />
                {t('modal.removeProductDescription')}
              </h6>
            </Col>
          </Row>
        </Container>

        <Row className="mt-4">
          <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
            <CustomButton
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={() => {
                setShow(false);
              }}
            >
              {t('buttons.cancel')}
            </CustomButton>
            <CustomButton cy="btn-save" type="button" variant="danger" onClick={() => handleDelete(show.especieId)}>
              {t('buttons.delete')}
            </CustomButton>
          </Col>
        </Row>
      </Modal>

      <Modal
        show={showModalDocument.show}
        handleClose={() => setShowModalDocument({ ...showModalDocument, show: false })}
        title={t('titles.editFloresta')}
        size="xl"
        className="styleModalConfirm"
        colorIcon
      >
        <CardSupplySpecie
          closeModal={setShowModalDocument}
          supplyChainId={produto.supply_chain_id}
          atualizarSpecie={atualizarSpecie}
          supplyChainSpecie={showModalDocument.especie}
          supplyChainProductSaveId={produto.id}
        />
      </Modal>

      {data.length > 0 ? (
        <>
          <Table
            bordered
            cellBordered
            sortColumn={sortColumn}
            sortType={sortType}
            onSortColumn={handleSortColumn}
            autoHeight={isMobile}
            height={300}
            affixHorizontalScrollbar={!isMobile}
            data={data}
            className="table"
            rowClassName={(rowData: any) => rowData?.className || ''}
          >
            {tableColumns.map((column: any) => {
              const { field, headerName, color, cursor, textDecoration, fixed, ...rest } = column;

              if (field === 'actions') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            <div
                              onClick={() => {
                                setShowModalDocument({ show: true, especie: rowData.supplyChainSpecie });
                              }}
                              role="presentation"
                              title={t('titles.editFloresta')}
                              style={{ cursor: 'pointer', color: '#494747' }}
                            >
                              <FiEdit2 size={20} color="green" />
                            </div>
                            <div
                              onClick={() => setShow({ show: true, especieId: rowData.id })}
                              role="presentation"
                              title={t('titles.removeFloresta')}
                              style={{ cursor: 'pointer' }}
                            >
                              <Image src={InactiveUser} height={20} />
                            </div>
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              if (field === 'car') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) =>
                        (rowData.car !== 'semdoc' && (
                          <Button type="button" variant="link" onClick={() => downloadDoc(rowData.car)}>
                            CAR - Cadastro Ambiental Rural
                          </Button>
                        )) || (
                          <Button type="button" variant="link" disabled={true}>
                            {t('labels.withoutDocument')}
                          </Button>
                        )
                      }
                    </Cell>
                  </Column>
                );
              }

              return (
                <>
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap />
                  </Column>
                </>
              );
            })}
          </Table>
          <div className="pagination">
            <div className="pageGroup">
              {!isMobile && <span>{t('pagination.clientsPerPage')}</span>}
              <Dropdown className="selectPerPage">
                <Dropdown.Toggle id="dropdown-limit">{limit}</Dropdown.Toggle>
                <Dropdown.Menu>
                  <Dropdown.Item onClick={() => handleChangeLimit(10)}>10</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(30)}>30</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(50)}>50</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(70)}>70</Dropdown.Item>
                  <Dropdown.Item onClick={() => handleChangeLimit(100)}>100</Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            </div>
            <div className="paginator">
              <span>
                {t('pagination.page')} {page} {t('pagination.of')} {lastPage}
              </span>
              <ArrowLeft className="navigator" onClick={handleDownPage} />
              <ArrowRight className="navigator" onClick={handleUpPage} />
            </div>
          </div>
        </>
      ) : (
        <></>
      )}
    </Container>
  );
};
