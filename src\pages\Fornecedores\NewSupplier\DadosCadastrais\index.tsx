import React, { useState } from 'react';
import { Row, Col, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import CustomInput from '../../../../components/Input/CustomInput';
import { useLoader } from '../../../../contexts/LoaderContext';
import CompaniesService from '../../../../services/companies.service';
import { useHistory } from 'react-router';
import toastMsg, { ToastType } from '../../../../utils/toastMsg';
import { useAuth } from '../../../../contexts/AuthContext';

interface IPropsCard {
  fornecedorUiid: string;
  companyInfo: any;
}

const CardInfoCompany = ({ fornecedorUiid, companyInfo }: IPropsCard): React.ReactElement => {
  const { user } = useAuth();
  const { t } = useTranslation();
  const { setShowLoader } = useLoader();
  const [cnpjError, setCnpjError] = useState(false);
  const history = useHistory();
  const [razaoSocial, setRazaoSocial] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [address, setAddress] = useState('');
  const [cnpj, setCnpj] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');

  const setValuesUpdate = React.useCallback(async () => {
    setRazaoSocial(companyInfo?.razaoSocial);
    setCompanyName(companyInfo?.companyName);
    setAddress(companyInfo?.address);
    setCnpj(companyInfo?.cnpj);
    setCity(companyInfo?.city);
    setState(companyInfo?.state);
  }, [companyInfo]);

  React.useEffect(() => {
    if (companyInfo) {
      setValuesUpdate();
    }
  }, [companyInfo, setValuesUpdate]);

  const handleSubmit = React.useCallback(async () => {
    try {
      setShowLoader(true);

      if (!razaoSocial || !companyName || !cnpj) {
        toastMsg(ToastType.Error, t('messages.errorRequiredFieldsFornecedor'));
        setShowLoader(false);
        return;
      }

      const newValues: any = {
        name: razaoSocial,
        tax_identifier_number: cnpj,
        address: address,
        name_sender: companyName,
        city: city,
        state: state,
        rules: {
          customer: false,
          seller: false,
          trader: false,
          supplier: true,
        },
      };

      if (fornecedorUiid) {
        await CompaniesService.changeCompany(newValues, fornecedorUiid);
        toastMsg(ToastType.Success, t('messages.updateSupplier'));

        history.push(`/new-fornecedor/${fornecedorUiid}?t=${Date.now()}`, { reload: true });
        window.location.reload();
      } else {
        toastMsg(ToastType.Success, t('messages.saveSupplier'));
        const companySave: any = await CompaniesService.newCompany(newValues);
        history.push(`/new-fornecedor/${companySave.data.uuid}?t=${Date.now()}`, { reload: true });
      }
    } catch (error: any) {
      if (error?.response?.data?.errors?.tax_identifier_number[0] === 'This customer is already registered') {
        setCnpjError(true);
        toastMsg(ToastType.Error, t('exceptions.cnpjAlready'));
      } else {
        toastMsg(ToastType.Error, error?.response?.data?.errors);
      }
    } finally {
      setShowLoader(false);
    }
  }, [setShowLoader, t, razaoSocial, cnpj, address, companyName, city, state, fornecedorUiid, history]);

  const getCompanieById = React.useCallback(async (uuid: string): Promise<any> => {
    try {
      const res = await CompaniesService.getCompanieByUiid(uuid);

      if (res) {
        return res;
      }
    } catch (error) {
      //eslint-disable-next-line
      console.log(error);
    }
  }, []);

  const onChangeCheckBox = React.useCallback(async () => {
    if (user?.default_company?.uuid) {
      const importador = await getCompanieById(user?.default_company.uuid);

      setRazaoSocial(importador.data.name);
      setCompanyName(importador.data.name_sender);
      setAddress(importador.data.street);
      setCnpj(importador.data.tax_identifier_number);
      setCity(importador.data.city);
      setState(importador.data.state);
    }
  }, [getCompanieById, razaoSocial, companyName, address, cnpj, city, state, user]);

  return (
    <>
      <label className="descricaoCard">{t('messages.supplierMessage')}</label>

      {companyInfo?.razaoSocial === undefined && (
        <>
          <br></br>
          <label className="text-checked">
            <>
              <input
                className="checkFilters"
                type="checkbox"
                id="isFornecedor"
                onChange={() => {
                  onChangeCheckBox();
                }}
              />
              &nbsp;{t('labels.exporteSupplier')}
            </>
          </label>
        </>
      )}
      <Row style={{ marginLeft: 'auto' }}>
        <Col md={12} className="mt-2">
          <CustomInput
            cy="test-razaoSocial"
            id="razaoSocial"
            name="razaoSocial"
            label={`${t('labels.razaoSocial')}*`}
            placeholder={t('labels.sendRazaoSocial')}
            value={razaoSocial}
            onChange={(e) => {
              setRazaoSocial(e.target.value);
            }}
            type="text"
            maxLength={255}
          />
        </Col>
        <Col md={12} className="mt-2">
          <CustomInput
            cy="test-companyName"
            id="companyName"
            name="companyName"
            label={`${t('labels.fantasia')}*`}
            placeholder={t('labels.sendFantasia')}
            value={companyName}
            onChange={(e) => {
              setCompanyName(e.target.value);
            }}
            type="text"
            maxLength={255}
          />
        </Col>

        <Col md={8} className="mt-2">
          <CustomInput
            cy="test-address"
            id="address"
            name="address"
            label={`${t('labels.fullAddress')}`}
            placeholder={t('labels.sendFullAddress')}
            value={address}
            onChange={(e) => {
              setAddress(e.target.value);
            }}
            type="text"
            maxLength={255}
          />
        </Col>
        <Col md={4} className="mt-2">
          <CustomInput
            cy="test-cnpj"
            id="cnpj"
            name="cnpj"
            label={`${t('labels.cnpj')}*`}
            placeholder={t('labels.sendCnpj')}
            value={cnpj}
            onChange={(e) => {
              setCnpj(e.target.value);
            }}
            type="text"
            maxLength={20}
            isInvalid={cnpjError}
            msg={t('exceptions.numberAlreadyExists')}
          />
        </Col>

        <Col md={6} className="mt-2">
          <CustomInput
            cy="test-city"
            id="city"
            name="city"
            label={`${t('labels.city')}`}
            placeholder={t('labels.sendCity')}
            value={city}
            onChange={(e) => {
              setCity(e.target.value);
            }}
            type="text"
            maxLength={20}
            msg={t('exceptions.numberAlreadyExists')}
          />
        </Col>
        <Col md={6} className="mt-2">
          <CustomInput
            cy="test-state"
            id="state"
            name="state"
            label={`${t('labels.state')}`}
            placeholder={t('labels.sendState')}
            value={state}
            onChange={(e) => {
              setState(e.target.value);
            }}
            type="text"
            maxLength={20}
            msg={t('exceptions.numberAlreadyExists')}
          />
        </Col>
        <Col md={12} className="mt-4" style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button variant="outline-green" onClick={() => handleSubmit()}>
            {t('buttons.fornecedorSave')}
          </Button>
        </Col>
      </Row>
    </>
  );
};

export default CardInfoCompany;
