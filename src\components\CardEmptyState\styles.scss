.cardEmptyState {
  display: flex;
  margin-right: 2rem;
  margin-top: 0.9rem;
  max-height: 34.6rem;
  min-height: 34.6rem;
  position: relative;
  h5 {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 120%;

    padding-left: 1rem;
    padding-right: 1rem;
    text-align: center;

    color: #201e40;
  }
  span {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 130%;
    cursor: pointer;
    text-align: center;
    text-decoration-line: underline;
    color: #18b680;
  }
  small {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 130%;
    cursor: pointer;
    text-align: center;
    color: #201e40;
  }
  .alignItems {
    text-align: center;
    display: flex;
    justify-content: center;
    flex-direction: column;
    min-height: 34.6rem;
  }
  .alignClick {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
