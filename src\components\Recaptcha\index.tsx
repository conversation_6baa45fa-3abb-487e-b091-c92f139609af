import React, { useCallback, useEffect } from 'react';
import { key } from '../../services/recaptcha.service';

const Recaptcha = (): React.ReactElement => {
  const handleGreCaptcha = useCallback((display: string) => {
    let style = document.getElementById('recaptcha_style');
    if (!style) {
      style = document.createElement('style');
      style.setAttribute('id', 'recaptcha_style');
      document.body.appendChild(style);
    }
    if (display === 'visible') {
      style.innerHTML = ' .grecaptcha-badge { display:block !important;}';
    } else {
      style.innerHTML = ' .grecaptcha-badge { display:none !important;}';
    }
  }, []);

  useEffect(() => {
    const script = document.createElement('script');
    script.setAttribute('id', 'recaptcha');
    script.src = `https://www.google.com/recaptcha/api.js?render=${key}`;
    document.body.appendChild(script);

    handleGreCaptcha('visible');

    return () => {
      handleGreCaptcha('hidden');
    };
  }, [handleGreCaptcha]);

  return <div className="g-recaptcha" data-sitekey={key} data-size="invisible" />;
};

export default Recaptcha;
