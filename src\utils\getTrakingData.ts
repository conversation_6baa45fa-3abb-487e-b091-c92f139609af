import { format, isValid } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';

interface IGetTrakingData {
  realDate: string;
  expectedDate: string;
  initialDate: string;
  format: string;
}

interface IReturnTrakingData {
  date: string | boolean;
  estimated: boolean;
}
export const getTrakingData = (date: IGetTrakingData): IReturnTrakingData => {
  if (date.realDate && isValid(new Date(date.realDate))) {
    const realDate = format(utcToZonedTime(date.realDate, 'America/Sao_Paulo'), date.format);
    return {
      date: realDate,
      estimated: false,
    };
  }

  if (date.expectedDate && isValid(new Date(date.expectedDate))) {
    const expectedDate = utcToZonedTime(date.expectedDate, 'America/Sao_Paulo');
    return {
      date: format(expectedDate, date.format),
      estimated: true,
    };
  }

  if (date.initialDate && isValid(new Date(date.initialDate))) {
    const initialDate = utcToZonedTime(date.initialDate, 'America/Sao_Paulo');
    return {
      date: format(initialDate, date.format),
      estimated: true,
    };
  }

  return {
    date: false,
    estimated: true,
  };
};
