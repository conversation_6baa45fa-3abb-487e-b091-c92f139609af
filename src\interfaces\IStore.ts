export interface Business {
  usuario_id_plataforma?: number;
  trader_id?: number;
  usuario_id_trader?: number;
  fornecedor_id?: number;
  usuario_id_fornecedor?: number;
  cliente_id?: number;
  usuario_id_cliente?: number;
  cod_ordem_compra?: string;
  cod_nota_fiscal?: string;
  etapa_id?: number;
  des_negocio?: string;
  cidade_id_origem?: number;
  porto_id_carga?: number;
  porto_id_destino?: number;
  cidade_id_destino?: number;
  incoterm_id?: number;
  cod_booking?: string;
  des_agente_carga?: string;
  des_navio?: string;
  qtd_container?: number;
  cod_tp_container?: string;
  val_volume_m3_total?: number;
  dt_producao_fim_prev_ini?: string;
  dt_producao_fim_prev?: string;
  dt_producao_fim_real?: string;
  dt_chegada_porto_ori_prev_ini?: string;
  dt_chegada_porto_ori_prev?: string;
  dt_chegada_porto_ori_real?: string;
  dt_saida_porto_ori_prev_ini?: string;
  dt_saida_porto_ori_prev?: string;
  dt_saida_porto_ori_real?: string;
  dt_chegada_porto_dest_prev_ini?: string;
  dt_chegada_porto_dest_prev?: string;
  dt_chegada_porto_dest_real?: string;
  dt_saida_porto_dest_prev_ini?: string;
  dt_saida_porto_dest_prev?: string;
  dt_saida_porto_dest_real?: string;
  dt_chegada_ponto_dest_prev_ini?: string;
  dt_chegada_ponto_dest_prev?: string;
  dt_chegada_ponto_dest_real?: string;
  dt_pagamento_prev_ini?: string;
  dt_pagamento_prev?: string;
  dt_pagamento_real?: string;
  num_dia_atraso?: string;
  cod_pagamento?: string;
  des_forma_pagamento?: string;
  des_status_pagamento?: string;
  val_negocio_antecipado?: string;
  val_negocio_total?: string;
  ind_situacao?: string;
  contact?: string;
  responsible?: string;
  customer?: string;
  partial_shipments?: string;
  transhipment?: string;
  last_date_shipment?: Date;
  insurance?: string;
  desc_pro_forma?: string;
  dynamic_title?: string;
  dynamic_description?: string;
}

export interface Product {
  des_negocio_prod?: string;
  qtd_palete?: string;
  qtd_peca_palete?: number;
  val_volume_m3_prod_palete?: string;
  qtd_peca?: number;
  val_volume_m3_prod?: string;
  val_preco_unit?: string;
  val_preco_prod?: string;
  cod_unid_medida_preco?: string;
  produto_id?: number;
  cod_produto?: string;
  nom_produto_pt?: string;
  nom_produto_en?: string;
  cod_produto_tp?: string;
  cod_especie?: string;
  cod_qualidade?: any;
  cod_cola?: any;
  list_certificado?: string;
  val_comprimento_mm?: number;
  val_largura_mm?: number;
  val_espessura_mm?: number;
  qtd_camada?: any;
  trader_id?: any;
  usuario_id_criacao?: number;
  usuario_id_alteracao?: number;
  ind_situacao?: string;
  label?: string;
}

export interface RootObject {
  negocio?: Business;
  produtos?: Product[];
}

export interface IUploadFile {
  message?: string;
  negocio_id?: number;
  documento_id?: number;
  etapa_id?: number;
  des_documento?: string;
  des_arq_documento?: string;
  send_notification: string | boolean;
  emails_notification: string[] | undefined;
}

export interface IUpdateFile {
  negocio_doc_id: number;
  negocio_id: number;
  documento_id: number;
  etapa_id: number;
  des_documento: string;
  des_arq_documento?: string;
}
