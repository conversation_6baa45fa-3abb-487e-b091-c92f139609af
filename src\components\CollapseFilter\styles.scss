.filtersButton {
  min-width: 100%;
  min-height: 3rem;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-size: 20px;
  line-height: 21px;
  color: #54666f;

  &:hover {
    background-color: #ffffff;
    color: #54666f;
    border: none;
  }

  &:focus {
    background-color: #ffffff;
    color: #54666f;
    border: none !important;
  }

  &:active {
    background-color: #ffffff;
    color: #54666f;
    border-color: none !important;
  }
  .btn-check:focus + .btn,
  .btn:focus {
    box-shadow: none;
    border: none;
  }

  svg {
    color: #54666f;
  }
}

.backgroundCollaps {
  background-color: #ffffff;
  padding-bottom: 21px;
  border-radius: 0.25rem;
  margin-top: -5px;
  overflow-y: scroll;
  max-height: 300px;
}

.text-checked {
  display: flex;
  gap: 0.438rem;
  justify-content: flex-start;
  margin-top: 0.4rem;
  padding: 0 1rem;
  font-family: 'Poppins';
  font-size: 14px;

  input[type='checkbox'] {
    margin-top: 0.2rem;
    position: relative;
    font-size: 10px;
    cursor: pointer;
    display: flex;
    align-items: flex-start;
    height: 16px;
    color: rgb(0, 0, 0);
    width: 1rem;
  }

  input[type='checkbox']::before {
    content: ' ';
    display: inline-block;
    vertical-align: middle;
    min-height: 16px;
    min-width: 16px;
    background-color: #ffffff;
    border-width: 1px;
    border-style: solid;
    border-color: #18b680;
    border-radius: 2px;
    box-shadow: none;
  }

  input[type='checkbox']:checked::before {
    content: ' ';
    display: inline-block;
    vertical-align: middle;
    min-height: 16px;
    min-width: 16px;
    background-color: #18b680;
    border-width: 1px;
    border-style: solid;
    border-color: #18b680;
    border-radius: 2px;
    box-shadow: none;
  }

  input[type='checkbox']:checked::after {
    content: ' ';
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MTIgNTEyIj48cGF0aCBmaWxsPSIjZmZmZmZmIiBkPSJNMTczLjg5OCA0MzkuNDA0bC0xNjYuNC0xNjYuNGMtOS45OTctOS45OTctOS45OTctMjYuMjA2IDAtMzYuMjA0bDM2LjIwMy0zNi4yMDRjOS45OTctOS45OTggMjYuMjA3LTkuOTk4IDM2LjIwNCAwTDE5MiAzMTIuNjkgNDMyLjA5NSA3Mi41OTZjOS45OTctOS45OTcgMjYuMjA3LTkuOTk3IDM2LjIwNCAwbDM2LjIwMyAzNi4yMDRjOS45OTcgOS45OTcgOS45OTcgMjYuMjA2IDAgMzYuMjA0bC0yOTQuNCAyOTQuNDAxYy05Ljk5OCA5Ljk5Ny0yNi4yMDcgOS45OTctMzYuMjA0LS4wMDF6Ii8+PC9zdmc+');
    background-repeat: no-repeat;
    background-size: 10px 10px;
    background-position: center center;
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 0px;
    left: 0px;
    top: 0px;
    text-align: center;
    background-color: transparent;
    font-size: 10px;
    height: 16px;
    width: 16px;
  }
}
