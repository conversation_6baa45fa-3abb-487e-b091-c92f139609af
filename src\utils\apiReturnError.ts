import React from 'react';
import { toast } from 'react-toastify';
import { AxiosError } from 'axios';

/**
 * @description
 * retorna os erros do catch vindo da api no toast.
 * Exemplo de uso:
 * apiReturnError(error);
 * @param {AxiosError | unknown} error
 * @return {React.ReactText} Toast.error("Campo X é obrigatório")
 */

const apiReturnError = (error: AxiosError | unknown): React.ReactText => {
  const { message, payload } = JSON.parse((error as AxiosError).request?.response);
  return toast.error(`${message} ${(payload || []).map((item: unknown) => item).join(', ')}`, { autoClose: false });
};

export default apiReturnError;
