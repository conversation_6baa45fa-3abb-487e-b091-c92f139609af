.margin-left {
  padding: 0 0 20px 20px;
}

.linha {
  border: 1px solid #000;
  margin-top: -10;
  margin-bottom: 0;
  width: 100%;
}

.btn svg {
  color: #fff7f7;
}

.btn svg {
  color: #3e3e3e;
}

// Estilos para o componente SupplyChainImporter
.bg-white {
  background-color: #ffffff;
  border-radius: 6px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.08);
}

.rounded-lg {
  border-radius: 6px;
}

.shadow {
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.08);
}

.pl-8 {
  padding-left: 2rem;
}

.pr-8 {
  padding-right: 2rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.p-6 {
  padding: 1.5rem;
}

.border-b {
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 0.5rem;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.cursor-pointer {
  cursor: pointer;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-6 {
  gap: 1.5rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.font-bold {
  font-weight: 700;
}

.text-sm {
  font-size: 14px;
  line-height: 1.5rem;
}

.font-medium {
  font-weight: 500;
}

.text-gray-700 {
  color: #4a5568;
}

.text-gray-500 {
  font-size: 14px;
  color: #718096;
  font-family: 'Poppins', sans-serif;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-6 {
  margin-bottom: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.block {
  display: block;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.btn-no-wrap {
  white-space: nowrap;
}

// Estilo para a seção de matéria-prima
.materia-prima {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.75rem;
  }

  > div {
    flex: 1;
    min-width: 200px;
  }
}

// Estilos para as tabelas
.table {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: 8px;
  overflow-y: hidden;

  & .rs-table-cell-header-icon-sort {
    color: #bac5c5;
  }

  & .rs-table-scrollbar-vertical {
    opacity: unset;

    & .rs-table-scrollbar-handle {
      background-color: #201e40;
    }
  }

  & .rs-table-cell-content {
    vertical-align: text-bottom !important;
  }

  & .rs-table-scrollbar-horizontal {
    opacity: unset;
    border-radius: 4px;

    @media (max-width: 820px) {
      padding-left: 0rem !important;
      padding-right: 0rem !important;
      margin-left: 0rem !important;
      margin-right: 0rem !important;
    }

    & .rs-table-scrollbar-handle {
      background-color: #201e40;
    }
  }

  & .rs-table-cell {
    font-family: 'Poppins';
    border-right: 1px solid #dfdfdf !important;
  }

  & .rs-table-body-row-wrapper {
    border-top: none;
  }

  & .rs-table-row {
    color: #bac5c5;
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    padding-left: 0;
  }
}

.table > :not(caption) > * > * {
  padding: 0;
}

.custom-row .rs-table-cell {
  background: #f1f1f1;
}

// Estilos para os cabeçalhos das seções
.headerTitle {
  text-transform: uppercase !important;
  font-family: 'Poppins' !important;
  color: #8e8e93;
}

// Estilo para os títulos dos acordeões
.accordion-title {
  color: #020406 !important;
  font-family: 'Poppins' !important;
  font-weight: 700 !important;
}

// Estilo para os ícones de expansão/colapso
.accordion-icon {
  color: #020406 !important;
}

// Estilos para o Supply Chain Flow - Layout Visual
.supply-chain-flow {
  .flow-container-visual {
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    padding: 2rem 0;
  }

  .flow-step-visual {
    display: flex;
    align-items: flex-start;
    margin-bottom: 4rem;
    position: relative;

    &.step-1,
    &.step-3 {
      justify-content: flex-start;

      .step-content-visual {
        margin-left: 2rem;
        text-align: left;
      }
    }

    &.step-2,
    &.step-4 {
      justify-content: flex-end;

      .step-content-visual {
        margin-right: 2rem;
        text-align: right;
        order: -1;
      }
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .step-circle {
    position: relative;
    width: 120px;
    height: 120px;
    border: 4px solid #000;
    border-radius: 50%;
    background: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 2;
    flex-shrink: 0;

    .step-number {
      position: absolute;
      top: -15px;
      left: -15px;
      width: 30px;
      height: 30px;
      background: #000;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 16px;
      z-index: 3;
    }

    .step-icon {
      color: #000;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .step-content-visual {
    max-width: 300px;

    h3 {
      font-size: 1.5rem;
      font-weight: bold;
      margin-bottom: 1rem;
      color: #000;
    }

    .step-details {
      p {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        color: #333;
        line-height: 1.4;

        strong {
          color: #000;
        }
      }
    }
  }

  .connecting-arrow {
    position: absolute;
    z-index: 1;

    &::before {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
    }

    &.arrow-1 {
      top: 100px;
      right: -80px;
      width: 160px;
      height: 80px;
      background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 160 80"><path d="M10 10 Q80 10 150 70" stroke="%23ccc" stroke-width="3" fill="none"/><polygon points="145,65 155,70 145,75" fill="%23ccc"/></svg>')
        no-repeat;
      background-size: contain;
    }

    &.arrow-2 {
      top: 100px;
      left: -80px;
      width: 160px;
      height: 80px;
      background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 160 80"><path d="M150 10 Q80 10 10 70" stroke="%23ccc" stroke-width="3" fill="none"/><polygon points="15,65 5,70 15,75" fill="%23ccc"/></svg>')
        no-repeat;
      background-size: contain;
    }

    &.arrow-3 {
      top: 100px;
      right: -80px;
      width: 160px;
      height: 80px;
      background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 160 80"><path d="M10 10 Q80 10 150 70" stroke="%23ccc" stroke-width="3" fill="none"/><polygon points="145,65 155,70 145,75" fill="%23ccc"/></svg>')
        no-repeat;
      background-size: contain;
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .flow-container-visual {
      padding: 1rem;
    }

    .flow-step-visual {
      flex-direction: column;
      align-items: center;
      text-align: center;
      margin-bottom: 3rem;

      &.step-1,
      &.step-2,
      &.step-3,
      &.step-4 {
        justify-content: center;

        .step-content-visual {
          margin: 1.5rem 0 0 0;
          text-align: center;
          order: 0;
        }
      }
    }

    .step-circle {
      width: 100px;
      height: 100px;

      .step-number {
        width: 25px;
        height: 25px;
        font-size: 14px;
        top: -12px;
        left: -12px;
      }
    }

    .step-content-visual {
      max-width: 100%;

      h3 {
        font-size: 1.25rem;
      }
    }

    .connecting-arrow {
      display: none;
    }
  }

  // Utility classes
  .text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .text-center {
    text-align: center;
  }

  .text-gray-800 {
    color: #1f2937;
  }

  .font-bold {
    font-weight: 700;
  }

  .mb-8 {
    margin-bottom: 2rem;
  }
}
