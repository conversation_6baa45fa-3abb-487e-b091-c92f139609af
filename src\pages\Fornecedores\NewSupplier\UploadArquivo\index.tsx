import React, { useState } from 'react';
import { Table } from 'rsuite';
import { Col, Container, Image, Row } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import EmptyState from '../../../../components/EmptyState';
import EmptyStateImage from '../../../../statics/emptyState.png';
import { FiEdit2 } from 'react-icons/fi';
import InactiveUser from '../../../../statics/delete.svg';
import Modal from '../../../../components/Modal';
import Button from '../../../../components/Button';

import '../styles.scss';
import { useLoader } from '../../../../contexts/LoaderContext';
import toastMsg, { ToastType } from '../../../../utils/toastMsg';
import { CheckCircle2, XCircle } from 'lucide-react';
import { ModalUpload } from '../../../../components/ModalUpload';
import DocumentosService from '../../../../services/documentos.service';
import { fi } from 'date-fns/locale';

export const UploadArquivo = ({
  sortColumn,
  sortType,
  handleSortColumn,
  isMobile,
  data,
  tableColumns,
  showLoader,
}: any): React.ReactElement => {
  const { Column, HeaderCell, Cell } = Table;
  const { t } = useTranslation();
  const history = useHistory();
  const [show, setShow] = useState<any>({ show: false, client: null });
  const [showModalDocument, setShowModalDocument] = useState<any>({
    show: false,
    idDoc: null,
    idTipo: null,
    name: null,
    idExportador: null,
    fileSize: null,
  });
  const [showMsg, setShowMsg] = useState<any>({
    show: false,
    msg: null,
    idDoc: null,
    titulo: null,
    site: null,
    orgao: null,
    local: null,
  });

  const { setShowLoader } = useLoader();

  const handleDeleteDocument = async (id: any): Promise<void> => {
    if (!id) {
      return;
    }

    try {
      setShowLoader(true);
      await DocumentosService.delete(id);
      setShowLoader(false);

      history.push('/industry', { reload: true });
      toastMsg(ToastType.Success, t('response.deleteSuccess'));
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  };

  return (
    <Container className="containerBackgroundCustomers">
      <Modal
        show={show.show}
        handleClose={() => setShow({ ...show, show: false })}
        title={t('modal.removeClientTitle')}
        size="lg"
        className="styleModalConfirm"
      >
        <Container>
          <Row className="mt-4 p-2">
            <Col className="d-flex align-items-center ">
              <h6>
                <br />
                {t('modal.removeDocSubTitle') + show.titulo + '?'}
                <br />
                {t('modal.removeProductDescription')}
              </h6>
            </Col>
          </Row>
        </Container>

        <Row className="mt-4">
          <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
            <Button
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={() => {
                setShow(false);
              }}
            >
              {t('buttons.cancel')}
            </Button>
            <Button cy="btn-save" type="button" variant="danger" onClick={() => handleDeleteDocument(show.idDoc)}>
              {t('buttons.delete')}
            </Button>
          </Col>
        </Row>
      </Modal>

      <Modal
        show={showModalDocument.show}
        handleClose={() => setShowModalDocument({ ...showModalDocument, show: false })}
        title="Upload de Arquivos"
        size="xl"
        className="styleModalConfirm"
        colorIcon
      >
        <ModalUpload
          idTipoDocumento={showModalDocument.idTipo}
          nameDocumento={showModalDocument.name}
          idExportador={showModalDocument.idExportador}
          fileSize={showModalDocument.fileSize}
          idDoc={showModalDocument.idDoc}
          onError={() => {}} // Função vazia para não quebrar
        />
      </Modal>

      <Modal
        show={showMsg.show}
        handleClose={() => setShowMsg({ ...showMsg, show: false })}
        title={t('modal.ondeEmitir') + ' ' + showMsg.titulo}
        size="lg"
        className="styleModalConfirm"
      >
        <Container>
          <Row className="mt-4 p-2">
            <Col className="d-flex align-items-center ">
              <h6>
                {showMsg.msg}
                <br />
                <br />
                <b>Órgão Responsável:</b> {showMsg.orgao}
                <br />
                <b>Local de Acesso:</b> {showMsg.local}
                <br />
                {showMsg?.site?.startsWith('https:') ? (
                  <Button
                    cy="link"
                    variant="link"
                    onClick={() => window.open(showMsg.site, '_blank')}
                    style={{ color: 'blue' }}
                  >
                    {showMsg.site}
                  </Button>
                ) : (
                  <></>
                )}
              </h6>
            </Col>
          </Row>
        </Container>

        <Row className="mt-4">
          <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
            <Button
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={() => {
                setShowMsg(false);
              }}
            >
              {t('buttons.fechar')}
            </Button>
          </Col>
        </Row>
      </Modal>

      {data.length > 0 ? (
        <>
          <Table
            bordered
            cellBordered
            sortColumn={sortColumn}
            sortType={sortType}
            onSortColumn={handleSortColumn}
            autoHeight={isMobile}
            height={400}
            affixHorizontalScrollbar={!isMobile}
            data={data}
            className="table"
            rowClassName={(rowData: any) => rowData?.className || ''}
          >
            {tableColumns.map((column: any) => {
              const { field, headerName, color, cursor, textDecoration, fixed, ...rest } = column;

              if (field === 'actions') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            {rowData.idDocumento ? (
                              <>
                                <div
                                  onClick={() => {
                                    setShowModalDocument({
                                      show: true,
                                      idDoc: rowData.idDocumento,
                                      idTipo: rowData.idTipo,
                                      name: rowData.documento,
                                      idExportador: rowData.idExportador,
                                      fileSize: rowData.tamanho_arquivo,
                                    });
                                  }}
                                  role="presentation"
                                  title={t('titles.alterDocument')}
                                  style={{ cursor: 'pointer', color: '#494747' }}
                                >
                                  <FiEdit2 size={20} color="green" />
                                </div>
                                <div
                                  onClick={() =>
                                    setShow({ show: true, idDoc: rowData.idDocumento, titulo: rowData.documento })
                                  }
                                  role="presentation"
                                  title={t('titles.removeDocument')}
                                  style={{ cursor: 'pointer' }}
                                >
                                  <Image src={InactiveUser} height={20} />
                                </div>
                              </>
                            ) : (
                              <></>
                            )}
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              if (field === 'recebido') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            <div>
                              {rowData[field] === 'true' ? (
                                <CheckCircle2 className="h-5 w-5 text-green-500" />
                              ) : (
                                <XCircle className="h-5 w-5 text-red-500" />
                              )}
                            </div>
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              if (field === 'enviarDocumento') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            <div>
                              {rowData[field] === 'true' ? (
                                <Button
                                  cy="btn-doc"
                                  type="button"
                                  variant="outline-secondary"
                                  style={{ height: 30, fontSize: 10, marginTop: -4 }}
                                  onClick={() =>
                                    setShowModalDocument({
                                      show: true,
                                      idTipo: rowData.idTipo,
                                      name: rowData.documento,
                                      idExportador: rowData.idExportador,
                                    })
                                  }
                                >
                                  {t('table.enviarDocumento')}
                                </Button>
                              ) : (
                                <label style={{ color: '#046b04' }}>{t('labels.documentoEnviado')}</label>
                              )}
                            </div>
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              if (field === 'ondeEmitir') {
                return (
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap>
                      {(rowData) => (
                        <>
                          <div className="d-flex justify-content-center gap-2">
                            <div>
                              <Button
                                cy="btn_oe"
                                variant="link"
                                style={{
                                  height: 30,
                                  fontSize: 12,
                                  marginTop: -4,
                                  color: '#201e40',
                                }}
                                onClick={() =>
                                  setShowMsg({
                                    show: true,
                                    msg: rowData.descricao,
                                    idDoc: rowData.idDocumento,
                                    titulo: rowData.documento,
                                    site: rowData.site,
                                    orgao: rowData.orgao,
                                    local: rowData.local,
                                  })
                                }
                              >
                                {t('table.ondeEmitir')}
                              </Button>
                            </div>
                          </div>
                        </>
                      )}
                    </Cell>
                  </Column>
                );
              }

              return (
                <>
                  <Column {...rest} key={field} fixed={fixed}>
                    <HeaderCell className="headerTitle">{headerName}</HeaderCell>
                    <Cell style={{ color, cursor, textDecoration }} dataKey={field} wordWrap />
                  </Column>
                </>
              );
            })}
          </Table>
        </>
      ) : (
        <>
          {!showLoader && (
            <EmptyState
              text={t('labels.labelDocumnetNotFound')}
              secondaryText={t('labels.labelDescDocumentNotFound')}
              img={EmptyStateImage}
            />
          )}
        </>
      )}
    </Container>
  );
};
