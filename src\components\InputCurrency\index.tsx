import React from 'react';
import MaskedInput from 'react-text-mask';
import createNumberMask from 'text-mask-addons/dist/createNumberMask';
import classNames from 'classnames';
import { HiInformationCircle } from 'react-icons/hi';
import IconTooltip from '../IconToolTip';
import './styles.scss';

interface IProp {
  value?: string;
  placeholder?: string;
  id?: string;
  label?: string;
  readOnly?: boolean;
  required?: boolean;
  disabled?: boolean;
  isInvalid?: boolean;
  desc?: string;
  msg?: string;
  className?: string;
  tabIndex?: number;
  ref?: React.Ref<any>;
  name?: string;
  cy?: string;
  onChange?: React.ChangeEventHandler<HTMLInputElement> | undefined;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onKeyPress?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onKeyUp?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  maskOptions?: {
    prefix?: string;
    suffix?: string;
    includeThousandsSeparator?: boolean;
    thousandsSeparatorSymbol?: string;
    allowDecimal?: boolean;
    decimalSymbol?: string;
    decimalLimit?: number;
    requireDecimal?: boolean;
    allowNegative?: boolean;
    allowLeadingZeroes?: boolean;
    integerLimit?: number;
  };
  defaultValue?: string | number;
  toolTip?: boolean;
  tooltipMessage?: string;
  colorToolTip?: string;
}

const defaultMaskOptions = {
  prefix: '',
  suffix: '',
  includeThousandsSeparator: false,
  thousandsSeparatorSymbol: '',
  allowDecimal: true,
  decimalSymbol: ',',
  decimalLimit: 2,
  integerLimit: 8,
  allowNegative: true,
  allowLeadingZeroes: false,
};

const InputCurrency = ({ maskOptions, isInvalid, cy, desc, ...inputProps }: IProp): React.ReactElement => {
  const currencyMask = createNumberMask({
    ...defaultMaskOptions,
    ...maskOptions,
  });

  return (
    <div className="input-currency w-100">
      {inputProps.label && (
        <label
          htmlFor={inputProps.id}
          className={classNames(
            (isInvalid && 'input-currency__label-error') || (inputProps.disabled && 'input-currency__label-disabled')
          )}
        >
          {inputProps.label}
          {inputProps.toolTip && inputProps.tooltipMessage && (
            <IconTooltip text={inputProps.tooltipMessage} icon={HiInformationCircle} color={inputProps.colorToolTip} />
          )}
        </label>
      )}
      <MaskedInput
        autoComplete="off"
        mask={currencyMask}
        {...inputProps}
        inputMode="numeric"
        aria-describedby={desc}
        data-cy={cy}
        className={`${isInvalid && 'input-currency__error'}`}
      />
      {isInvalid && inputProps.msg && <span className="input-currency__invalid-feedback">{inputProps.msg}</span>}
    </div>
  );
};

InputCurrency.defaultProps = {
  value: '',
  label: '',
  readOnly: false,
  required: false,
  disabled: false,
  isInvalid: false,
  msg: '',
  className: '',
  tabIndex: 0,
  ref: undefined,
  name: '',
  onChange: undefined,
  onKeyDown: undefined,
  onKeyUp: undefined,
  onKeyPress: undefined,
  onBlur: undefined,
  maskOptions: defaultMaskOptions,
  toolTip: false,
  tooltipMessage: '',
};

export default InputCurrency;
