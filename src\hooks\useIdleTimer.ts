import { useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';

const IDLE_TIMEOUT = 30 * 60 * 1000;

export const useIdleTimer = () => {
    const { user, signOut } = useAuth();

    const resetTimer = useCallback(() => {
        localStorage.setItem('lastActivity', new Date().getTime().toString());
    }, []);

    const checkIdle = useCallback(() => {
        if (user === undefined || user === null) {
            return;
        }

        const lastActivity = parseInt(localStorage.getItem('lastActivity') || '0');
        const currentTime = new Date().getTime();

        if (currentTime - lastActivity > IDLE_TIMEOUT) {
            signOut();
        }
    }, [signOut, user]);

    useEffect(() => {
        // Eventos para resetar o timer
        const events = [
            'mousemove',
            'mousedown',
            'keypress',
            'scroll',
            'touchstart',
            'click'
        ];

        // Inicializa o timer
        resetTimer();

        // Adiciona listeners para os eventos
        events.forEach(event => {
            document.addEventListener(event, resetTimer);
        });

        // Verifica inatividade a cada minuto
        const interval = setInterval(checkIdle, 60000);

        return () => {
            // Cleanup
            events.forEach(event => {
                document.removeEventListener(event, resetTimer);
            });
            clearInterval(interval);
        };
    }, [resetTimer, checkIdle]);
};