import { Download, Eye } from 'lucide-react';
import Button from '../Button';

interface FileAttachmentProps {
  filename: string;
  fileSize: string;
  filepath: string;
}

const downloadDoc = (url: string) => {
  window.open(url, '_blank');
};

export default function FileAttachment({ filename, fileSize, filepath }: FileAttachmentProps) {
  return (
    <div className="flex items-center justify-between p-2 border rounded-md">
      <div className="flex items-center gap-2">
        <div className="bg-gray-100 p-2 rounded">
          <svg width="16" height="20" viewBox="0 0 16 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 0H2C0.9 0 0 0.9 0 2V18C0 19.1 0.9 20 2 20H14C15.1 20 16 19.1 16 18V6L10 0Z" fill="#333333" />
            <path d="M10 0V6H16L10 0Z" fill="#666666" />
          </svg>
        </div>
        <div>
          <p className="text-sm font-medium">{filename}</p>
          <div className="flex items-center gap-2">
            <span className="text-xs text-gray-500">• Preview</span>
            <span className="text-xs text-gray-500">{fileSize}</span>
          </div>
        </div>
      </div>
      <div className="flex gap-1">
        <Button cy="test-newClient" variant="gost" className="h-8 w-8" onClick={() => downloadDoc(filepath)}>
          <Eye size={16} />
        </Button>
        <Button cy="download" variant="gost" className="h-8 w-8" onClick={() => downloadDoc(filepath)}>
          <Download size={16} />
        </Button>
      </div>
    </div>
  );
}
