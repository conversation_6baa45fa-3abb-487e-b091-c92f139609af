import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import HttpClient from '../services/httpClient';
import checkTokenIsValid from '../utils/checkTokenIsValid';
import toastMsg, { ToastType } from '../utils/toastMsg';

export const useApiConnection = () => {
  const [isConnected, setIsConnected] = useState<boolean>(true);
  const [isTokenValid, setIsTokenValid] = useState<boolean>(true);
  const { signOut } = useAuth();

  const checkConnection = async () => {
    const connected = await HttpClient.checkApiConnection();
    //eslint-disable-next-line no-console
    console.error('Erro de conexão com a API', connected);
    setIsConnected(connected);

    if (!connected) {
      toastMsg(ToastType.Error, 'Sem conexão com o servidor');
    }

    const tokenValid = checkTokenIsValid('@WoodFlowExporter:token');
    setIsTokenValid(tokenValid);

    if (!tokenValid) {
      toastMsg(ToastType.Error, 'Sessão expirada');
      signOut();
    }

    return connected && tokenValid;
  };

  useEffect(() => {
    // Verifica a conexão ao montar o componente
    checkConnection();

    // Verifica a cada 5 minutos
    const interval = setInterval(checkConnection, 1 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  return { isConnected, isTokenValid, checkConnection };
};