.new-customer {
  max-width: 96rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 8rem;
  padding-right: 8rem;
  padding-top: 1rem;
  
  @media (max-width: 768px) {
    padding-top: 1rem;
    padding-left: 0;
    padding-right: 0;
  }
  .form-control {
    line-height: 2;
  }
  .rti--input {
    width: 100%;
  }

  .divInfo {
    background-color: #201e40;
    height: auto;
    padding-top: 2rem;
    @media (max-width: 768px) {
      display: none;
    }
  }
  .divCrud {
    padding-left: 2.4rem;
  }
  .displayImg {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .btnReturn {
    min-height: 3rem;
    border-radius: 8px;
    margin-right: 0.7rem;
    border-color: #18b680;
    color: #18b680;
  }
  .alignLength {
    padding-right: 1rem;
    margin-top: 0.6rem;
  }
  .alignQuality {
    padding-right: 1.4rem;
    @media (max-width: 768px) {
      padding-right: 1rem;
      margin-top: 1rem;
    }
  }

  .alignWidth {
    padding-right: 1rem;
    margin-top: 0.6rem;
  }
  .alignThickness {
    margin-top: 0.6rem;
    padding-right: 1.4rem;
    @media (max-width: 768px) {
      padding-right: 1rem;
    }
  }

  .labelInfoCountry {
    color: #201e40;
    text-decoration: none;
  }
  .labelInfoImport {
    color: #ffffffff;
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 700;
    font-size: 2.125rem;
    line-height: 3rem;
    text-align: center;
  }

  .alignItemsInfos {
    flex-direction: column;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 3rem;
    margin-bottom: 4rem;
    @media (max-width: 1056px) {
      padding-left: 1rem;
    }
  }

  .numberFormat {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 0.5px;
    color: #95daaf;
  }

  .textInfos {
    font-size: 26px;
    line-height: 40px;

    letter-spacing: 0.5px;
    color: #ffffff;
    padding-top: 2.4rem;
  }

  .textNumbers {
    font-weight: 400;
    font-size: 20px;
    line-height: 28px;
    letter-spacing: 0.5px;

    color: #ffffff;
  }

  .infoQuote {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #201e40;
  }
  .css-319lph-ValueContainer {
    line-height: 2;
  }
  
}
