import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Collapse } from 'react-bootstrap';
import { KeyboardArrowDown, KeyboardArrowRight } from '@mui/icons-material';

import Text from '../Text';
import './styles.scss';
import { useAnalytics } from '../../contexts/AnalyticsContext';

export interface IOptionsInterface {
  label: string;
  value: string;
}

export interface ICollapseFilterProps {
  title: string;
  nameEvent: string;
  options: IOptionsInterface[];
  defaultKeys: any[];
  setSelectedFilters: (selectedItems: string[]) => void;
  setPage: (page: number) => void;
}

const CollapseFilter = ({
  title,
  nameEvent,
  options,
  defaultKeys,
  setSelectedFilters,
  setPage,
}: ICollapseFilterProps): React.ReactElement => {
  const [open, setOpen] = React.useState(false);
  const { trackEvent } = useAnalytics();

  const onChangeCheckBox = (value: any, event: any, param: any, titleEvent: any): void => {
    if (event.target.checked) {
      setSelectedFilters([...defaultKeys, value]);
      trackEvent(`${titleEvent} - Filter - ${param}`, {
        action: `Filtrado por ${param}`,
      });
    } else {
      const newColumnsKeys = defaultKeys?.filter((opt: string) => opt !== value).map((opt: string) => opt);

      setSelectedFilters(newColumnsKeys);
    }
  };

  const [newDefaultKey, setNewDefaultKey] = React.useState<any>([]);

  React.useEffect(() => {
    setNewDefaultKey(defaultKeys);
  }, [defaultKeys]);

  return (
    <>
      <Button
        className="filtersButton"
        onClick={() => {
          setOpen(!open);
        }}
        aria-expanded={open}
      >
        <Text as="b" size="0.8rem">
          {title}
        </Text>{' '}
        {open ? <KeyboardArrowDown /> : <KeyboardArrowRight />}
      </Button>
      <Collapse in={open} className="backgroundCollaps">
        <div id="example-collapse-text">
          {options?.map((opt: any) => (
            <Col className="d-flex" key={opt.value}>
              <label className="text-checked" htmlFor={opt.value}>
                <>
                  <input
                    className="checkFilters"
                    type="checkbox"
                    id={opt.value}
                    value={opt.value}
                    defaultChecked={newDefaultKey?.find((optChecked: string) => optChecked === opt.value)}
                    checked={newDefaultKey?.find((optChecked: string) => optChecked === opt.value)}
                    onChange={(e) => {
                      onChangeCheckBox(opt.value, e, title, nameEvent);
                      setPage(1);
                    }}
                  />
                  {opt.label}
                </>
              </label>
            </Col>
          ))}
        </div>
      </Collapse>
    </>
  );
};

export default CollapseFilter;
