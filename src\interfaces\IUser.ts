import { Links, Meta } from './IMeta';

export interface IDefaultCompany {
  id?: number;
  name?: string;
  email?: string;
  uuid?: string;
  features?: string[];
}
export interface IUser {
  id: number;
  profileId: number;
  displayName: string;
  email: string;
  avatarUrl?: string;
  phone?: string;
  language: string;
  address?: string;
  shipmentScheduleColumns?: IShipmentScheduleColumn[];
  default_company?: IDefaultCompany;
  first_login_at?: string;
  adm_usuario_rel?: any;
  status_profile?: string | null;
  accept_privacy_policies_at?: string | null;
  perfil_id?: number;
}

export interface IShipmentScheduleColumn {
  value: string;
}

export interface UserList {
  status_profile: string;
  profile_id: number;
  id: number;
  name: string;
  email: string;
  created_at?: string | null;
  companies?: any;
}

export interface ResponseUser {
  data: UserList[];
  links: Links;
  meta: Meta;
}

export interface IUserResponseData {
  id?: number;
  displayName?: string;
  created_at?: string | null;
  email: string;
  default_company?: IDefaultCompany;
  data: IUserResponseData[];
}

export interface IUserResponse {
  current_page: number;
  data: IUserResponseData[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  next_page_url?: any;
  path: string;
  per_page: number;
  prev_page_url?: any;
  to: number;
  total: number;
  meta: Meta;
}