import React from 'react';
import { Col } from 'react-bootstrap';
import './styles.scss';

export interface IOptionsInterface {
  label: string;
  value: string;
}

export interface IRadioButtonProps {
  title?: string;
  options: IOptionsInterface[];
  selectedValue: any;
  setSelected: (selectedValue: string | null) => void;
}

const RadioButton = ({ title, options, selectedValue, setSelected }: IRadioButtonProps): React.ReactElement => {
  const onChangeRadioButton = (value: string): void => {
    setSelected(value);
  };

  return (
    <div className="radioMain">
      <p>{title}</p>
      <div>
        {options?.map((opt: any) => (
          <Col className="d-flex" key={opt.value}>
            <label className="text-radio" htmlFor={opt.value}>
              <>
                <input
                  className="radioInput"
                  type="radio"
                  id={opt.value}
                  name="radioGroup"
                  value={opt.value}
                  checked={selectedValue === opt.value}
                  onChange={() => {
                    onChangeRadioButton(opt.value);
                  }}
                />
                {opt.label}
              </>
            </label>
          </Col>
        ))}
      </div>
    </div>
  );
};

export default RadioButton;
