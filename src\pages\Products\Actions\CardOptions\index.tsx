import React from 'react';
import { Col, Row } from 'react-bootstrap';
import { TagsInput } from 'react-tag-input-component';
import { useTranslation } from 'react-i18next';
import Card from '../../../../components/Card';
import Text from '../../../../components/Text';
import Select from '../../../../components/Select';
import '../styles.scss';
import { ISelectOption } from '../../../../interfaces';
import { handleBeforeAddValidate } from '../../../../utils/handleBeforeAddValidade';

const CardOptions = ({
  selectSpecies,
  setSpeciesProduct,
  speciesProduct,
  selectQualities,
  setQualitiesProduct,
  qualitiesProduct,
  selectCertificates,
  selectFinishes,
  finishesProduct,
  setFinishesProduct,
  selectGlues,
  setGluesProduct,
  gluesProduct,
  layers,
  setLayers,
  typeId,
  setCertificates,
  certificates,
}: any): React.ReactElement => {
  const { t } = useTranslation();
  const [showErrorLayers, setShowErrorLayers] = React.useState<boolean>(false);

  const handleTagsChange = (newTags: string[]): void => {
    if (newTags.length <= 20) {
      setLayers(newTags);
      setShowErrorLayers(false);
    } else {
      setLayers(newTags.slice(0, 20));
      setShowErrorLayers(false);
    }
  };

  return (
    <Card
      cy="card-test-product"
      className="style"
      renderBody={
        <Row>
          <Text as="span" size="1.2rem" weight={600} color="#201E40">
            {t('language') === 'USA' ? 'Product options' : 'Opções do produto'}
          </Text>
          <Col md={6} className="mt-3">
            <Select
              id={`selectSpecies${typeId}`}
              renderKey
              isDisabled={!typeId}
              loadOptions={selectSpecies}
              cacheOptions
              defaultOptions
              placeholder={t('placeholders.holderSpecieProduct')}
              title={`${t('labels.labelSpecieProduct')}*`}
              noOptionsMessage={() => t('labels.noOptions')}
              loadingMessage={() => 'Loading...'}
              onChange={(e: ISelectOption[]) => {
                setSpeciesProduct(e);
              }}
              cy="test-selectSpecies"
              value={speciesProduct.map((item: any) => item)}
              isMulti
            />
          </Col>
          <Col md={6} className="mt-3">
            <Select
              id={`selectQuality${typeId}`}
              renderKey
              isDisabled={!typeId}
              loadOptions={selectQualities}
              cacheOptions
              defaultOptions
              title={t('labels.labelQualityProduct')}
              placeholder={t('placeholders.holderQualityProduct')}
              noOptionsMessage={() => t('labels.noOptions')}
              loadingMessage={() => 'Loading...'}
              onChange={(e: ISelectOption[]) => {
                setQualitiesProduct(e);
              }}
              cy="test-selectQuality"
              value={qualitiesProduct.map((item: any) => item)}
              isMulti
            />
          </Col>
          <Col md={6} className="mt-3">
            <Select
              id={`selectCertificates${typeId}`}
              renderKey
              isDisabled={!typeId}
              loadOptions={selectCertificates}
              cacheOptions
              defaultOptions
              title={t('labels.labelCertificateProduct')}
              placeholder={t('placeholders.holderCertificate')}
              noOptionsMessage={() => t('labels.noOptions')}
              loadingMessage={() => 'Loading...'}
              onChange={(e: ISelectOption[]) => {
                setCertificates(e);
              }}
              cy="test-selectCertificates"
              value={certificates.map((item: any) => item)}
              isMulti
            />
          </Col>
          <Col md={6} className="mt-3">
            <Select
              id={`selectFinishe${typeId}`}
              renderKey
              isDisabled={!typeId}
              loadOptions={selectFinishes}
              cacheOptions
              defaultOptions
              title={t('labels.acabaments')}
              placeholder={t('placeholders.holderAcabaments')}
              noOptionsMessage={() => t('labels.noOptions')}
              loadingMessage={() => 'Loading...'}
              onChange={(e: ISelectOption[]) => {
                setFinishesProduct(e);
              }}
              cy="test-selectFinishe"
              value={finishesProduct.map((item: any) => item)}
              isMulti
            />
          </Col>
          <Col md={6} className="mt-3">
            <Select
              id={`selectFinishe${typeId}`}
              renderKey
              isDisabled={!typeId}
              loadOptions={selectGlues}
              cacheOptions
              defaultOptions
              placeholder={t('placeholders.holderGlueProduct')}
              title={t('labels.labelGlueProduct')}
              noOptionsMessage={() => t('labels.noOptions')}
              loadingMessage={() => 'Loading...'}
              onChange={(e: ISelectOption[]) => {
                setGluesProduct(e);
              }}
              cy="test-selectGlue"
              value={gluesProduct.map((item: any) => item)}
              isMulti
            />
          </Col>
          <Col md={6} className="mt-4">
            <span>{t('labels.labelLayersProduct')}</span>
            <TagsInput
              value={layers}
              onChange={handleTagsChange}
              name="layers"
              placeHolder={t('labels.layersOfProduct')}
              beforeAddValidate={(tag) => handleBeforeAddValidate(tag, setShowErrorLayers)}
            />
            {showErrorLayers && <div className="error-message">{t('labels.genericErrorTag')}.</div>}
          </Col>
        </Row>
      }
    />
  );
};

export default CardOptions;
