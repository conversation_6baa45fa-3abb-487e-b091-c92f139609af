.exporter-form {
  .form-title {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    text-align: left;
  }

  .section-title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    text-align: left;
  }

  .form-section {
    margin-bottom: 2rem;
    padding-left: 0;
  }

  .form-field {
    margin-bottom: 1rem;
    text-align: left;

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #333;
      text-align: left;
      padding-left: 0;
    }

    // Adicionar espaçamento entre colunas
    .gx-4 {
      --bs-gutter-x: 1.5rem;
    }

    // Remover padding padrão do Bootstrap nas colunas
    .col,
    [class*='col-'] {
      padding-left: 0;
    }
  }

  .full-width-input {
    width: 100%;
    text-align: left;

    // Override any existing styles to remove label inside the input component
    > label {
      display: none;
    }
  }

  hr {
    margin-top: 0.5rem;
    margin-bottom: 1.5rem;
    border-color: #dee2e6;
  }

  // Ajustar alinhamento do container
  .container,
  .container-fluid {
    padding-left: 0;
  }

  // Ajustar alinhamento das linhas
  .row {
    margin-left: 0;
    text-align: left;
  }
}

// Sobrescrever o padding padrão do Bootstrap para as colunas
.col,
[class*='col-'] {
  padding-left: 0;
}

// Ajustar o gutter padrão das linhas
.row {
  --bs-gutter-x: 0;
  margin-left: 0;
}
