import { useState, useCallback, useEffect } from 'react';

export default function useCompleteUploadFile(callback?: (file: File) => void): () => void {
  const [file, setFile] = useState<File | null>(null);

  useEffect(() => {
    if (file) {
      // eslint-disable-next-line no-unused-expressions
      callback && callback(file);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [file]);

  const input = document.createElement('input');
  input.setAttribute('type', 'file');
  input.accept = 'image/jpg, image/jpeg, image/png';

  const handleClick = useCallback((): void => {
    input.click();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  input.onchange = () => {
    if (input.files?.length) {
      setFile(input.files[0]);
    }
    input.value = '';
  };

  return handleClick;
}
