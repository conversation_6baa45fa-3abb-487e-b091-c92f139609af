.inputMask {
  font-weight: var(--is-400);
  &__error {
    border-color: var(--red-500) !important;
    color: var(--gray-800);
    font-weight: var(--is-400);
    background-image: url('../../statics/icons/input-error.svg');
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    padding-right: calc(1.5em + 0.75rem);
    &:focus {
      box-shadow: 0.5rem var(--teal-200);
      border-color: var(--red-500) !important;
      outline: var(--teal-200) 0.125rem solid;
    }
  }
  input {
    &:focus {
      box-shadow: 0.5rem var(--teal-200);
      border-color: var(--gray-200);
      outline: var(--teal-200) 0.125rem solid;
    }
  }
  &__label-error,
  .invalid-feedback {
    color: var(--gray-800);
    font-weight: var(--is-400);
  }
  .invalid-feedback {
    font-size: 0.87rem;
    color: var(--red-500);
  }

  .form-control:disabled,
  .form-control[readonly] {
    background-color: transparent;
    border: 0.06rem solid var(--gray-200);
    color: var(--gray-800);
    pointer-events: none;
    &::placeholder {
      color: var(--gray-200);
    }
  }
  &__label-disabled {
    color: var(--gray-200);
  }
}
