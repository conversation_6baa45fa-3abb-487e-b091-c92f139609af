import React from 'react';
import { t } from 'i18next';
import classnames from 'classnames';
import AsyncSelect from 'react-select/async';
import Text from '../Text';
import { ISelect } from './interfaces';
import './styles.scss';

const Select = ({
  title,
  children,
  placeholder,
  cacheOptions,
  defaultOptions,
  isDisabled,
  isMulti,
  isInvalid,
  msg,
  onChange,
  loadOptions,
  noOptionsMessage,
  loadingMessage,
  defaultValue,
  tabIndex,
  value,
  cy,
  onBlur,
  menuPosition = 'absolute',
  renderKey = false,
  isOptionDisabled,
  id,
  isClearable = true,
}: ISelect): React.ReactElement => (
  <div className={`select mt-2  ${isInvalid && 'select-error'} ${isMulti && 'select-multi'}`} data-cy={cy}>
    {children ? (
      <div className="d-flex gap-2 align-items-center justify-content-between">
        <Text as="span" className={`form-label d-block ${isInvalid && 'select-error__title'}`}>
          {title}
        </Text>
        {children}
      </div>
    ) : (
      <Text as="span" className={`ma form-label d-block mb-0  ${isInvalid && 'select-error__title'}`}>
        {title}
      </Text>
    )}

    <AsyncSelect
      key={renderKey ? id : null}
      cacheOptions={cacheOptions}
      defaultOptions={defaultOptions}
      placeholder={placeholder || (t('language') === 'USA' ? 'Select..' : 'Selecionar...')}
      isDisabled={isDisabled}
      isClearable={isMulti ? false : isClearable}
      isMulti={isMulti}
      loadOptions={loadOptions}
      loadingMessage={loadingMessage}
      noOptionsMessage={noOptionsMessage}
      onChange={onChange}
      defaultValue={defaultValue}
      className={classnames(`${isInvalid && 'select-error__input'}  ${isMulti && 'select-multi__input'}`)}
      tabIndex={tabIndex}
      value={value}
      onBlur={onBlur}
      menuPosition={menuPosition}
      isOptionDisabled={isOptionDisabled}
    />

    {isInvalid && (
      <Text as="span" className="select-error__invalid-feedback">
        {msg}
      </Text>
    )}
  </div>
);

export default Select;
