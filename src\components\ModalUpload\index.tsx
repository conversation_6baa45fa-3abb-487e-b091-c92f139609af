import React, { useState } from 'react';
import { Upload, FileIcon } from 'lucide-react';
import { Button, Card, Image } from 'react-bootstrap';
import { CardContent } from '@mui/material';
import InactiveUser from '../../statics/delete.svg';
import './styles.scss';
import toastMsg, { ToastType } from '../../utils/toastMsg';
import DocumentosService, { IGrupoDocumentoExportadorSave } from '../../services/documentos.service';
import DatePicker from '../../components/Datepicker';
import { formatDateToString } from '../../utils/formatDateToString';
import { useHistory } from 'react-router';
import { useTranslation } from 'react-i18next';
import { useLoader } from '../../contexts/LoaderContext';

interface FileItem {
  file: File;
  preview?: string;
  path?: string;
}

export const ModalUpload = ({
  idDoc,
  idTipoDocumento,
  nameDocumento,
  idExportador,
  fileSize,
  loadList,
  onClose,
  onError,
}: any): React.ReactElement => {
  const [files, setFiles] = useState<FileItem[]>([] as FileItem[]);
  const { t } = useTranslation();
  const [dataAtual, setDataAtual] = useState<Date | null>(new Date());
  const history = useHistory();
  const [idDocumento, setIdDocumento] = useState<string | null>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const { setShowLoader } = useLoader();
  let arquivoValido = 'F';

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) {
      return;
    }

    const file = event.target.files[0];

    // Verificar tipo MIME
    if (file.type !== 'application/pdf') {
      toastMsg(ToastType.Error, t('exceptions.pdfType'));
      return;
    }

    // Verificar tamanho mínimo
    if (file.size < 1024) {
      toastMsg(ToastType.Error, t('exceptions.fileTooSmall'));
      return;
    }

    // Criar novo arquivo com preview
    const newFile = {
      file,
      preview: URL.createObjectURL(file),
    };

    // Atualizar estado diretamente
    setFiles([newFile]);
  };

  // Reescrever completamente as funções de drag and drop
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();

    // Remover classe visual
    event.currentTarget.classList.remove('drag-over');

    if (!event.dataTransfer.files || event.dataTransfer.files.length === 0) {
      return;
    }

    const file = event.dataTransfer.files[0];

    // Verificar tipo MIME
    if (file.type !== 'application/pdf') {
      toastMsg(ToastType.Error, t('exceptions.pdfType'));
      return;
    }

    // Verificar tamanho mínimo
    if (file.size < 1024) {
      toastMsg(ToastType.Error, t('exceptions.fileTooSmall'));
      return;
    }

    // Criar novo arquivo com preview
    const newFile = {
      file,
      preview: URL.createObjectURL(file),
    };

    // Atualizar estado diretamente
    setFiles([newFile]);
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.add('drag-over');
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.remove('drag-over');
  };

  const removeFile = (index: number) => {
    const newFiles = [...files];
    URL.revokeObjectURL(newFiles[index].preview || '');
    newFiles.splice(index, 1);
    setFiles(newFiles);
  };

  const validateFiles = (tamanhoArquivo: string) => {
    if (tamanhoArquivo !== '') {
      arquivoValido = 'T';
    }
    return tamanhoArquivo;
  };

  const formatFileSize = (bytes: number) => {
    // Validate file type and set arquivoValido
    files.forEach((file) => {
      if (file.file.type === 'application/pdf') {
        if (bytes < 1024) {
          arquivoValido = 'F';
        } else {
          arquivoValido = 'T';
        }
      } else if (file.file.type === '') {
        arquivoValido = 'T';
      }
    });

    // Format file size
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];

    if (bytes < k) {
      return bytes + ' ' + sizes[0];
    } else if (bytes < k * k) {
      return parseFloat((bytes / k).toFixed(2)) + ' ' + sizes[1];
    } else if (bytes < k * k * k) {
      return parseFloat((bytes / (k * k)).toFixed(2)) + ' ' + sizes[2];
    } else {
      return parseFloat((bytes / (k * k * k)).toFixed(2)) + ' ' + sizes[3];
    }
  };

  const getTipoDocumento = React.useCallback(async (idTipo: string): Promise<any> => {
    try {
      const res: any = await DocumentosService.getTiposDocumento(idTipo);

      if (res) {
        return res;
      }
    } catch (error) {
      //eslint-disable-next-line
      console.log(error);
    }
  }, []);

  const getDocumentoById = React.useCallback(async (): Promise<any> => {
    try {
      const res: any = await DocumentosService.getDocumentoById(idDoc);

      if (res) {
        return res;
      }
    } catch (error) {
      //eslint-disable-next-line
      console.log(error);
    }
  }, []);

  React.useEffect(() => {
    async function loadDocSave(): Promise<void> {
      if (idDoc === undefined) return;
      const docAtual = await getDocumentoById();
      setIdDocumento(idDoc);
      setDataAtual(new Date(docAtual?.data?.documento.vencimento));
      const fileName = docAtual?.data?.documento.documento_path.split('/').pop() || 'documento.pdf';

      setFiles([
        {
          file: new File([docAtual?.data?.documento.documento_path], fileName),
          preview: URL.createObjectURL(
            new File([docAtual?.data?.documento.documento_path], docAtual?.data?.documento.documento_path)
          ),
          path: docAtual?.data?.documento.documento_path,
        },
      ]);
    }

    loadDocSave();
  }, [getDocumentoById, setIdDocumento, setDataAtual, setFiles]);

  const handleSubmit = React.useCallback(async () => {
    // Bloquear botão imediatamente para evitar duplo clique
    if (isSubmitting) {
      return;
    }

    // Bloquear botão imediatamente e continuar com o envio
    setIsSubmitting(true);

    setShowLoader(true);

    try {
      if (files.length === 0) {
        toastMsg(ToastType.Error, t('exceptions.noFile'));
        return;
      }

      if (files.length > 0 && arquivoValido === 'F') {
        toastMsg(ToastType.Error, t('exceptions.fileInvalid'));
        return;
      }

      const formData = new FormData();

      if (files[0].file.type === 'application/pdf') {
        formData.append('file', files[0].file);
      }

      formData.append('id', idDocumento?.toString() || '');

      const tipoDocumento = await getTipoDocumento(idTipoDocumento);

      const documento: IGrupoDocumentoExportadorSave = {
        nome: tipoDocumento.data.titulo,
        descricao: tipoDocumento.data.descricao,
        status: 'A',
        documento: {
          nome: tipoDocumento.data.titulo,
          descricao: tipoDocumento.data.descricao,
          descricao_en: tipoDocumento.data.descricao_en,
          extensao: '',
          tamanho_arquivo: formatFileSize(files[0].file.size),
          vencimento: formatDateToString(dataAtual),
          tipo_documento_id: idTipoDocumento.toString(),
          status: 'A',
        },
        exportador_id: idExportador,
      };

      formData.append('documento', JSON.stringify(documento));

      try {
        await DocumentosService.salvarDocumento(formData);
        if (onClose) {
          onClose();
        }
        if (loadList) {
          loadList();
        }
        toastMsg(ToastType.Success, t('messages.sendFileSuccess'));
      } catch (error: any) {
        //eslint-disable-next-line
        console.log('valida erro da api----->', error);

        // Em caso de erro, não chama loadList e não fecha o modal
        const errorData = error?.response?.data;
        const errorMsg = errorData?.message || error?.message || 'Erro desconhecido no upload';

        // Chama função de erro do componente pai
        if (onError) {
          // Verificar se é erro relacionado a PDF para mostrar botão de ajuda
          const isPdfError =
            errorMsg.toLowerCase().includes('pdf') ||
            errorMsg.toLowerCase().includes('versão') ||
            errorMsg.toLowerCase().includes('formato');

          onError({
            message: errorMsg,
            showHelpButton: errorData?.showHelpButton || isPdfError,
          });
        } else {
          // Só exibe toast se não houver função de erro (para compatibilidade)
          toastMsg(ToastType.Error, errorMsg);
        }
      }
    } finally {
      // Desbloquear botão após processamento
      setIsSubmitting(false);
      setShowLoader(false);
    }
  }, [getTipoDocumento, files, dataAtual, history, idExportador, idTipoDocumento, isSubmitting]);

  const downloadDoc = (url: string) => {
    window.open(url, '_blank');
  };

  const getDocumentNameLimited = (name: string): string => {
    if (name.length <= 45) {
      return name;
    }

    // Extract the file extension
    const lastDotIndex = name.lastIndexOf('.');
    const extension = lastDotIndex !== -1 ? name.substring(lastDotIndex) : '';

    // Calculate how much of the name we can keep
    const maxNameLength = 45 - extension.length;

    if (maxNameLength <= 0) {
      // If extension is too long, just truncate the whole name
      return name.substring(0, 45);
    }

    // Truncate name and append original extension
    return name.substring(0, maxNameLength) + extension;
  };

  const getDocumentName = (path: string): string => {
    const parts = path.split('/');
    if (parts.length > 0) {
      return getDocumentNameLimited(parts[parts.length - 1]);
    }
    return '';
  };

  return (
    <>
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <label className="text-xl">
                {t('modal.selecioneArquivo')} <b>{nameDocumento}</b>
                <br />
              </label>
            </div>

            <div
              className="border-2 border-dashed rounded-lg p-8 text-center"
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              <div className="flex flex-col items-center gap-2">
                <Upload className="h-10 w-10 text-muted-foreground" />
                <p>{t('labels.selecioneArquivo')}</p>
                <p className="text-sm text-muted-foreground">PDF File: {t('exceptions.maxSizeDocment')}</p>
                <Button
                  onClick={() => document.getElementById('file-upload')?.click()}
                  variant="outline-success"
                  className="mt-2"
                >
                  {t('buttons.selectFile')}
                </Button>
                <input
                  id="file-upload"
                  type="file"
                  className="hidden"
                  onChange={handleFileSelect}
                  accept=".pdf"
                  multiple
                />
              </div>
            </div>
          </div>

          {files.length > 0 && (
            <div className="space-y-4">
              <h3 className="font-bold text-lg">{t('labels.labelArquivoAdicionado')}</h3>
              <div className="border rounded-lg">
                <div className="grid grid-cols-12 gap-4 p-4 border-b bg-muted/50">
                  <div className="col-span-1">{t('table.type')}</div>
                  <div className="col-span-6">{t('table.name')}</div>
                  <div className="col-span-2">{t('table.size')}</div>
                  <div className="col-span-2" title={t('labels.noValidateDate')}>
                    {t('table.vencimento')}
                  </div>
                  <div className="col-span-1">{t('modal.modalTitleActions')}</div>
                </div>
                {files.map((file, index) => (
                  <div key={index} className="grid grid-cols-12 gap-4 p-4 items-center">
                    <div className="col-span-1">
                      <FileIcon className="h-6 w-6" />
                    </div>
                    <div className="col-span-6 flex items-center gap-2">
                      <Button variant="link" onClick={() => downloadDoc(file.path || '')}>
                        {file.path ? getDocumentName(file.path || '') : getDocumentNameLimited(file.file.name)}
                      </Button>
                    </div>
                    <div className="col-span-2">
                      {fileSize ? validateFiles(fileSize) : formatFileSize(file.file.size)}
                    </div>
                    <div className="col-span-2" title={t('labels.noValidateDate')}>
                      <DatePicker
                        onChange={(start) => {
                          setDataAtual(start);
                        }}
                        selected={dataAtual}
                        className="dt-picker"
                      />
                    </div>
                    <div className="col-span-1">
                      <Button variant="ghost" onClick={() => removeFile(index)}>
                        <Image src={InactiveUser} height={20} />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="d-flex justify-content-end">
            <Button variant="outline-secondary" onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? t('buttons.processing') || 'Processando...' : t('table.enviarDocumento')}
            </Button>
          </div>
        </CardContent>
      </Card>
    </>
  );
};
