.pagination {
  &__list {
    color: var(--gray-400);
    font-weight: var(--is-300);
    list-style: none;
    padding-left: 0;
    margin: 0;

    &--arrow {
      color: rgb(32, 30, 64);
      cursor: pointer;
      font-size: 0.75rem;
      font-weight: var(--is-700);
      text-transform: uppercase;

      svg {
        position: relative;
        top: -0.09rem;
      }

      &:first-child {
        padding-top: 0.1rem;
      }
    }

    &--active {
      border-radius: 0.12rem;
      text-align: center;
    }

    &__disabled {
      color: rgb(32, 30, 64);
      pointer-events: none;
      opacity: 0.5;
      font-size: 0.75rem;
      text-transform: uppercase;
    }
  }

  li+li {
    margin-left: 0.8rem;
  }
}