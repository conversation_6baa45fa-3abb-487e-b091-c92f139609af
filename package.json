{"name": "woodflow-eudr", "version": "1.1.0", "private": true, "dependencies": {"@brazilian-utils/brazilian-utils": "^1.0.0-rc.12", "@emotion/react": "^11.10.4", "@emotion/styled": "^11.10.4", "@mui/icons-material": "^5.10.6", "@mui/material": "^5.10.8", "@mui/styles": "^5.14.14", "@mui/x-data-grid": "^5.17.5", "@mui/x-data-grid-generator": "^5.17.5", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@types/file-saver": "^2.0.5", "@types/html2canvas": "^0.5.35", "@types/jest": "^26.0.15", "@types/marked": "^5.0.2", "@types/node": "^12.0.0", "@types/react": "^18.0.15", "@types/react-datepicker": "^4.4.2", "@types/react-dom": "^18.0.6", "@types/react-input-mask": "^3.0.1", "@types/react-router-dom": "^5.3.2", "@types/react-select": "2.0.17", "@types/react-text-mask": "^5.4.11", "autoprefixer": "^10.4.7", "axios": "^1.7.9", "bootstrap": "5.1.3", "chart.js": "^4.0.0", "classnames": "^2.3.1", "date-fns": "^2.27.0", "date-fns-tz": "^1.2.2", "file-saver": "^2.0.5", "formik": "^2.2.9", "html2canvas": "^1.4.1", "i18next": "^21.8.16", "i18next-browser-languagedetector": "^6.1.4", "js-file-download": "^0.4.12", "jwt-decode": "^3.1.2", "lodash.debounce": "^4.0.8", "lucide-react": "^0.468.0", "marked": "^16.2.0", "mixpanel-browser": "^2.45.0", "pdfjs-dist": "^4.10.38", "react": "^17.0.2", "react-bootstrap": "^2.0.4", "react-bootstrap-range-slider": "^3.0.8", "react-chartjs-2": "^5.0.0", "react-currency-input-field": "^3.6.11", "react-datepicker": "^4.8.0", "react-dom": "^17.0.2", "react-helmet": "^6.1.0", "react-i18next": "^11.18.3", "react-icons": "^4.3.1", "react-image-crop": "^10.0.9", "react-input-mask": "^2.0.4", "react-quill": "^2.0.0", "react-router-dom": "^5.3.0", "react-scripts": "^5.0.1", "react-select": "^5.4.0", "react-tag-input-component": "^2.0.2", "react-text-mask": "^5.5.0", "react-toastify": "^8.1.0", "react-tooltip": "^4.2.21", "react-use-intercom": "^3.0.2", "rsuite": "^5.19.0", "sass": "^1.50.0", "text-mask-addons": "^3.8.0", "typescript": "^4.5.4", "yet-another-react-lightbox": "^3.18.0", "yup": "^0.32.11"}, "devDependencies": {"@babel/preset-react": "^7.16.5", "@types/enzyme": "^3.10.12", "@types/file-saver": "^2.0.5", "@types/lodash.debounce": "^4.0.7", "@types/mixpanel-browser": "^2.36.0", "@types/pdfjs-dist": "^2.10.377", "@types/react-helmet": "^6.1.5", "@types/react-image-gallery": "^1.2.0", "@types/react-router": "^5.1.19", "@typescript-eslint/eslint-plugin": "^5.8.0", "@typescript-eslint/parser": "^5.8.0", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-typescript": "^2.7.1", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.27.1", "eslint-plugin-react-hooks": "^4.3.0", "eslint-plugin-storybook": "^0.5.5", "husky": "^7.0.0", "postcss": "^8.4.14", "postcss-scss": "^4.0.4", "prettier": "^2.5.1", "stylelint": "^14.2.0", "stylelint-config-recommended": "^6.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.5.0", "stylelint-scss": "^4.1.0"}, "resolutions": {"immer": "^9.0.6", "glob-parent": "^5.1.2", "nth-check": "^2.0.1", "ansi-regex": "^5.0.1", "json-schema": "^0.4.0", "browserslist": "^4.16.5", "autoprefixer": "10.4.5"}, "scripts": {"start": "react-scripts start", "start-https": "set HTTPS=true&&set SSL_CRT_FILE=C:/Windows/System32/cert.crt&&set SSL_KEY_FILE=C:/Windows/System32/cert.key&&react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/**/*.{ts,tsx}", "lint:fix": "eslint src/**/*.{ts,tsx} --fix", "lint:quiet": "eslint src/**/*.{ts,tsx} --quiet", "lint:style": "stylelint src/**/*.scss", "lint:stylelint:fix": "stylelint src/**/*.scss --fix", "prettier": "prettier --check src/**/*.{ts,tsx}", "prettier:fix": "prettier --write src/**/*.{ts,tsx}", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"npm": ">=6.0.0", "node": ">=14.0.0"}}