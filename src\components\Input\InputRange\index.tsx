import React from 'react';
import { Form, Col } from 'react-bootstrap';
import RangeSlider from 'react-bootstrap-range-slider';
import './styles.scss';

interface IProp {
  label?: string;
  id?: string;
  value: number | string;
  onChange?: React.ChangeEventHandler<HTMLInputElement> | undefined;
  min?: number;
  max?: number;
}

const InputRange = ({ id, label, value, onChange, min, max }: IProp): React.ReactElement => (
  <Form.Group controlId={id} className="inputRange mb-3">
    <Form.Label htmlFor={id} className="w-100">
      <RangeSlider value={value} onChange={onChange} min={min} max={max} variant="success" />
      <Col className="d-flex justify-content-between align-items-center">
        <Form.Label className="minAlign">{min}mm</Form.Label>
        <Form.Label className="maxAlign">{max}mm</Form.Label>
      </Col>
      {label}
    </Form.Label>
  </Form.Group>
);

export default InputRange;
