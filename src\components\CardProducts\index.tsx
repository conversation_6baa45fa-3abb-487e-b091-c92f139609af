import React from 'react';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom';
import { Image, Col } from 'react-bootstrap';
import { Panel } from 'rsuite';
import ICShare from '../../statics/share.svg';
import EmptyStateImage from '../../statics/emptyStateImage.png';
import Button from '../Button';
import Text from '../Text';
import { useAnalytics } from '../../contexts/AnalyticsContext';
import { renderDefaultObjectsTable, renderPropertiesValues } from '../../utils/renderDefaultObjects';
import ProductsModal from './ModalProducts';
import './styles.scss';

export const CardProducts = ({
  product,
  isProfile = false,
  statusId = undefined,
  home = false,
}: any): React.ReactElement => {
  const { t, i18n } = useTranslation();
  const { trackEvent } = useAnalytics();
  const history = useHistory();

  const renderNameProduct = React.useCallback(
    (value: any): string => {
      if (value) {
        let productLabel;
        if (i18n.language === 'pt-BR') {
          productLabel = value?.name_ptbr;
        } else {
          productLabel = value?.name;
        }
        return productLabel;
      }
      return '';
    },
    [i18n]
  );

  const renderDimensions = React.useCallback((value: any): string => {
    const metricsReturn = renderPropertiesValues(value, true);

    return metricsReturn;
  }, []);

  const [isOpen, setIsOpen] = React.useState<boolean>(false);

  return (
    <>
      <ProductsModal
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        product={product}
        isProfile={isProfile}
        renderDimensions={renderDimensions}
        statusId={statusId}
      />
      <Panel
        shaded
        bordered
        bodyFill
        style={{ display: 'inline-flex', alignItems: 'flex-start', width: 300, background: '#FFFFFF' }}
        className="cardProducts"
      >
        <Image
          src={product?.image || EmptyStateImage}
          height="218"
          width="288"
          style={{ paddingTop: '16px', paddingLeft: '16px', paddingRight: '6px' }}
        />
        <Panel>
          <h4 title={renderNameProduct(product)}>
            {renderNameProduct(product)?.length > 22
              ? renderNameProduct(product)?.substring(0, 22).concat('...')
              : renderNameProduct(product)}
          </h4>
          <span title={renderDefaultObjectsTable(product?.specie, t('language'))} className="specieLayout">
            {renderDefaultObjectsTable(product?.specie, t('language'))?.length > 30
              ? renderDefaultObjectsTable(product?.specie, t('language'))?.substring(0, 30).concat('...')
              : renderDefaultObjectsTable(product?.specie, t('language'))}
          </span>
          <Col className="mt-2">
            <span>
              {t('labels.labelThicknessProduct')} (mm):{' '}
              <small>{renderDimensions(product?.thickness) || t('labels.notInformated')}</small>
            </span>
            <br />
            <span>
              {t('labels.labelWidthProduct')} (mm):{' '}
              <small>{renderDimensions(product?.width) || t('labels.notInformated')}</small>
            </span>
            <br />
            <span>
              {t('labels.labelLengthProduct')} (mm):{' '}
              <small>{renderDimensions(product?.length) || t('labels.notInformated')}</small>
            </span>
            <Col md={12} className="d-flex gap-1 mt-2">
              <span>
                {t('labels.labelCertificateProduct')}:{' '}
                <small className="numbers">
                  {renderDefaultObjectsTable(product?.certificates, t('language')).length > 3
                    ? renderDefaultObjectsTable(product?.certificates, t('language'))
                        .split(',')
                        .slice(0, 3)
                        .join(',')
                        .concat('...') || t('labels.notInformated')
                    : renderDefaultObjectsTable(product?.certificates, t('language')) || t('labels.notInformated')}
                </small>
              </span>
            </Col>
          </Col>
          <div
            onClick={() => {
              history.push(`/suppliers-profile/${product?.company?.uuid}`, {
                id: product?.company?.uuid,
                name: product?.company?.name,
                idQuery: product?.company?.id,
              });
            }}
            role="presentation"
            className={home ? 'redirectHome' : 'redirect'}
            title={`redirect to ${product?.company?.profile?.name}`}
          >
            {!isProfile &&
              (product?.company?.profile?.status_id === 3 ? (
                <div className="d-flex gap-2">
                  {product?.company?.profile?.name}
                  <Image src={ICShare} />
                </div>
              ) : (
                ''
              ))}
          </div>
          <div className="btn-div mt-3">
            <Button
              className={home ? 'btnHome' : 'btnReturn w-10 d-flex align-items-start'}
              type="button"
              cy="test-create"
              variant={home ? 'primary' : 'btn-transparent'}
              style={{
                minWidth: home ? '100%' : '',
                borderRadius: home ? '8px' : '',
              }}
              onClick={() => {
                history.push('/new-quote', { product });
                trackEvent('Products - Request quote', {
                  action: `id do produto ${product.id}, nome ${renderNameProduct(product)} e fornecedor ${
                    product?.company?.name
                  }`,
                });
              }}
            >
              {home ? (
                <div className="btn-style">{t('labels.labelRequestQuote')}</div>
              ) : (
                <Text as="b">{t('labels.labelRequestQuote')}</Text>
              )}
            </Button>
          </div>

          <p onClick={() => setIsOpen(!isOpen)} className="viewMore">
            {t('language') === 'USA' ? 'see more >' : 'ver mais >'}
          </p>
        </Panel>
      </Panel>
    </>
  );
};
