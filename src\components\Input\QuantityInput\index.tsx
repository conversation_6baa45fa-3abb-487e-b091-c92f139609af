import React from 'react';
import MaskedInput from 'react-text-mask';
import { Col } from 'react-bootstrap';

interface IQuantityInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  colSize?: number;
  cy?: string;
}

const QuantityInput: React.FC<IQuantityInputProps> = ({
  label,
  value,
  onChange,
  placeholder,
  disabled = false,
  colSize = 4,
  cy = 'quantity-input',
}) => {
  const quantityMask = (rawValue: string) => {
    const digits = rawValue.replace(/[^\d]/g, '');
    const decimalPart = [/\d/, /\d/, /\d/]; // 3 casas decimais

    const integerPart = [];
    for (let i = 0; i < digits.length - 3; i++) {
      integerPart.push(/\d/);
      if ((digits.length - 3 - i) % 3 === 1 && i < digits.length - 4) {
        integerPart.push('.');
      }
    }

    return [...integerPart, ',', ...decimalPart];
  };

  return (
    <Col md={colSize} className="mt-2">
      <label htmlFor={cy}>{label}</label>
      <MaskedInput
        id={cy}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        mask={quantityMask}
        guide={false}
        placeholderChar={'\u2000'}
        placeholder={placeholder}
        className="form-control"
        disabled={disabled}
        data-cy={cy}
      />
    </Col>
  );
};

export default QuantityInput;
