import React from 'react';
import { useLocation, Link, useHistory } from 'react-router-dom';
import { Row, Col, Card, Form, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';

import Section from '../../../components/Section';
import Text from '../../../components/Text';

import './styles.scss';

const ConfirmAccount = (): React.ReactElement => {
  const location: any = useLocation();
  const history = useHistory();
  const { t } = useTranslation();

  return (
    <Section title="Confirm account" description="Confirm account">
      <Row className="confirm-account d-flex align-items-center justify-content-center mt-5">
        <Col className="box-confirm">
          <Card>
            <Card.Body>
              <Row className="justify-content-start">
                <Text as="h1" size="3rem">
                  {t('labels.labelAccessEmail')}
                </Text>
                <p className="mt-4">
                  {t('labels.labelEmailSendFor')}{' '}
                  <p color="#201e40">
                    <b>{location?.state?.email}</b>
                  </p>
                </p>

                <p>{t('labels.labelsNotGetEmail')}</p>
                <Row className="notGetEmail">
                  <Text as="span" className="w-auto ml-2">
                    <Link to="/forgotPassword">{t('labels.labelsReSendEmail')}</Link>
                  </Text>
                </Row>
                <Row className="notGetEmail">
                  <Text as="span" className="w-auto ml-2">
                    <a href="https://api.whatsapp.com/send?phone=5541996858551" target="_blank" rel="noreferrer">
                      {t('labels.labelContactWhatsApp')}
                    </a>
                  </Text>
                </Row>
                <br />
                <p className="mt-4 mb-4">{t('labels.labelOr')}</p>
                <br />
              </Row>
              <Row>
                <Form.Group controlId="button">
                  <Button className="controlBtn" type="submit" onClick={() => history.push('/login')}>
                    <Text className="labelConfirm" as="span">
                      {t('labels.labelRedirectToLogin')}
                    </Text>
                  </Button>
                </Form.Group>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Section>
  );
};

export default ConfirmAccount;
