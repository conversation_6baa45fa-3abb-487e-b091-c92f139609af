import React from 'react';
import { Accordion, AccordionProps } from 'react-bootstrap';
import './styles.scss';

interface IProp extends AccordionProps {
  title?: string;
  body: React.ReactElement;
  defaultActive?: string;
  active?: string;
  className?: string;
}

const DataAccordion = ({ title, body, defaultActive = '0', active = '0', className }: IProp): React.ReactElement => (
  <Accordion className="dataAccordion" defaultActiveKey={defaultActive}>
    <Accordion.Item eventKey={active}>
      <Accordion.Header>
        <h4>{title}</h4>
      </Accordion.Header>
      <Accordion.Body className={className}>{body}</Accordion.Body>
    </Accordion.Item>
  </Accordion>
);

export default DataAccordion;
