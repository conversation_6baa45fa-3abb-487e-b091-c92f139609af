import { IpropsMateriaPrima } from '../../services/floresta.service';

// Função de validação extraída da lógica da página
const validateMateriasPrima = (materiasPrima: IpropsMateriaPrima[]): boolean => {
  return materiasPrima.every((mp) => {
    const qtdM3 = mp.quantidade?.trim();
    const qtdTons = mp.quantidade_tons?.trim();
    return qtdM3 || qtdTons;
  });
};

describe('Specie Validation', () => {
  describe('validateMateriasPrima', () => {
    it('should return true when only m³ is filled', () => {
      const materiasPrima: IpropsMateriaPrima[] = [
        {
          id: 1,
          nome: 'Eucalipto',
          nome_cientifico: 'Eucalyptus',
          quantidade: '2,500',
          quantidade_tons: '',
          status: 'A'
        }
      ];

      expect(validateMateriasPrima(materiasPrima)).toBe(true);
    });

    it('should return true when only tons is filled', () => {
      const materiasPrima: IpropsMateriaPrima[] = [
        {
          id: 1,
          nome: 'Eucalipto',
          nome_cientifico: 'Eucalyptus',
          quantidade: '',
          quantidade_tons: '1,500',
          status: 'A'
        }
      ];

      expect(validateMateriasPrima(materiasPrima)).toBe(true);
    });

    it('should return true when both m³ and tons are filled', () => {
      const materiasPrima: IpropsMateriaPrima[] = [
        {
          id: 1,
          nome: 'Eucalipto',
          nome_cientifico: 'Eucalyptus',
          quantidade: '2,500',
          quantidade_tons: '1,500',
          status: 'A'
        }
      ];

      expect(validateMateriasPrima(materiasPrima)).toBe(true);
    });

    it('should return false when neither m³ nor tons is filled', () => {
      const materiasPrima: IpropsMateriaPrima[] = [
        {
          id: 1,
          nome: 'Eucalipto',
          nome_cientifico: 'Eucalyptus',
          quantidade: '',
          quantidade_tons: '',
          status: 'A'
        }
      ];

      expect(validateMateriasPrima(materiasPrima)).toBe(false);
    });

    it('should return false when one item has no quantities filled', () => {
      const materiasPrima: IpropsMateriaPrima[] = [
        {
          id: 1,
          nome: 'Eucalipto',
          nome_cientifico: 'Eucalyptus',
          quantidade: '2,500',
          quantidade_tons: '',
          status: 'A'
        },
        {
          id: 2,
          nome: 'Pinus',
          nome_cientifico: 'Pinus',
          quantidade: '',
          quantidade_tons: '',
          status: 'A'
        }
      ];

      expect(validateMateriasPrima(materiasPrima)).toBe(false);
    });

    it('should handle whitespace-only values correctly', () => {
      const materiasPrima: IpropsMateriaPrima[] = [
        {
          id: 1,
          nome: 'Eucalipto',
          nome_cientifico: 'Eucalyptus',
          quantidade: '   ',
          quantidade_tons: '1,500',
          status: 'A'
        }
      ];

      expect(validateMateriasPrima(materiasPrima)).toBe(true);
    });
  });
});
