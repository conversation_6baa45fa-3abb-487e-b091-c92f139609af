export const priceTotal = (values: any): any => {
  const precoUnitario = parseFloat((values.val_preco_unit ?? 0).toString().replace(',', '.'));
  if (values.cod_unid_medida_preco === 'VOLUME' && values.val_preco_unit && values.val_volume_m3_prod) {
    const volume = parseFloat((values.val_volume_m3_prod ?? 0).toString().replace(',', '.'));
    return precoUnitario * volume;
  }
  if (values.cod_unid_medida_preco === 'PECA' && values.val_preco_unit && values.qtd_peca) {
    const qtdPeca = parseFloat((values.qtd_peca ?? 0).toString().replace(',', '.'));
    return precoUnitario * qtdPeca;
  }
  return null;
};
