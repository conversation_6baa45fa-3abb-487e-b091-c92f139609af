.dt-picker {
  font-weight: var(--is-400);
  background-color: transparent;
  border: 0.06rem solid var(--gray-300);
  width: 100%;
  /*padding-left: 0.6rem;*/
  text-indent: 1em !important;
  margin-top: 10px;
  /*background-image: url(../../statics/calendar.png) !important;
  background-repeat: no-repeat !important;
  background-size: 0.935rem !important;
  background-position-x: 15px !important;
  background-position-y: 14px !important;*/
  line-height: 2;

  input:focus {
    box-shadow: 0.5rem var(--teal-200);
    border-color: var(--gray-200);
    outline: var(--teal-200) 0.125rem solid;
  }
  &__label-error,
  .invalid-feedback {
    color: var(--red-500);
    font-weight: var(--is-400);
  }
  .invalid-feedback {
    font-size: 0.47rem;
  }
}
.invalid-border {
  border: 0.06rem solid var(--red-500) !important;
}

.datePicker {
  .react-datepicker__input-container {
    .form-control:disabled,
    .form-control[readonly] {
      background-color: transparent;
      border: 0.06rem solid var(--gray-200);
      color: var(--gray-400);
      pointer-events: none;
      text-indent: 2em !important;
      background-image: url(../../statics/calendar.png) !important;
      background-repeat: no-repeat !important;
      background-size: 0.935rem !important;
      background-position-x: 15px !important;
      background-position-y: 14px !important;
      line-height: 2.2;

      &:hover {
        box-shadow: none;
      }
      font-size: 0.875rem !important;
      margin: 0 !important;
    }
  }

  .disabled-label {
    color: var(--gray-400);
  }

  ::placeholder {
    color: var(--gray-400);
  }

  :-ms-input-placeholder {
    color: var(--gray-400);
  }

  ::-ms-input-placeholder {
    color: var(--gray-400);
  }
}

.divAlignHolder {
  display: flex;
  align-items: center;
}
