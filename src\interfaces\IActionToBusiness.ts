import { User } from './IBusiness';

export interface NegocioAcaoTp {
  acao_tp_id: number;
  des_acao_tp_pt: string;
  des_acao_tp_en: string;
  cod_acao_tp: string;
  num_ordem: number;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}

export interface ActionRoot {
  acao_id: number;
  negocio_id: number;
  des_titulo: string;
  des_acao: string;
  acao_tp_id: number;
  cod_email: string;
  list_email_destinatario?: any;
  dt_planejado: string;
  ind_finalizado: string;
  dt_finalizado?: any;
  usuario_id_finalizado?: any;
  des_finalizado?: any;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
  negocio_acao_tp: NegocioAcaoTp;
  user: User;
}

export interface RequestAction {
  des_titulo: string;
  des_acao: string;
  ind_finalizado: string;
  des_finalizado: string;
  dt_planejado: Date | null;
  cod_email: string;
  list_email_destinatario?: any;
  negocio_id: number;
  acao_tp_id?: number;
}

export interface RequestActionUpdate {
  acao_id: number;
  negocio_id: number;
  des_titulo: string;
  des_acao: string;
  acao_tp_id: number;
  cod_email: string;
  list_email_destinatario?: any;
  dt_planejado: string;
  ind_finalizado: string;
  dt_finalizado?: any;
  usuario_id_finalizado?: any;
  des_finalizado?: any;
  usuario_id_criacao: number;
  usuario_id_alteracao: number;
  ind_situacao: string;
}
