import React from 'react';
import { NavLink } from 'react-router-dom';
import { Navbar } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import Text from '../Text';
import './styles.scss';

const Menu: React.FunctionComponent = () => {
  const { t } = useTranslation();

  return (
    <Navbar className="menu align-items-center flex-column w-100 p-0">
      <ul className="menu__routes--ul d-flex flex-wrap flex-row m-0">
        <li className="d-flex align-items-center">
          <NavLink
            to="/dashboard/home"
            exact
            className="menu__routes--link w-100"
            activeClassName="menu__routes--active"
          >
            <Text as="span" className="menu__routes--title">
              {t('sideBar.home')}
            </Text>
          </NavLink>
        </li>
        <li className="d-flex align-items-center">
          <NavLink to="/dashboard/home" className="menu__routes--link w-100" activeClassName="menu__routes--active">
            <Text as="span" className="menu__routes--title">
              {t('sideBar.register')}
            </Text>
          </NavLink>
        </li>
      </ul>
    </Navbar>
  );
};

export default Menu;
