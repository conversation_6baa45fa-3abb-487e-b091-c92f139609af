import React, { useState } from 'react';
import { Row } from 'react-bootstrap';

import '../styles.scss';
import { useParams } from 'react-router';
import SupplyChainService from '../../../services/supplyChain.service';
import { SupplyChainImporter } from '../../../components/SupplyChainImporter';

const ViewSupply: React.FC = () => {
  const params = useParams<{ id: string }>();
  const id = params.id;
  //eslint-disable-next-line
  const [isMobile, setIsMobile] = React.useState<boolean>(window.innerWidth <= 768);
  const [supplyChain, setSupplyChain] = useState<any[]>([]);
  const [supplyChainInfo, setSupplyChainInfo] = useState({
    id: 0,
    uuid: '',
    pedido: '',
    bl: '',
    invoice: '',
    status: '',
    importador_id: '',
    client_id: '',
    supply_chain_id: '',
    importador: {
      id: 0,
      nome: '',
      uuid: '',
    },
    documentos: [
      {
        id: 0,
        uuid: '',
        nome: '',
        documento_path: '',
        extensao: '',
        descricao: '',
        vencimento: '',
        status: '',
        tipo_documento_id: 0,
      },
    ],
  });

  const loadCrudDetail = React.useCallback(async (): Promise<void> => {
    if (id !== undefined) {
      try {
        const res = await SupplyChainService.getSupplyById(id);
        if (res) {
          setSupplyChain(res.data);
        }
      } catch (error) {
        console.error('Erro ao carregar detalhes da supply chain:', error);
      }
    }
  }, [id]);

  React.useEffect(() => {
    loadCrudDetail();
  }, [loadCrudDetail]);

  React.useEffect(() => {
    if (supplyChain && supplyChain.length > 0) {
      setSupplyChainInfo({
        id: supplyChain[0]?.supply_chain?.id || supplyChain[0]?.id,
        uuid: supplyChain[0]?.supply_chain?.uuid || supplyChain[0]?.uuid,
        pedido: supplyChain[0]?.supply_chain?.pedido || supplyChain[0]?.pedido,
        bl: supplyChain[0]?.supply_chain?.bl,
        invoice: supplyChain[0]?.supply_chain?.invoice,
        status: supplyChain[0]?.supply_chain?.status,
        importador_id: supplyChain[0]?.supply_chain?.importador_id || supplyChain[0]?.importador_id,
        client_id: supplyChain[0]?.supply_chain?.cliente_id || supplyChain[0]?.cliente_id,
        supply_chain_id: supplyChain[0]?.supply_chain_id,
        importador: {
          id: supplyChain[0]?.supply_chain?.importador?.id || supplyChain[0]?.importador?.id,
          nome: supplyChain[0]?.supply_chain?.importador?.name || supplyChain[0]?.importador?.name,
          uuid: supplyChain[0]?.supply_chain?.importador?.uuid || supplyChain[0]?.importador?.uuid,
        },
        documentos: supplyChain.map((doc?: any) => ({
          id: doc?.documento?.id,
          uuid: doc?.documento?.uuid,
          nome: doc?.documento?.nome,
          documento_path: doc?.documento?.documento_path,
          extensao: doc?.documento?.extensao,
          descricao: doc?.documento?.descricao,
          vencimento: doc?.documento?.vencimento,
          status: doc?.documento?.status,
          tipo_documento_id: doc?.documento?.tipo_documento_id,
        })),
      });
    }
  }, [supplyChain]);

  const handleWindowSizeChange = (): void => {
    if (window.innerWidth <= 768) {
      setIsMobile(true);
    } else {
      setIsMobile(false);
    }
  };

  React.useEffect(() => {
    window.addEventListener('resize', handleWindowSizeChange);

    return () => {
      window.removeEventListener('resize', handleWindowSizeChange);
    };
  }, []);

  return (
    <div className="dashboard-container">
      <Row>
        <main className="container mx-auto max-w-5xl">
          <SupplyChainImporter supplyChain={supplyChainInfo} />
        </main>
      </Row>
    </div>
  );
};

export default ViewSupply;
