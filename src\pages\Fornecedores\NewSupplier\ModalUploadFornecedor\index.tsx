import React, { useState } from 'react';
import { Upload, FileIcon } from 'lucide-react';
import { Card, Image, Container, Row, Col, Button } from 'react-bootstrap';
import { CardContent } from '@mui/material';
import InactiveUser from '../../../../statics/delete.svg';
import './styles.scss';
import { useLoader } from '../../../../contexts/LoaderContext';
import toastMsg, { ToastType } from '../../../../utils/toastMsg';
import DocumentosService, { IGrupoDocumentoFlorestaSave } from '../../../../services/documentos.service';
import DatePicker from '../../../../components/Datepicker';
import { formatDateToString } from '../../../../utils/formatDateToString';
import { useHistory } from 'react-router';
import { useTranslation } from 'react-i18next';
import FlorestaService, { IFloresta } from '../../../../services/floresta.service';
import Modal from '../../../../components/Modal';
import CustomButton from '../../../../components/Button';

interface FileItem {
  file: File;
  preview?: string;
}

export const ModalUpload = ({
  idDoc,
  idTipoDocumento,
  floresta,
  idFornecedor,
  uuidFornecedor,
}: any): React.ReactElement => {
  const [files, setFiles] = useState<FileItem[]>([] as FileItem[]);
  const { t } = useTranslation();
  const { setShowLoader } = useLoader();
  const [dataAtual, setDataAtual] = useState<Date | null>(new Date());
  const history = useHistory();
  const [idDocumento, setIdDocumento] = useState<string | null>('');
  const [show, setShow] = useState<any>({ show: false, client: null, idDoc: null });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const newFiles = Array.from(event.target.files).map((file) => ({
        file,
        preview: URL.createObjectURL(file),
      }));
      setFiles([...newFiles]);
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (event.dataTransfer.files) {
      const newFiles = Array.from(event.dataTransfer.files).map((file) => ({
        file,
        preview: URL.createObjectURL(file),
      }));
      setFiles([...newFiles]);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const removeFile = (index: number) => {
    const newFiles = [...files];
    URL.revokeObjectURL(newFiles[index].preview || '');
    newFiles.splice(index, 1);
    setFiles(newFiles);
  };

  const formatFileSize = (bytes: number) => {
    // Format file size
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];

    if (bytes < k) {
      return bytes + ' ' + sizes[0];
    } else if (bytes < k * k) {
      return parseFloat((bytes / k).toFixed(2)) + ' ' + sizes[1];
    } else if (bytes < k * k * k) {
      return parseFloat((bytes / (k * k)).toFixed(2)) + ' ' + sizes[2];
    } else {
      return parseFloat((bytes / (k * k * k)).toFixed(2)) + ' ' + sizes[3];
    }
  };

  const getTipoDocumento = React.useCallback(async (idTipo: string): Promise<any> => {
    try {
      const res: any = await DocumentosService.getTiposDocumento(idTipo);

      if (res) {
        return res;
      }
    } catch (error) {
      //eslint-disable-next-line
      console.log(error);
    }
  }, []);

  const getDocumentoById = React.useCallback(async (): Promise<any> => {
    try {
      const res: any = await DocumentosService.getDocumentoCar(idDoc);

      if (res) {
        return res;
      }
    } catch (error) {
      //eslint-disable-next-line
      console.log(error);
    }
  }, []);

  React.useEffect(() => {
    async function loadDocSave(): Promise<void> {
      if (idDoc === undefined) return;
      const docAtual = await getDocumentoById();
      setIdDocumento(idDoc);

      setDataAtual(new Date());

      setFiles([
        {
          file: new File([docAtual?.data?.documento_path], docAtual?.data?.nome),
          preview: URL.createObjectURL(new File([docAtual?.data?.documento_path], docAtual?.data?.documento_path)),
        },
      ]);
    }

    loadDocSave();
  }, [getDocumentoById, setIdDocumento, setDataAtual, setFiles, idDoc]);

  const handleSubmit = React.useCallback(async () => {
    try {
      setShowLoader(true);

      const newFloresta: IFloresta = {
        nome: floresta.nome,
        identificacao: floresta.identificacao,
        status: 'A',
        fornecedor_id: idFornecedor,
      };

      const florestaSave = await FlorestaService.saveFloresta(newFloresta);

      const formData = new FormData();
      formData.append('file', files[0].file);
      formData.append('id', idDocumento?.toString() || '');

      const tipoDocumento = await getTipoDocumento(idTipoDocumento);

      const documento: IGrupoDocumentoFlorestaSave = {
        nome: tipoDocumento.data.titulo,
        descricao: tipoDocumento.data.descricao,
        status: 'A',
        documento: {
          nome: tipoDocumento.data.titulo,
          descricao: tipoDocumento.data.descricao,
          descricao_en: tipoDocumento.data.descricao_en,
          extensao: '',
          tamanho_arquivo: formatFileSize(files[0].file.size),
          vencimento: formatDateToString(dataAtual),
          tipo_documento_id: idTipoDocumento,
          status: 'A',
        },
        floresta_id: String(florestaSave.id),
      };

      formData.append('documento', JSON.stringify(documento));

      await FlorestaService.salvarDocumento(formData);
      window.location.reload();
      //history.push('/new-fornecedor' + uuidFornecedor, { reload: true });
      toastMsg(ToastType.Success, t('messages.sendFileSuccess'));
    } catch (error: any) {
      toastMsg(ToastType.Error, error?.response?.data?.errors);
    } finally {
      setShowLoader(false);
    }
  }, [setShowLoader, getTipoDocumento, files, dataAtual, history, idTipoDocumento]);

  const handleDeleteDocument = async (id: any): Promise<void> => {
    if (!id) {
      return;
    }

    try {
      setShowLoader(true);
      await FlorestaService.deleteFloresta(id);
      setShowLoader(false);

      history.push('/new-fornecedor/' + uuidFornecedor, { reload: true });
      toastMsg(ToastType.Success, t('response.deleteSuccess'));
    } catch (error) {
      toastMsg(ToastType.Error, (error as Error).message);
    } finally {
      setShowLoader(false);
    }
  };

  const getDocumentNameLimited = (name: string): string => {
    if (name.length <= 45) {
      return name;
    }

    // Extract the file extension
    const lastDotIndex = name.lastIndexOf('.');
    const extension = lastDotIndex !== -1 ? name.substring(lastDotIndex) : '';

    // Calculate how much of the name we can keep
    const maxNameLength = 45 - extension.length;

    if (maxNameLength <= 0) {
      // If extension is too long, just truncate the whole name
      return name.substring(0, 45);
    }

    // Truncate name and append original extension
    return name.substring(0, maxNameLength) + extension;
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <Modal
        show={show.show}
        handleClose={() => setShow({ ...show, show: false })}
        title={t('modal.removeFlorestaTitle')}
        size="lg"
        className="styleModalConfirm"
      >
        <Container>
          <Row className="mt-4 p-2">
            <Col className="d-flex align-items-center ">
              <h6>
                <br />
                {t('modal.removeFlorestaSubTitle') + show.titulo + '?'}
                <br />
                {t('modal.removeProductDescription')}
              </h6>
            </Col>
          </Row>
        </Container>

        <Row className="mt-4">
          <Col className="d-flex align-items-center justify-content-end gap-2 p-4">
            <CustomButton
              cy="btn-cancel"
              type="button"
              variant="outline-green"
              onClick={() => {
                setShow(false);
              }}
            >
              {t('buttons.cancel')}
            </CustomButton>
            <CustomButton cy="btn-save" type="button" variant="danger" onClick={() => handleDeleteDocument(show.idDoc)}>
              {t('buttons.delete')}
            </CustomButton>
          </Col>
        </Row>
      </Modal>
      <CardContent className="space-y-6">
        {!idDocumento && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <label className="text-xl">
                {t('modal.selecioneArquivo')} <b>{'CAR - Cadastro Ambiental Rural'}</b>
                <br />
              </label>
            </div>

            <div
              className="border-2 border-dashed rounded-lg p-8 text-center"
              onDrop={handleDrop}
              onDragOver={handleDragOver}
            >
              <div className="flex flex-col items-center gap-2">
                <Upload className="h-10 w-10 text-muted-foreground" />
                <p>{t('labels.selecioneArquivo')}</p>
                <p className="text-sm text-muted-foreground">JPG, PNG or PDF. {t('exceptions.maxSizeDocment')}</p>
                <Button
                  onClick={() => document.getElementById(`file-upload${floresta.uuid}`)?.click()}
                  variant="outline-success"
                  className="mt-2"
                >
                  {t('buttons.selectFile')}
                </Button>
                <input
                  id={'file-upload' + floresta.uuid}
                  type="file"
                  className="hidden"
                  onChange={handleFileSelect}
                  accept=".jpg,.jpeg,.png,.pdf"
                  multiple
                />
              </div>
            </div>
          </div>
        )}
        {files.length > 0 && (
          <div className="space-y-4">
            <h3 className="font-bold text-lg">{t('labels.labelArquivoAdicionado')}</h3>
            <div className="border rounded-lg">
              <div
                className="grid grid-cols-12 border-b bg-muted/50"
                style={{ paddingTop: '10px', paddingBottom: '10px' }}
              >
                <div className="col-span-1" style={{ textAlign: 'center' }}>
                  {t('table.type')}
                </div>
                <div className="col-span-6">{t('table.name')}</div>
                <div className="col-span-2">{t('table.size')}</div>
                <div className="col-span-2">{t('table.vencimento')}</div>
                <div className="col-span-1">{t('modal.modalTitleActions')}</div>
              </div>
              {files.map((file, index) => (
                <div key={index} className="grid grid-cols-12 items-center">
                  <div className="col-span-1">
                    <FileIcon className="h-6 w-6" />
                  </div>
                  <div className="col-span-6 flex items-center gap-2">{getDocumentNameLimited(file.file.name)}</div>
                  <div className="col-span-2">{formatFileSize(file.file.size)}</div>
                  <div className="col-span-2" style={{ paddingTop: '10px' }}>
                    <DatePicker
                      onChange={(start) => {
                        setDataAtual(start);
                      }}
                      selected={dataAtual}
                      className="dt-picker"
                    />
                  </div>
                  <div className="col-span-1">
                    <Button variant="ghost" title={t('titles.removeDocument')} onClick={() => removeFile(index)}>
                      <Image src={InactiveUser} height={20} />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="d-flex justify-content-end">
          {(!idDocumento && (
            <Button variant="outline-secondary" onClick={handleSubmit}>
              {t('table.enviarDocumento')}
            </Button>
          )) || (
            <Button
              variant="outline-secondary"
              onClick={() => setShow({ show: true, idDoc: floresta.id, titulo: floresta.nome })}
            >
              {'Remover Floresta'}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
