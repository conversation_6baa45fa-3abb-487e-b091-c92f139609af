export const getStatusColor = (description: string): string => {
  switch (description) {
    case 'Under negotiation':
    case 'Em negociação':
      return 'cffff00';
    case 'Order created':
    case 'Pedido criado':
      return 'derf987';
    case 'On production':
    case 'Em produção':
      return 'c0000ff';
    case 'Under production':
    case 'Aguardando embarque':
    case 'Awaiting shipment':
      return 'c1e90ff';
    case 'Collected from the mill':
    case 'Carga coletada na fabrica':
      return 'c87cefa';
    case 'Delivered at the port of origin':
    case 'Entregue no porto de embarque':
      return 'cadff2f';
    case 'Shipped':
    case 'Cargo shipped':
    case 'Carga embarcada':
    case 'Embarcado':
      return 'c00ff00';
    case 'In transit':
    case 'Em trânsito':
      return 'c4169e1';
    case 'Arrived at port of destination':
    case 'Chegada no porto de destino':
      return 'cffd700';
    case 'Moving to final destination':
    case 'Em transito para entrega no final':
      return 'cf0e68c';
    case 'Delivered at final destination':
    case 'Entregue no Porto de Destino':
    case 'Arrived at Port of Destination':
    case 'Entregue':
      return 'cadff2f';
    default:
      return '';
  }
};

export const getStatusQuoteColor = (description: string): string => {
  switch (description) {
    case 'Requested':
    case 'Pendente':
      return 'ffe16f';
    case 'Pending Quote':
    case 'Em negociação':
      return 'c6fe5ff';
    case 'To Be Approved':
    case 'Aguardando aprovação':
      return 'c87cefa';
    case 'Approved':
    case 'Aprovada':
      return 'c6fffa9';
    case 'Declined':
    case 'Recusada':
      return 'ff6f6f';
    default:
      return '';
  }
};

export const getStatusVisible = (description: string): string => {
  switch (description) {
    case 'Visível':
    case 'Visible':
      return 'visible';
    case 'Occult':
    case 'Oculto':
      return 'occult';
    default:
      return '';
  }
};

export const getInspectColor = (description: string): string => {
  switch (description) {
    case 'Finished':
    case 'Finalizada':
      return 'realized';
    case 'in progress':
    case 'Em progresso':
      return 'notRealized';
    case 'Pending':
    case 'Pendente':
      return 'pending';
    default:
      return '';
  }
};

export const getStatusColorUser = (description: string): string => {
  switch (description) {
    case 'A':
      return 'ffe16f';
    case 'I':
      return 'c6fe5ff';
    default:
      return '';
  }
};

export const getStatusProfileColorUser = (id: string): string => {
  switch (id) {
    case '1':
      return 'ce3d3d';
    case '2':
      return 'fabb00';
    case '3':
      return 'ffe16f';
    default:
      return '';
  }
};

export const getStatusAccessColorUser = (description: string): string => {
  switch (description) {
    case 'reproved':
      return 'f18b680';
    case 'approved':
      return 'fabb00';
    default:
      return 'f18b680';
  }
};
