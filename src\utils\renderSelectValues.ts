import { ISelectOption } from '../interfaces';

/**
 * @description
 * Retorna a lista de selects.
 * Exemplo de uso:
 * renderSelectValues(value, ISelectOption[]);
 * @param {String} value
 * @param {Array} options
 * @return {Array}
 */

const renderSelectValues = async (value: string, options: ISelectOption[]): Promise<ISelectOption[]> => {
  if (!options) return [];

  const optionSelected = options.filter((option) => option?.label?.toLowerCase().includes(value.toLowerCase()));
  return optionSelected;
};

export default renderSelectValues;
