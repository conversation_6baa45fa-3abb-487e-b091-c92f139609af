import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../translate/i18n';
import QuantityInput from '../QuantityInput';

const renderWithI18n = (component: React.ReactElement) => {
  return render(<I18nextProvider i18n={i18n}>{component}</I18nextProvider>);
};

describe('QuantityInput', () => {
  const defaultProps = {
    label: 'Test Quantity',
    value: '',
    onChange: jest.fn(),
    cy: 'test-quantity',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    renderWithI18n(<QuantityInput {...defaultProps} />);

    expect(screen.getByLabelText('Test Quantity')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('should display initial value', () => {
    renderWithI18n(<QuantityInput {...defaultProps} value="1,500" />);

    expect(screen.getByDisplayValue('1,500')).toBeInTheDocument();
  });

  it('should call onChange when value changes', () => {
    const mockOnChange = jest.fn();
    renderWithI18n(<QuantityInput {...defaultProps} onChange={mockOnChange} />);

    const input = screen.getByLabelText('Test Quantity');
    fireEvent.change(input, { target: { value: '1,000' } });

    expect(mockOnChange).toHaveBeenCalledWith('1,000');
  });

  it('should be disabled when disabled prop is true', () => {
    renderWithI18n(<QuantityInput {...defaultProps} disabled={true} />);

    const input = screen.getByLabelText('Test Quantity');
    expect(input).toBeDisabled();
  });

  it('should show placeholder when provided', () => {
    const placeholder = 'Enter quantity';
    renderWithI18n(<QuantityInput {...defaultProps} placeholder={placeholder} />);

    const input = screen.getByLabelText('Test Quantity');
    expect(input).toHaveAttribute('placeholder', placeholder);
  });

  it('should have correct data-cy attribute', () => {
    const cy = 'custom-test-id';
    renderWithI18n(<QuantityInput {...defaultProps} cy={cy} />);

    const input = screen.getByLabelText('Test Quantity');
    expect(input).toHaveAttribute('data-cy', cy);
  });

  it('should apply correct CSS classes', () => {
    renderWithI18n(<QuantityInput {...defaultProps} />);

    const input = screen.getByLabelText('Test Quantity');
    expect(input).toHaveClass('form-control');
  });

  it('should handle numeric input with decimal mask', () => {
    const mockOnChange = jest.fn();
    renderWithI18n(<QuantityInput {...defaultProps} onChange={mockOnChange} />);

    const input = screen.getByLabelText('Test Quantity');
    
    // Simular entrada de números
    fireEvent.change(input, { target: { value: '1234567' } });
    
    // Verificar se onChange foi chamado
    expect(mockOnChange).toHaveBeenCalled();
  });
});
